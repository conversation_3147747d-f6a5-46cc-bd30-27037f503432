import simd
import Swift<PERSON>

@MainActor
private let logger = Logger.create()

@MainActor
struct PointOfInterestView: View {
    private enum FocusedField {
        case text
    }

    @Environment(\.dismiss) private var dismiss: DismissAction

    @Binding var isPhotoModeEnabled: Bo<PERSON>
    @Binding var webViewModel: WebViewModel

    let env: CapturedStructureScannerEnvironment
    let capturedStructureScannerHandler: CapturedStructureScannerHandler
    let pointOfInterestId: String

    var body: some View {
        VStack(alignment: .center, spacing: .zero) {
            ZStack {
                Text("pointOfInterest.title")
                    .font(.title3)

                HStack {
                    Spacer()

                    Button {
                        capturedStructureScannerHandler.deletePointOfInterest(id: pointOfInterestId)
                        dismiss()
                    } label: {
                        Image(systemName: "trash")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 20, height: 20)
                            .foregroundStyle(.colorIconError)
                            .accessibilityLabel("pointOfInterest.deleteButton")
                    }
                }
            }
            .padding(.horizontal)
            .padding(.top)
            .background(Color.colorBackgroundDefault)

            ThemeBackground {
                WebView(
                    webViewModel: $webViewModel,
                    showNavigationButtons: false,
                    showProgress: false
                )
            }

            Color
                .colorOutlineDefault
                .frame(height: 1)

            Button {
                dismiss()
            } label: {
                Text("pointOfInterest.doneButton")
                    .padding(.horizontal)
                    .padding(.vertical, 8)
                    .tint(.colorOnBackgroundPrimary)
            }
            .bold()
            .font(.title3)
            .buttonStyle(.borderedProminent)
            .tint(.colorBackgroundPrimary)
            .padding()
        }
        .onAppear {
            webViewModel.pointOfInterestCustomDataSupplier = { pointOfInterestId in
                if self.pointOfInterestId != pointOfInterestId {
                    logger.e("Requested custom data for wrong poi id: \(pointOfInterestId)", env: env)
                    throw WebViewModel.PointOfInterestDataError.invalidPointOfInterestId
                }
                return capturedStructureScannerHandler.pointOfInterestCustomData(forPointOfInterestId: pointOfInterestId)
            }
            webViewModel.pointOfInterestCustomDataConsumer = { pointOfInterestId, newPointOfInterestCustomData in
                if self.pointOfInterestId != pointOfInterestId {
                    logger.e("Received custom data for wrong poi id: \(pointOfInterestId)", env: env)
                    return
                }
                capturedStructureScannerHandler.setPointOfInterestCustomData(forPointOfInterestId: pointOfInterestId, pointOfInterestCustomData: newPointOfInterestCustomData, shouldRecalculateStructure: false)
            }
            webViewModel.pointOfInterestDataConfig = WebViewModel.PointOfInterestDataConfig(
                listingId: env.listingId,
                customUIElementId: env.customUIElementId,
                pointOfInterestId: pointOfInterestId,
                env: env
            )
        }
        .onDisappear {
            webViewModel.pointOfInterestCustomDataSupplier = nil
            webViewModel.pointOfInterestCustomDataConsumer = nil
            webViewModel.roomDataConfig = nil
            capturedStructureScannerHandler.recalculateStructure()
        }
        .presentationDetents([.height(500), .large])
    }
}

#if DEBUG
#Preview {
    ThemeBackground {
    }
    .sheet(isPresented: .constant(true)) {
        PointOfInterestView(
            isPhotoModeEnabled: .constant(false),
            webViewModel: .constant(WebViewModel(initialURL: URL(string: "https://integ.doorbit.com/nativeApp/pointOfInterestData/?useNativeAppCommunication=true&hideNativeBottomNavigationFast=true&isBuildingScanner=true&isIOS=true&hasLIDAR=true"))),
            env: CapturedStructureScannerEnvironment(
                listingBuildingToken: "2af89045bb44c0fe9d68b9d6266756e1c7437bd3dc8840e0476b412abd3eb498",
                listingId: "60756464468",
                customUIElementId: "model-edit"
            ),
            capturedStructureScannerHandler: CapturedStructureScannerHandler(),
            pointOfInterestId: "pointOfInterestId"
        )
    }
}
#endif
