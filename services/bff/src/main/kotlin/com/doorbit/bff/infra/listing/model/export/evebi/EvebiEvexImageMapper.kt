package com.doorbit.bff.infra.listing.model.export.evebi

import com.doorbit.bff.infra.listing.model.export.evebi.EvebiXmlHelper.createGuid
import com.doorbit.bff.infra.listing.model.export.evebi.model.TBildStream
import com.doorbit.bff.infra.listing.model.export.evebi.model.TBilderStreamListe
import com.doorbit.bff.infra.listing.model.export.evebi.model.TEveImage
import com.doorbit.bff.infra.listing.model.listing.Image
import java.time.ZonedDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter

object EvebiEvexImageMapper {

    fun createBilderStreamsListe(images: List<Image>): TBilderStreamListe {

        return TBilderStreamListe().apply {
            guid = createGuid()
            item = createEvexImages(images)
        }

    }

    private fun createEvexImages(images: List<Image>): List<TEveImage> {
        return images.map { createEveImageSingle(it.imageInfo?.caption, it.id) }
    }

    fun createEveImageSingle(caption: String?, id: String) : TEveImage {
        return TEveImage().apply {
            guid = createGuid()
            stream = createStream(id)
            titel = caption ?: ""
            beschreibung = ""
            name = ""
            quelle = "Energieberater"
            aufnahmedatum = ZonedDateTime.now(ZoneId.of("Europe/Berlin")).format(DateTimeFormatter.ofPattern("dd.MM.yyyy"))
        }
    }

    private fun createStream(id: String): TBildStream {
        return TBildStream().apply {
            contentDisposition = "attachment"
            contentDispositionFilename = "blob_{$id}.jpg"
        }
    }
}