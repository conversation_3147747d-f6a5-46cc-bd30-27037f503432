package com.doorbit.bff.infra.listing.adapter.fileuploads

import com.doorbit.bff.infra.listing.model.fileupload.FileStorageAdapter
import com.doorbit.bff.core.domain.extension.WithLogger
import com.doorbit.bff.core.domain.extension.t
import com.google.cloud.storage.Storage
import org.springframework.core.io.Resource
import org.springframework.core.io.WritableResource
import java.net.URL

class GcpCloudStorageAdapter(
    private val storage: Storage,
    private var bucketName: String = ""
) : FileStorageAdapter {

    override fun getWritableResource(fullFileName: String): GoogleCloudStorageResource {
        return GoogleCloudStorageResource(storage, "gs://$bucketName/$fullFileName")
    }

    override fun getFileContents(fullFileName: String): ByteArray {
        getWritableResource(fullFileName).inputStream.use {
            return it.readAllBytes()
        }
    }

    override fun deleteFile(fullFileName: String) {
        getWritableResource(fullFileName).blob?.delete()
    }

    override fun copyFile(srcfullFileName: String, targetFullFileName: String) {
        val src = getWritableResource(srcfullFileName)
        val target = getWritableResource(targetFullFileName)

        src.inputStream.use { srcStream ->
            target.outputStream.use { targetStream ->
                srcStream.copyTo(targetStream)
            }
        }

        LOGGER.t { "Copied file from $srcfullFileName to $targetFullFileName"}
    }


    override fun setContentType(resource: WritableResource, contentType: String) {
        // Set content type of created file

        (resource as GoogleCloudStorageResource).blob!!.toBuilder()
            .setContentType(contentType)
            .build().update()
    }

    override fun setCacheControl(resource: WritableResource, value: String) {
        (resource as GoogleCloudStorageResource).blob!!.toBuilder()
            .setCacheControl(value)
            .build().update()
    }


    override fun getURL(resource: Resource): URL = URL("https", resource.uri.host, resource.uri.path)

    companion object : WithLogger()

}