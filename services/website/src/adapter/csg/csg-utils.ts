import {Brush, Evaluator, SUBTRACTION} from "three-bvh-csg";
import {BufferGeometry, Mesh} from "three";
import {Optional} from "@/model/Optional";
import {tDestroyMesh} from "@/adapter/three/three-utility";

// noinspection FunctionNamingConventionJS
export function csgCreateBrush_000_GM(mesh: Mesh): Optional<Brush> {
    //Brushes funktionieren nur mit Geometrien, die mindestens einen Punkt haben
    if (mesh.geometry.attributes.position.count <= 0) {
        return null
    }

    mesh.updateMatrixWorld()

    const geometry = mesh.geometry.clone()
    const transformedGeometry = geometry.applyMatrix4(mesh.matrixWorld)

    const brush = new Brush(transformedGeometry)
    brush.updateMatrixWorld()

    return brush
}

export function csgCreateEvaluator(): Evaluator {
    const evaluator = new Evaluator()
    evaluator.attributes = ["position", "normal"]; //"uv" is only for texture materials. we don't use them
    evaluator.useGroups = false
    evaluator.consolidateMaterials = true
    return evaluator
}

// noinspection FunctionNamingConventionJS
export function csgGenerateGeometryWithHoles_000(parentMesh: Mesh, holeMeshes: readonly Mesh[]): BufferGeometry {
    if (holeMeshes.length <= 0) {
        return parentMesh.geometry.clone()
    }

    // noinspection LocalVariableNamingConventionJS
    let parentBrush_000_GM = csgCreateBrush_000_GM(parentMesh)
    if (parentBrush_000_GM === null) {
        return parentMesh.geometry.clone()
    }

    const evaluator = csgCreateEvaluator()
    for (const holeMesh of holeMeshes) {
        // noinspection LocalVariableNamingConventionJS
        const holeBrush_000_GM = csgCreateBrush_000_GM(holeMesh)
        if (holeBrush_000_GM === null) {
            continue
        }

        // noinspection LocalVariableNamingConventionJS
        const oldParentBrush_000_GM = parentBrush_000_GM

        try {
            parentBrush_000_GM = evaluator.evaluate(parentBrush_000_GM, holeBrush_000_GM, SUBTRACTION)
            tDestroyMesh(oldParentBrush_000_GM, true, true)
        } catch (e) {
            console.error("Error while subtracting hole from parent", e)
        }
    }

    // noinspection LocalVariableNamingConventionJS
    const geometry_000 = parentBrush_000_GM.geometry.clone()
    geometry_000.applyMatrix4(parentMesh.matrixWorld.clone().invert())

    tDestroyMesh(parentBrush_000_GM, true, true)

    return geometry_000
}