<template>
    <v-badge :class="{
        noPointer,
        smallPadding,
        nested,
        disabled,
        invisible,
        'type-default': type === 'default',
        'type-surface': type === 'surface',
        'type-primary': type === 'primary',
        'type-secondary': type === 'secondary',
        'type-tertiary': type === 'tertiary',
        'type-success': type === 'success',
        'type-warning': type === 'warning',
        'type-error': type === 'error',
        'type-info': type === 'info',
    }"
             class="dBadge">
        <template v-if="$slots.badge"
                  #badge>
            <slot name="badge"/>
        </template>

        <slot/>
    </v-badge>
</template>

<script lang="ts"
        setup>
    defineProps<{
        noPointer?: boolean
        nested?: boolean
        smallPadding?: boolean
        disabled?: boolean
        invisible?: boolean
        type: 'default' | 'surface' | 'primary' | 'secondary' | 'tertiary' | 'success' | 'warning' | 'error' | 'info'
    }>()
</script>

<style scoped>
    .dBadge.nested :deep(.v-badge__wrapper > :not(.dBadge) + .v-badge__badge),
    .dBadge:not(.nested) :deep(.v-badge__badge) {
        color: rgb(var(--v-theme-d-text-default));
        border: 1px solid rgb(var(--v-theme-d-outline-default));
    }

    .dBadge.type-default.nested :deep(.v-badge__wrapper > :not(.dBadge) + .v-badge__badge),
    .dBadge.type-default:not(.nested) :deep(.v-badge__badge) {
        background-color: rgb(var(--v-theme-d-background-default));
    }

    .dBadge.type-surface.nested :deep(.v-badge__wrapper > :not(.dBadge) + .v-badge__badge),
    .dBadge.type-surface:not(.nested) :deep(.v-badge__badge) {
        background-color: rgb(var(--v-theme-d-background-surface));
    }

    .dBadge.type-primary.nested :deep(.v-badge__wrapper > :not(.dBadge) + .v-badge__badge),
    .dBadge.type-primary:not(.nested) :deep(.v-badge__badge) {
        background-color: rgb(var(--v-theme-d-background-primary));
        color: rgb(var(--v-theme-d-on-background-primary));
    }

    .dBadge.type-secondary.nested :deep(.v-badge__wrapper > :not(.dBadge) + .v-badge__badge),
    .dBadge.type-secondary:not(.nested) :deep(.v-badge__badge) {
        background-color: rgb(var(--v-theme-d-background-secondary));
        color: rgb(var(--v-theme-d-on-background-secondary));
    }

    .dBadge.type-tertiary.nested :deep(.v-badge__wrapper > :not(.dBadge) + .v-badge__badge),
    .dBadge.type-tertiary:not(.nested) :deep(.v-badge__badge) {
        background-color: rgb(var(--v-theme-d-background-tertiary));
        color: rgb(var(--v-theme-d-on-background-tertiary));
    }

    .dBadge.type-success.nested :deep(.v-badge__wrapper > :not(.dBadge) + .v-badge__badge),
    .dBadge.type-success:not(.nested) :deep(.v-badge__badge) {
        background-color: rgb(var(--v-theme-d-background-success));
        border-color: rgb(var(--v-theme-d-outline-success));
        color: rgb(var(--v-theme-d-text-success));
    }

    .dBadge.type-warning.nested :deep(.v-badge__wrapper > :not(.dBadge) + .v-badge__badge),
    .dBadge.type-warning:not(.nested) :deep(.v-badge__badge) {
        background-color: rgb(var(--v-theme-d-background-warning));
        border-color: rgb(var(--v-theme-d-outline-warning));
        color: rgb(var(--v-theme-d-text-warning));
    }

    .dBadge.type-error.nested :deep(.v-badge__wrapper > :not(.dBadge) + .v-badge__badge),
    .dBadge.type-error:not(.nested) :deep(.v-badge__badge) {
        background-color: rgb(var(--v-theme-d-background-error));
        border-color: rgb(var(--v-theme-d-outline-error));
        color: rgb(var(--v-theme-d-text-error));
    }

    .dBadge.type-info.nested :deep(.v-badge__wrapper > :not(.dBadge) + .v-badge__badge),
    .dBadge.type-info:not(.nested) :deep(.v-badge__badge) {
        background-color: rgb(var(--v-theme-d-background-info));
        border-color: rgb(var(--v-theme-d-outline-info));
        color: rgb(var(--v-theme-d-text-info));
    }

    .dBadge.noPointer.nested :deep(.v-badge__wrapper > :not(.dBadge) + .v-badge__badge),
    .dBadge.noPointer:not(.nested) :deep(.v-badge__badge) {
        pointer-events: none;
    }

    .dBadge.disabled.nested :deep(.v-badge__wrapper > :not(.dBadge) + .v-badge__badge),
    .dBadge.disabled:not(.nested) :deep(.v-badge__badge) {
        opacity: 0.26;
    }

    .dBadge.smallPadding.nested :deep(.v-badge__wrapper > :not(.dBadge) + .v-badge__badge),
    .dBadge.smallPadding:not(.nested) :deep(.v-badge__badge) {
        padding: 1px 3px !important;
        min-width: 0;
        height: auto;
    }

    .dBadge.invisible.nested :deep(.v-badge__wrapper > :not(.dBadge) + .v-badge__badge),
    .dBadge.invisible:not(.nested) :deep(.v-badge__badge) {
        display: none;
    }
</style>