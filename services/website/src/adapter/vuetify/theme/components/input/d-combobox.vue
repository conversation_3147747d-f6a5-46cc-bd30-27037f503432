<template>
    <v-combobox v-model="value"
                :autofocus="autofocus"
                :chips="chips"
                :class="{required: required === null ? undefined : required}"
                :closable-chips="closableChips"
                :item-title="itemTitle"
                :item-value="itemValue"
                :items="items"
                :label="label"
                :loading="loading"
                :multiple="multiple"
                :placeholder="placeholder"
                :return-object="returnObject"
                :rules="validationRules"
                auto-select-first="exact"
                density="compact"
                hide-details="auto"
                hide-no-data
                hide-spin-buttons
                rounded="xl"
                variant="outlined"
                @update:search="onSearchTriggered"/>
</template>

<script generic="T"
        lang="ts"
        setup>
    import {computed, toRef} from "vue";
    import {Optional} from "@/model/Optional";
    import {isString} from "@/utility/converter";
    import {useValidation, VuetifyValidationRule} from "@/adapter/vuetify/theme/components/input/validation-rules";

    const props = defineProps<{
        modelValue: Optional<T>
        items: T[]
        itemTitle?: string //"keyof T" wäre besser, aber das geht nicht mit apolloQueryResults
        itemValue?: string //"keyof T" wäre besser, aber das geht nicht mit apolloQueryResults
        searchTerm?: string
        autofocus?: boolean
        chips?: boolean
        closableChips?: boolean
        multiple?: boolean
        loading?: boolean
        returnObject?: boolean
        label?: string
        placeholder?: string
        rules?: readonly VuetifyValidationRule[]
        required?: Optional<boolean>
    }>()

    const emits = defineEmits<{
        'update:modelValue': [value: Optional<T>]
        'onSearch': [searchTerm: string]
    }>()

    const {
        validationRuleRequired,
    } = useValidation(toRef(() => props.required ?? false))

    const validationRules = computed<VuetifyValidationRule[] | undefined>(() => {
        const rules: VuetifyValidationRule[] = props.rules === undefined ? [] : [...props.rules]

        rules.push(validationRuleRequired)

        return rules.length > 0 ? rules : undefined
    })

    //TODO: derzeit ist der support für "string" values nicht möglich
    const value = computed<Optional<T> | undefined>({
        get() {
            return props.modelValue ?? undefined
        },
        set(newValue) {
            if (newValue === undefined || newValue === null || isString(newValue)) {
                return
            }
            emits('update:modelValue', newValue)
        }
    })

    function onSearchTriggered(searchTerm: string) {
        emits('onSearch', searchTerm)
    }
</script>

<style scoped>

</style>