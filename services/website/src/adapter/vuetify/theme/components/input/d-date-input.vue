<template>
    <!--    <v-menu v-model="isShowMenu"-->
    <!--            :close-on-content-click="false">-->
    <!--        <template #activator="{props: activatorProps}">-->
    <!--            <d-text-field :append-inner-icon="mdiCalendar"-->
    <!--                          :hint="hint ?? undefined"-->
    <!--                          :label="label ?? undefined"-->
    <!--                          :model-value="inputValue ? d(inputValue, 'date') : undefined"-->
    <!--                          :persistent-hint="persistentHint"-->
    <!--                          :required="required"-->
    <!--                          clearable-->
    <!--                          readonly-->
    <!--                          v-bind="activatorProps"-->
    <!--                          @clear="onClear"/>-->
    <!--        </template>-->
    <!--        <v-date-picker v-model="inputValue"-->
    <!--                       hide-header/>-->
    <!--    </v-menu>-->
    <div/>
</template>

<!--<script lang="ts"-->
<!--        setup>-->
<!--    import DTextField from "@/adapter/vuetify/theme/components/input/d-text-field.vue";-->
<!--    import {Optional} from "@/model/Optional";-->
<!--    import dayjs, {Dayjs} from "dayjs";-->
<!--    import {computed, shallowRef} from "vue";-->
<!--    import {useI18n} from "vue-i18n";-->
<!--    import {mdiCalendar} from "@mdi/js";-->

<!--    const {d} = useI18n()-->

<!--    const props = defineProps<{-->
<!--        modelValue: Optional<Dayjs>-->
<!--        required?: boolean-->
<!--        label?: Optional<string>-->
<!--        hint?: Optional<string>-->
<!--        persistentHint?: boolean-->
<!--    }>()-->

<!--    const emits = defineEmits<{-->
<!--        'update:modelValue': [value: Optional<Dayjs>]-->
<!--    }>()-->

<!--    const isShowMenu = shallowRef<boolean>(false)-->

<!--    const inputValue = computed<Date | undefined>({-->
<!--        get() {-->
<!--            return props.modelValue?.toDate() ?? undefined-->
<!--        },-->
<!--        set(value: Date | undefined) {-->
<!--            isShowMenu.value = false-->
<!--            if (value) {-->
<!--                emits('update:modelValue', dayjs(value))-->
<!--            } else {-->
<!--                emits('update:modelValue', null)-->
<!--            }-->
<!--        }-->
<!--    })-->

<!--    function onClear() {-->
<!--        inputValue.value = undefined-->
<!--    }-->
<!--</script>-->

<!--<style scoped>-->

<!--</style>-->