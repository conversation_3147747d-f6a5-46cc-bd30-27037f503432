<template>
    <v-textarea v-model="value"
                v-model:focused="isFocused"
                :auto-grow="autoGrow"
                :base-color="baseColor"
                :bg-color="backgroundColor"
                :class="{required: required === null ? undefined : required}"
                :color="color"
                :counter="showCharacterCounter ? lengthMax! : undefined"
                :label="label ?? undefined"
                :persistent-counter="showCharacterCounter"
                :readonly="readonly"
                :rows="rows"
                :rules="validationRules"
                :variant="readonly ? 'filled' : 'outlined'"
                data-lpignore="true"
                density="compact"
                hide-details="auto"
                hide-spin-buttons
                rounded="xl">
        <template v-if="showCharacterCounter"
                  #counter="{value: counter, max: counterMaximum}">
            <d-input-counter :counter="counter"
                             :counter-maximum="counterMaximum"/>
        </template>

        <template #append-inner>
            <d-input-unit v-if="unit"
                          :unit="unit"/>
            <slot name="appendInner"/>
        </template>
    </v-textarea>
</template>

<script lang="ts"
        setup>
    import {Optional} from "@/model/Optional";
    import {computed, shallowRef, toRef, watch} from "vue";
    import {useDoorbitTheme} from "@/adapter/vuetify/theme/doorbit-theme-provider";
    import {useValidation, VuetifyValidationRule} from "@/adapter/vuetify/theme/components/input/validation-rules";
    import DInputCounter from "@/adapter/vuetify/theme/components/input/d-input-counter.vue";
    import DInputUnit from "@/adapter/vuetify/theme/components/input/d-input-unit.vue";
    import {ListingFieldUnit} from "@/model/listing/ListingFieldUnit";

    const {themeLayer} = useDoorbitTheme()

    const props = defineProps<{
        label?: Optional<string>
        rules?: readonly VuetifyValidationRule[]
        lengthMin?: Optional<number>
        lengthMax?: Optional<number>
        required?: Optional<boolean>
        readonly?: boolean
        showCharacterCounter?: boolean
        autoGrow?: boolean
        unit?: ListingFieldUnit
        rows?: number
        preventWriteOnFocus?: boolean
    }>()

    const modelValue = defineModel<Optional<any>>("modelValue")

    const {
        validationRuleRequired,
        validationRuleLengthMin,
        validationRuleLengthMax
    } = useValidation(toRef(() => props.required ?? false))

    const validationRules = computed<VuetifyValidationRule[] | undefined>(() => {
        const rules: VuetifyValidationRule[] = props.rules === undefined ? [] : [...props.rules]

        rules.push(validationRuleRequired)

        const lengthMin = props.lengthMin;
        if (lengthMin !== undefined && lengthMin !== null) {
            rules.push(validationRuleLengthMin(lengthMin))
        }

        const lengthMax = props.lengthMax;
        if (lengthMax !== undefined && lengthMax !== null) {
            rules.push(validationRuleLengthMax(lengthMax))
        }

        return rules.length > 0 ? rules : undefined
    })


    const value = shallowRef<any | undefined>(undefined)
    const isFocused = shallowRef<boolean>(false)

    watch(value, value => {
        if (value === undefined || value === "") {
            modelValue.value = null
        } else {
            modelValue.value = value
        }
    })

    watch(modelValue, modelValue => {
        if (isFocused.value && props.preventWriteOnFocus) {
            return
        }
        value.value = modelValue ?? undefined
    }, {
        immediate: true
    })

    //border + label (focused)
    const color = computed<string | undefined>(() => {
        switch (themeLayer) {
            case 'surface':
            case 'background':
                return 'rgb(var(--v-theme-d-text-title))'
            default:
                return undefined
        }
    })

    //border + label (default)
    const baseColor = computed<string | undefined>(() => {
        switch (themeLayer) {
            case 'surface':
            case 'background':
                return 'rgb(var(--v-theme-d-text-default))'
            default:
                return undefined
        }
    })

    const backgroundColor = computed<string | undefined>(() => {
        switch (themeLayer) {
            case 'surface':
                return 'rgb(var(--v-theme-d-background-default))'
            case 'background':
                return 'rgb(var(--v-theme-d-background-surface))'
            default:
                return undefined
        }
    })
</script>

<style scoped>
    :deep(.v-field--variant-filled .v-field__outline) {
        display: none;
    }
</style>