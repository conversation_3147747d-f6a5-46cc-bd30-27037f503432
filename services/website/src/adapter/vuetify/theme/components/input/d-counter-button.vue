<template>
    <d-text-field :label="label"
                  :model-value="modelValue"
                  center-input-text
                  no-append-padding
                  no-prepend-padding
                  readonly
                  style="width: 110px;"
                  type="number">
        <template #prependInner>
            <d-btn :disabled="!canDecrease"
                   :icon="mdiMinus"
                   size="small"
                   type="tertiary"
                   variant="text"
                   @click="decrease"/>
        </template>

        <template #appendInner>
            <d-btn :disabled="!canIncrease"
                   :icon="mdiPlus"
                   size="small"
                   type="tertiary"
                   variant="text"
                   @click="increase"/>
        </template>
    </d-text-field>
</template>

<script lang="ts"
        setup>
    import {mdiMinus, mdiPlus} from "@mdi/js";
    import DTextField from "@/adapter/vuetify/theme/components/input/d-text-field.vue";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import {computed} from "vue";

    // TODO: <<<<<<<<<<<<<< UNGETESTET >>>>>>>>>>>>>>>>>>

    const props = defineProps<{
        min: number
        max: number
        label?: string
    }>()

    const modelValue = defineModel<number>("modelValue", {
        required: true,
    })

    const canDecrease = computed<boolean>(() => modelValue.value > props.min)
    const canIncrease = computed<boolean>(() => modelValue.value < props.max)

    function decrease() {
        if (canDecrease.value) {
            --modelValue.value
        }
    }

    function increase() {
        if (canIncrease.value) {
            ++modelValue.value
        }
    }
</script>

<style scoped>
</style>