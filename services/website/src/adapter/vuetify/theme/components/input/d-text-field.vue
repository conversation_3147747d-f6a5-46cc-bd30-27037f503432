<template>
    <v-text-field v-model="value"
                  v-model:focused="isFocused"
                  :append-inner-icon="appendInnerIcon"
                  :autofocus="autofocus"
                  :base-color="baseColor"
                  :bg-color="backgroundColor"
                  :class="{
                      required: required === null ? undefined : required,
                      centerInputText: centerInputText,
                      endInputText: endInputText,
                      noPrependPadding: noPrependPadding,
                      noAppendPadding: noAppendPadding,
                      smallPrependInnerIcon: smallPrependInnerIcon,
                      smallAppendInnerIcon: smallAppendInnerIcon,
                      small: small,
                  }"
                  :clearable="clearable"
                  :color="color"
                  :counter="showCharacterCounter ? lengthMax! : undefined"
                  :hint="hint ?? undefined"
                  :label="label ?? undefined"
                  :loading="loading"
                  :persistent-counter="showCharacterCounter"
                  :persistent-hint="persistentHint"
                  :placeholder="placeholder"
                  :prepend-icon="prependIcon"
                  :prepend-inner-icon="prependInnerIcon"
                  :readonly="readonly"
                  :rules="validationRules"
                  :step="step"
                  :type="type"
                  :variant="readonly && !hideReadonly ? 'filled' : 'outlined'"
                  aria-autocomplete="none"
                  autocapitalize="off"
                  autocomplete="off"
                  autocorrect="off"
                  data-lpignore="true"
                  density="compact"
                  hide-details="auto"
                  hide-spin-buttons
                  rounded="xl"
                  spellcheck="false"
                  @click:clear="onClear">
        <template v-if="$slots.prependInner"
                  #prepend-inner>
            <slot name="prependInner"/>
        </template>
        <template #append-inner>
            <d-input-unit v-if="unit"
                          :unit="unit"/>
            <slot name="appendInner"/>
        </template>
        <template v-if="showCharacterCounter"
                  #counter="{value: counter, max: counterMaximum}">
            <d-input-counter :counter="counter"
                             :counter-maximum="counterMaximum"/>
        </template>
        <template v-if="$slots.append"
                  #append>
            <slot name="append"/>
        </template>
        <template v-if="$slots.prepend"
                  #prepend>
            <slot name="prepend"/>
        </template>
    </v-text-field>
</template>

<script generic="T extends number | string"
        lang="ts"
        setup>
    import {Optional} from "@/model/Optional";
    import {computed, shallowRef, toRef, watch} from "vue";
    import {useDoorbitTheme} from "@/adapter/vuetify/theme/doorbit-theme-provider";
    import {useValidation, VuetifyValidationRule} from "@/adapter/vuetify/theme/components/input/validation-rules";
    import DInputCounter from "@/adapter/vuetify/theme/components/input/d-input-counter.vue";
    import {ListingFieldUnit} from "@/model/listing/ListingFieldUnit";
    import DInputUnit from "@/adapter/vuetify/theme/components/input/d-input-unit.vue";
    import {isNumber, isString, stringToDouble} from "@/utility/converter";

    const {themeLayer} = useDoorbitTheme()

    const props = defineProps<{
        label?: Optional<string>
        rules?: readonly VuetifyValidationRule[]
        lengthMin?: Optional<number>
        lengthMax?: Optional<number>
        loading?: boolean
        required?: Optional<boolean>
        centerInputText?: boolean
        endInputText?: boolean
        showCharacterCounter?: boolean
        autofocus?: boolean
        placeholder?: string
        clearable?: boolean
        appendInnerIcon?: string
        step?: number
        type?: "number" | "tel" | "email" | "url"
        bgColor?: string
        noPrependPadding?: boolean
        noAppendPadding?: boolean
        unit?: ListingFieldUnit
        readonly?: boolean
        valueMin?: Optional<number>
        valueMax?: Optional<number>
        autocomplete?: string
        hint?: Optional<string>
        persistentHint?: boolean
        prependInnerIcon?: string
        prependIcon?: string
        preventWriteOnFocus?: boolean
        smallPrependInnerIcon?: boolean
        smallAppendInnerIcon?: boolean
        hideReadonly?: boolean
        small?: boolean
    }>()

    const emits = defineEmits<{
        'clear': []
    }>()

    const modelValue = defineModel<Optional<any>>("modelValue")

    const {
        validationRuleRequired,
        validationRuleStep,
        validationRuleLengthMin,
        validationRuleLengthMax,
        validationRuleMin,
        validationRuleMax
    } = useValidation(toRef(() => props.required ?? false))

    const validationRules = computed<VuetifyValidationRule[] | undefined>(() => {
        const rules: VuetifyValidationRule[] = props.rules === undefined ? [] : [...props.rules]

        rules.push(validationRuleRequired)

        if (props.type === 'number' && props.step !== undefined && value.value !== undefined && (!isString(value.value) || value.value !== "")) {
            rules.push(validationRuleStep(props.step))
        }

        const lengthMin = props.lengthMin;
        if (lengthMin !== undefined && lengthMin !== null && value.value !== undefined && (!isString(value.value) || value.value !== "")) {
            rules.push(validationRuleLengthMin(lengthMin))
        }

        const lengthMax = props.lengthMax;
        if (lengthMax !== undefined && lengthMax !== null && value.value !== undefined && (!isString(value.value) || value.value !== "")) {
            rules.push(validationRuleLengthMax(lengthMax))
        }

        const valueMin = props.valueMin;
        if (valueMin !== undefined && valueMin !== null && value.value !== undefined && (!isString(value.value) || value.value !== "")) {
            rules.push(validationRuleMin(valueMin))
        }

        const valueMax = props.valueMax;
        if (valueMax !== undefined && valueMax !== null && value.value !== undefined && (!isString(value.value) || value.value !== "")) {
            rules.push(validationRuleMax(valueMax))
        }

        return rules.length > 0 ? rules : undefined
    })

    const value = shallowRef<any | undefined>(undefined)
    const isFocused = shallowRef<boolean>(false)

    watch(value, value => {
        if (value === undefined || value === "") {
            modelValue.value = null
        } else {
            modelValue.value = value
        }
    })

    watch(modelValue, modelValue => {
        if (isFocused.value && props.preventWriteOnFocus) {
            return
        }

        if (props.type === 'number') {
            if (isString(modelValue)) {
                const newValue = stringToDouble(modelValue)
                value.value = newValue === null ? undefined : newValue
                return
            }

            if (isNumber(modelValue)) {
                value.value = modelValue
                return
            }

            value.value = undefined
            return
        }
        value.value = modelValue ?? undefined
    }, {
        immediate: true
    })

    //border + label (focused)
    const color = computed<string | undefined>(() => {
        switch (themeLayer) {
            case 'surface':
            case 'background':
                return 'rgb(var(--v-theme-d-text-title))'
            default:
                return undefined
        }
    })

    //border + label (default)
    const baseColor = computed<string | undefined>(() => {
        switch (themeLayer) {
            case 'surface':
            case 'background':
                return 'rgb(var(--v-theme-d-text-default))'
            default:
                return undefined
        }
    })

    const backgroundColor = computed<string | undefined>(() => {
        if (props.bgColor !== undefined) {
            return props.bgColor
        }
        switch (themeLayer) {
            case 'surface':
                return 'rgb(var(--v-theme-d-background-default))'
            case 'background':
                return 'rgb(var(--v-theme-d-background-surface))'
            default:
                return undefined
        }
    })

    function onClear() {
        emits('clear')
    }
</script>

<style scoped>
    .centerInputText :deep(input) {
        text-align: center;
    }

    .endInputText :deep(input) {
        text-align: end;
    }

    .noPrependPadding :deep(.v-field--prepended) {
        padding-inline-start: 0;
    }

    .noAppendPadding :deep(.v-field--appended) {
        padding-inline-end: 0;
    }

    .smallPrependInnerIcon :deep(.v-field__prepend-inner .v-icon) {
        --v-icon-size-multiplier: 0.75;
    }

    .smallAppendInnerIcon :deep(.v-field__append-inner .v-icon) {
        --v-icon-size-multiplier: 0.75;
    }

    :deep(.v-field--variant-filled .v-field__outline) {
        display: none;
    }

    .small :deep(input) {
        min-height: auto;
        padding-top: 1px;
        padding-bottom: 1px;
    }
</style>