<template>
    <div ref="canvasWrapper"
         :style="{cursor}"
         class="canvasWrapper w-100 h-100">
        <div ref="canvasContainer"
             class="canvasContainer"/>

        <div v-if="isRendererReady && rendererManager?.isReady.value"
             v-show="false">
            <slot/>
        </div>

        <div v-if="false"
             style="position: absolute; left: calc(50% - 25px); top: calc(50% - 25px); width: 50px; height: 50px; background-color: magenta;"/>

        <!--        <div style="position: absolute; bottom: 0; right: 0; color: magenta; padding: 50px; font-size: 150%; font-weight: bold; background-color: black;">-->
        <!--        </div>-->

        <v-slide-y-reverse-transition>
            <div v-if="showSignature"
                 :class="{
                start: signaturePosition === 'START',
                end: signaturePosition === 'END'
            }"
                 class="signature text-caption pa-4">
                <d-sheet rounded="pill">
                    generated by doorbit
                </d-sheet>
            </div>
        </v-slide-y-reverse-transition>
    </div>
</template>

<script lang="ts"
        setup>
    import {computed, nextTick, onMounted, onUnmounted, provide, shallowRef, watchEffect} from "vue";
    import {TRendererManager} from "@/adapter/three/TRendererManager";
    import {TMeshInjection, TRendererManagerInjection} from "@/adapter/three/TInjectionKeys";
    import {useRoute} from "vue-router";
    import {Color, SRGBColorSpace} from "three";
    import Stats from "three/addons/libs/stats.module.js"
    import {IS_DEVELOPMENT} from "@/utility/environment";
    import DSheet from "@/adapter/vuetify/theme/components/card/d-sheet.vue";
    import {TRaycaster} from "@/adapter/three/raycasting/TRaycaster";
    import {Optional} from "@/model/Optional";
    import {SVGRenderer} from 'three/addons/renderers/SVGRenderer.js';

    const props = defineProps<{
        rendererId: string,
        fillWindow?: boolean
        fitToSceneAfterResize?: boolean
        showStats?: boolean
        showInfo?: boolean
        cursor?: string
        paused?: boolean
        redrawCounter?: number
        showSignature: boolean
        raycasters?: readonly TRaycaster[] //not reactive
        signaturePosition?: "START" | "END"
    }>()

    provide(TMeshInjection, null)

    const route = useRoute()
    const isDebugModeEnabled = computed<boolean>(() => route.query.debug === 'true')

    const canvasContainer = shallowRef<HTMLDivElement>();
    const canvasWrapper = shallowRef<HTMLDivElement>();
    const isRendererReady = shallowRef<boolean>(false);
    const rendererManager = shallowRef<Optional<TRendererManager>>(null)
    let stats: Optional<Stats> = null

    //https://threejs.org/docs/#examples/en/renderers/SVGRenderer
    const renderer = new SVGRenderer();
    renderer.overdraw = 0
    renderer.setClearColor(new Color(0x000000), 0)
    renderer.outputColorSpace = SRGBColorSpace //das ist sehr wichtig in kombination mit three-utility.ts initialize, sonst werden die farben falsch dargestellt

    watchEffect(() => {
        const rm = rendererManager.value
        if (rm === null) {
            return
        }
        rm.forcePause.value = props.paused
    })
    watchEffect(() => {
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        props.redrawCounter //trigger reactivity

        const rm = rendererManager.value
        if (rm === null) {
            return
        }
        //this will trigger the render loop for 10s
        rm.hasRenderedOnce.value = true
        rm.hasRenderedOnce.value = false
    })

    const onTouchStart = (event: TouchEvent) => { //implicit "this" binding
        const eventTarget = event.target as Optional<HTMLElement>
        if (eventTarget === null) {
            return
        }
        const canvas = canvasContainer.value?.children?.item(0) as HTMLCanvasElement ?? null
        if (eventTarget !== canvas) {
            return
        }
        console.log("Touch blocked in canvas")
        event.preventDefault() //this prevents the default behavior of the browser, which is scrolling or long press
    }

    onMounted(() => {
        const container = canvasContainer.value!

        rendererManager.value = new TRendererManager(
            props.rendererId,
            renderer,
            container,
            props.fillWindow,
            props.fitToSceneAfterResize,
            [],
            props.raycasters ? [...props.raycasters] : [],
            false
        )
        provide(TRendererManagerInjection, rendererManager.value)

        if (IS_DEVELOPMENT && (props.showStats || isDebugModeEnabled.value)) {
            initializeStats()
        }

        isRendererReady.value = true

        if (IS_DEVELOPMENT && props.showInfo) {
            (window as any).printRendererInfo = () => {
                console.log(renderer.info)
            }

            nextTick(() => {
                console.log(renderer.info)
            })
        }

        window.addEventListener("touchstart", onTouchStart, {passive: false})
    })

    function render(delta: number) {
        stats?.update()
    }

    onUnmounted(() => {
        delete (window as any).printRendererInfo

        window.removeEventListener("touchstart", onTouchStart)

        rendererManager.value?.removeRenderListener(render)
        stats?.dom.remove()

        rendererManager.value?.destroy()
        renderer.domElement.remove()

        rendererManager.value = null
    })

    function initializeStats() {
        stats = new Stats()
        stats.dom.className = "rendererStats"
        canvasWrapper.value!.append(stats.dom)

        rendererManager.value?.addRenderListener(render)
    }

    function generateSVG(): SVGElement {
        return renderer.domElement
    }

    defineExpose({
        generateSVG
    })
</script>

<style scoped>
    .canvasWrapper {
        position: relative;
        /*background-color: magenta; /*TODO: noch ändern*/
        /*color: white;*/
    }

    .canvasWrapper > :deep(.rendererStats) {
        position: absolute !important;
    }

    .canvasContainer {
        width: 100%;
        height: 100%;
        position: relative;
    }

    .canvasContainer :deep(svg) {
        position: absolute;
    }

    .signature {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        text-align: center;
        font-weight: bold;
        display: flex;
        justify-content: center;
        pointer-events: none;
    }

    .signature.start {
        width: auto;
    }

    .signature.end {
        left: auto;
        right: 0;
        width: auto;
    }

    .signature > * {
        font-size: 80%;
        padding: 2px 4px;
        line-height: 1;
    }
</style>