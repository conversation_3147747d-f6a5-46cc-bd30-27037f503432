// noinspection ES6UnusedImports
import {DefineLocaleMessage} from 'vue-i18n';
import {PointOfInterestType} from "@/model/listing/PointOfInterestType";
import {PointOfInterestDetail} from "@/model/listing/PointOfInterestDetail";
import {PointOfInterestParentGroup} from "@/model/listing/PointOfInterestParentGroup";
import {EnergyEffiencyCategory} from "@/model/listing/EnergyEffiencyCategory";
import {TransportType} from "@/model/listing/TransportType";
import {AttractivenessCategory} from "@/model/listing/AttractivenessCategory";
import {AttractivenessCriteria} from "@/model/listing/AttractivenessCriteria";
import {PopulationGroup} from "@/model/listing/PopulationGroup";
import {LanguageCode} from "@/adapter/vue-i18n/LanguageCode";
import {MenuSubCategory} from "@/components/account/MenuItem";
import {ListingRegion} from "@/model/listing/ListingRegion";
import {ListingFieldUnit} from "@/model/listing/ListingFieldUnit";
import {ContactInformationType} from "@/model/userprofile/ContactInformationType";
import {AdditionalInformationType} from "@/model/userprofile/AdditionalInformationType";
import {PreferredThemeOptionType} from "@/model/userprofile/PreferredThemeOptionType";
import {ContactReason} from "@/adapter/graphql/generated/graphql";
import {ListingCustomStatus} from "@/model/listing/ListingCustomStatus";
import {UserFriendlyServiceWorkerState} from "@/components/layout/header/UserFriendlyServiceWorkerState";
import {WallOpeningType} from "@/model/building/WallOpeningType";
import {WhiteLabelContextId} from "@/service/white-label/white-label-context";
import {ShapeType} from "@/model/building/ShapeType";
import {WallType} from "@/model/building/WallType";
import {BuildingExportType, EvexExportType, GBXMLExportType, PDFExportType} from "@/model/building/BuildingExportType";
import {IFCExportContextTargetCAD} from "@/adapter/ifc/IFCExportContext";
import {CardinalDirectionType} from "@/model/CardinalDirection";
import {WallSizeAdjustmentYSideResultType} from "@/components/listing/building/pipeline/stages/RecalculateAllWallSizeAdjustmentsBPipelineStage";
import {BuildingComponentCADType} from "@/components/listing/building/renderer/BuildingComponent";

declare module 'vue-i18n' {
    declare interface DefineLocaleMessage {
        $vuetify: {
            carousel: {
                prev: string
                next: string
                ariaLabel: {
                    delimiter: string
                }
            }
            dataFooter: {
                itemsPerPageText: string
                pageText: string //Parameter: {page}, {pages}
                firstPage: string
                prevPage: string
                nextPage: string
                lastPage: string
            }
            badge: string
            noDataText: string
            open: string
            close: string
            input: {
                clear: string
            }
            loading: string
            infiniteScroll: {
                loadMore: string
                empty: string
            }
            stepper: {
                prev: string
                next: string
            }
            rating: {
                ariaLabel: {
                    item: string
                }
            }
            pagination: {
                ariaLabel: {
                    root: string
                    previous: string
                    next: string
                    page: string
                    currentPage: string
                }
            }
            datePicker: {
                header: string
            }
        }
        documentationButton: string
        network: {
            noInternetConnection: string
        }
        dateConverter: {
            timeFormat: string //Parameter: {time}
            dateTimeFormat: string //Parameter: {date}, {time}
        }
        delete: {
            confirmationHeading: string,
        }
        theme: {
            components: {
                input: {
                    counter: string
                    autocomplete: {
                        address: {
                            placeholder: string
                        }
                    }
                }
            }
        }
        globalErrorDialog: {
            title: string
            description: {
                noInternet: string
                newVersion: string
                unknown: string
            }
            reloadButton: string
            continueButton: string
        }
        flowConfig: {
            uiElement: {
                array: {
                    itemName: string //Parameter: {number}
                }
                arrayWrapper: {
                    size: string //Parameter: {size}, {sizeMaximum}
                }
                custom: {
                    scanner: {
                        buildingNotScannedYet: string
                        deleteBuildingDialog: {
                            title: string
                            description: string
                            confirm: string
                            cancel: string
                        }
                    }
                }
                date: {
                    ymdt: {
                        time: string
                    }
                }
                suggestion: {
                    currentValue: string
                    suggestedValue: string
                    closeButton: string
                    abortButton: string
                    applyButton: string
                }
                file: {
                    image: {
                        noImagesAvailable: string
                        showAllButton: string
                    }
                }
            }
        }
        key: {
            control: string
        }
        listing: {
            loadingError: {
                message: string
                backButton: string
            }
            edit: {
                backButton: string
                showButton: string
                duplicateButton: string
                pageIndicator: string //Parameter: {currentPage}, {totalPages}
                navigateBackwardButton: string
                navigateForwardButton: string
                saveButton: string
                doneButton: string
                unsavedChangesWarning: string
            }
            view: {
                backButton: string
                editButton: string
            }
            building: {
                creationProcess: {
                    loadingBuildingScan: string
                    buildingScanNotCompleted: string
                    buildingScanNotFound: string
                    creatingBuilding: string
                    optimizeBuilding: string
                }
                wallType: {
                    INTERIOR_WALL: string
                    EXTERIOR_WALL: string
                    INTERIOR_PARTITION_WALL: string
                    EXTERIOR_PARTITION_WALL: string
                    INTERMEDIATE_WALL: string
                }
                cad: {
                    history: {
                        undo: string
                        redo: string
                    }
                    displayMode: string
                    renderType: string
                    doorbitStudioButton: Record<WhiteLabelContextId, string>
                    building: string
                    selection: {
                        evebiDisplayIds: string
                        evebiRoomNumbers: string
                        wallRoofPoints: {
                            info: string
                            warning: string
                            deselectButton: string
                            createRoofAreaButton: string
                        }
                        wall: {
                            evebi: {
                                ringWallWarning: string
                                neighborWallThicknessShares: string
                                interiorWidth: string
                                exteriorWidth: string
                                slabHeightShares: string
                                slabHeightSharesPolygon: string //percent, height
                                interiorHeight: string
                                exteriorHeight: string
                                slabTypes: Record<WallSizeAdjustmentYSideResultType, string>
                                exteriorWallThickness: string
                                correction: string
                                addRelatedWallPlaceholder: string
                                description: string
                                info: string
                            }
                            shape: string
                            isIntermediate: {
                                label: string
                                info: string
                            }
                            width: string
                            height: string
                            startAngle: string
                            endAngle: string
                            innerRadius: string
                            thickness: string
                            editShapeButton: string
                            wallEditorDialog: {
                                title: string
                                leftClickInfo: string
                                rightClickInfo: string
                                resetVerticesButton: string
                                saveVerticesButton: string
                            }
                            overwriteMenu: {
                                menuItems: {
                                    allOfTypeOnFloor: Record<WallType, string>
                                    allOnFloor: string
                                    allOfType: Record<WallType, string>
                                    all: string
                                }
                                variant: {
                                    thickness: {
                                        button: string
                                        description: string
                                    }
                                    height: {
                                        button: string
                                        description: string
                                    }
                                    evebi: {
                                        thickness: {
                                            button: string
                                            description: string
                                        }
                                    }
                                    uValue: {
                                        button: string
                                        description: string
                                    }
                                }
                            }
                        }
                        opening: {
                            relocateButton: string
                            type: string
                            width: string
                            height: string
                            sillHeight: string
                            overwriteMenu: {
                                menuItems: {
                                    allOfTypeOnWall: Record<WallOpeningType, string>
                                    allOfTypeOnFloor: Record<WallOpeningType, string>
                                    allOfType: Record<WallOpeningType, string>
                                }
                                variant: {
                                    uValue: {
                                        button: Record<WallOpeningType, string>
                                        description: Record<WallOpeningType, string>
                                    }
                                }
                            }
                        }
                        building: {
                            rotation: string
                        }
                        floor: {
                            autoRotateButton: string
                            rotation: string
                            floorSlabHeight: string
                            ceilingSlabHeight: string
                        }
                    }
                    componentTypes: Record<BuildingComponentCADType, string> //Pluralization: {n}
                    defaultRoomName: string //Parameter: {roomNumber}
                    floorLevelInfo: {
                        type: {
                            short: Record<string, string> //Pluralization: {n}
                            long: Record<string, string> //Pluralization: {n}
                        }
                    },
                    subflow: {
                        deleteButton: string
                    },
                    toolbar: {
                        mode: {
                            selection: {
                                tooltip: string
                            }
                            dragAndDrop: {
                                tooltip: string
                                subItem: {
                                    grouped: {
                                        tooltip: string
                                    }
                                    ungrouped: {
                                        tooltip: string
                                    }
                                }
                            }
                            wallCreation: {
                                tooltip: string
                            }
                            poiAdding: {
                                tooltip: string
                            }
                            roofAreaCreation: {
                                tooltip: string
                            }
                            openingCreation: {
                                tooltip: string
                                subItem: {
                                    door: {
                                        tooltip: string
                                    }
                                    window: {
                                        tooltip: string
                                    }
                                    opening: {
                                        tooltip: string
                                    }
                                }
                            }
                        }
                        selection: {
                            building: {
                                tooltip: string
                            }
                        }
                        floors: {
                            addMode: {
                                tooltip: string
                            }
                            addFloor: {
                                tooltip: string
                            }
                        }
                    }
                }
            }
        }
        //see validation-rules.ts
        validation: {
            required: string
            step: string //Parameter: {step}
            lengthMin: string //Parameter: {lengthMin}
            lengthMax: string //Parameter: {lengthMax}
            min: string //Parameter: {min}
            max: string //Parameter: {max}
            arraySizeMin: string //Parameter: {arraySizeMin}
            arraySizeMax: string //Parameter: {arraySizeMax}
        }
        iOSAppClip: {
            success: string
            error: string
        }
        boolean: {
            yes: string
            no: string
        }
        pageTitle: {
            default: Record<WhiteLabelContextId, string>
            custom: Record<WhiteLabelContextId, string>
        },
        addressFormat: {
            singleLine: string //Parameters: {street}, {houseNumber}, {zipcode}, {city}
            singleLineNoHouseNumber: string //Parameters: {street}, {zipcode}, {city}
            streetAndHouseNumber: string //Parameters: {street}, {houseNumber}
            zipcodeAndCity: string //Parameters: {zipcode}, {city}
        }
        pullToRefresh: {
            pullToRefresh: string
            releaseToRefresh: string
        }
        serviceWorkerStatusDialog: {
            title: string
            state: Record<UserFriendlyServiceWorkerState, string>
            newVersion: {
                updateAvailableButton: string
                applyUpdateButton: string
            }
            reregisterServiceWorker: {
                button: string
                description: string
            }
            clearOfflineData: {
                button: string
                description: string
            }
            queue: string //Parameter: {queueSize}
        }
        nativeApp: {
            rooms: {
                title: string
            }
            bottomNavigation: {
                account: string
                reloadButton: string
            }
            account: {
                details: string
                logout: string
            }
            settings: {
                tac: string
                privacy: string
                imprint: string
                app: string
            }
            renderer: {
                floorWithFocusedRoom: {
                    newFloorMessage: string
                }
            }
        }
        components: {
            topMenu: {
                offerType: string
                mainType: string
                nearby: {
                    inputPlaceholder: string
                    radius: string
                }
            }
            components: {
                aiSearch: {
                    buttonLabel: string
                }
            }
            contact: {
                title: string
                subtitle: string
                email: string
                tel: string
                formData: {
                    title: string
                    name: string
                    email: string
                    message: string
                    registerForUpdates: string
                    contactReason: string
                }
                contactReason: Record<ContactReason, string>
                submit: string
                submitSuccess: string
                submitError: string
            }
            map: {
                clickToInteract: string
            }
            notFound: {
                title: string
                description: string
                backToAccount: string
            }
            facet: {
                searchBox: {
                    placeholder: string
                }
                distanceFacet: {
                    title: string
                    description: string
                    transportType: Record<TransportType, string>
                    travelDuration: string
                }
                facetTypeCheckbox: {
                    yes: string
                    no: string
                }
                facetTypeRangeSliderRead: {
                    min: string
                    max: string
                    minMax: string
                }
                facetSelection: {
                    chip: string
                }
                showLess: string
                showMore: string
                showResults: string
            }
            layout: {
                app: {
                    feedback: {
                        buttonText: string
                        dialog: {
                            title: string
                            description: string
                            textCaption: string
                            close: string
                            submit: string
                            registerForUpdates: string
                            nameCaption: string
                            emailCaption: string
                            errorMessage: string
                            successTitle: string
                            successMessage: string
                        }
                    }
                }
            }
            login: {
                redirectMessage: string
            }
            logout: {
                redirectMessage: string
            }
            listing: {
                labels: {
                    hasFloorPlan: string,
                    has3DModel: string,
                }
                list: {
                    filter: string
                    filterWithActiveCount: string
                    results: string
                }
                edit: {
                    location: {
                        markerInfoStatus: {
                            geolocalization: string
                            invalid: string
                            valid: string
                            obfuscated: string
                        }
                        editAddress: string
                        lastAddresses: {
                            single: string,
                            multiple: string
                        }
                        obfuscateLocation: {
                            label: string
                            hint: string
                        }
                        geolocalizationError: {
                            permissionDenied: {
                                nativeApp: {
                                    goToSettings: {
                                        introduction: string
                                        button: string
                                    }
                                    reloadIntroduction: string
                                }
                                reload: {
                                    introduction: string
                                    button: string
                                }
                            }
                            positionUnavailable: string
                            timeout: string
                            unknown: string
                        }
                        confirmButton: string
                    }
                    images: {
                        name: string
                        delete: string
                        deleteImage: {
                            description: string
                            confirm: string
                            cancel: string
                        }
                        statusIndicator: {
                            uploading: string
                            uploaded: string
                            failed: string
                        }
                        imageUploadError: string
                        isImageUploading: string
                        addImage: string
                        savingFailed: string
                        savingSucceeded: string
                        noImages: string
                    }
                    details: {
                        furnishings: string
                        storage: string
                        outdoors: string
                        wellbeing: string
                        otherFurnishings: string
                        spaceUtilization: string
                    }
                    buildingPlan: {
                        startScannerButton: string
                        completeScanButton: string
                        continueScanButton: string
                        intro: {
                            title: string
                            description: string
                            tutorial: {
                                question: string
                                steps: {
                                    closeWindowsAndDoors: string
                                    lighting: string
                                    roomByRoom: string
                                    cameraCover: string
                                }
                                closeDialogButton: string
                            }
                            noLIDAR: string
                            qrCode: {
                                caption: string
                                error: string
                                share: {
                                    title: string
                                    message: string
                                }
                            }
                        }
                    }
                    energy: {
                        efficiency: string
                        heating: string,
                        waterHeating: string,
                        ventilationAndTightness: string,
                        solar: string
                        other: string
                    }
                    saveListing: string
                    saveListingSucceeded: string
                    saveListingFailed: string
                    aiText: {
                        generating: string
                        applyButton: {
                            label: string
                            confirmationDialogQuestion: string
                        }
                    }
                    asteriskText: string
                }
                detail: {
                    overview: {
                        highlightsTitle: string
                        propertyDescriptionTitle: string
                        moreInformationTitle: string
                    }
                    energy: {
                        noInformation: string
                    }
                    contact: {
                        vendorDetails: {
                            title: string
                            privateVendor: string
                            ratings: string
                            fields: {
                                EMAIL: string
                                WEBSITE: string
                                TELEPHONE: string
                                COMPANY_ADDRESS: string
                                VAT_NUMBER: string
                                PROFESSION: string
                                CONSULTANT_NUMBER: string
                            }
                        }
                    }
                    location: {
                        locationDescriptionTitle: string
                        pointsOfInterestTitle: string
                        addressTitle: string
                        nearestMetropolitanCitiesTitle: string
                    }
                    attractivity: {
                        percentageTotal: string
                        attractivityScoreTitle: string
                        attractivityScoreDescription: string
                        ratingCriteriaDescription: Record<AttractivenessCriteria, string>
                    }
                    cta: {
                        contact: string
                        downloadImages: string
                    }
                }
                fieldUnitValue: string
                buildingModel: {
                    backToListingButton: string
                    floorLevels: string
                    exportDialog: {
                        title: string
                        button: string
                        downloadButton: string
                        onlyEnergeticRelevantParts: string
                        onlyEnergeticRelevantPartsFilenameToken: string
                        optimizeExportFor: string
                        pageOrientation: string
                    }
                    visibilityMenu: {
                        button: string
                        items: {
                            outlines: string
                            generic: string
                            roomTexts: string
                            compass: string
                            pointsOfInterest: string
                            furniture: string
                            walls: string
                            wallWidths: string
                            wallThicknesses: string
                            slabs: string
                            roofAreas: string
                            displayIds: string
                        }
                    }
                }
                buildingModelPreview: {
                    openDetailsButton: string
                }
                images: {
                    more: string
                    backToListing: string
                    toggleBlackBackground: string
                }
                imagesDownload: {
                    yourDownloadWillStartShortly: string
                }
                print: string
                report: string
                offer: {
                    noEndOfRentDate: string
                    hasEndOfRent: string
                }
                objectDescription: {
                    showMore: string
                    showLess: string
                }
                adminBoundary: {
                    deviationFromAverage: {
                        aboveAverage: string
                        belowAverage: string
                        average: string
                    }
                    deviationFromAverageTitle: string
                    populationGroup: {
                        title: string
                        plural: Record<PopulationGroup, string>
                    }
                    areaInSquareKilometers: {
                        title: string
                    }
                    population: {
                        totalPopulation: {
                            title: string
                            subtitle: string
                        }
                        threeYearsTrend: string
                        fiveYearsTrend: string
                        tenYearsTrend: string
                        density: {
                            title: string
                            HIGH: string
                            LOW: string
                            MEDIUM: string
                            VERY_HIGH: string
                            VERY_LOW: string
                        }
                        ageDistribution: {
                            title: string
                            deviationFromAverageDescription: string
                        }
                        ageGroup: {
                            BELOW_3: string
                            FROM_3_TO_6: string
                            FROM_6_TO_15: string
                            FROM_15_TO_18: string
                            FROM_18_TO_25: string
                            FROM_25_TO_30: string
                            FROM_30_TO_40: string
                            FROM_40_TO_50: string
                            FROM_50_TO_60: string
                            ABOVE_60: string
                        }
                    }
                    income: {
                        title: string
                        deviationFromAverageDescription: string
                    }
                    perHeadOutput: {
                        title: string
                    }
                    education: {
                        title: string
                        withoutVocationalTraining: string
                        withVocationalTraining: string
                        withHigherEducation: string
                        rest: string
                    }
                    unemploymentStatistics: {
                        title: string
                        currentYearTotal: string
                        tenYearsTrend: string
                    }
                    crimeStatistics: {
                        title: string
                        fiveYearsTrend: string
                        tenYearsTrend: string
                    }
                }
                attractiveness: {
                    title: string
                    description: string
                    score: string
                    poiTooltip: {
                        available: string
                        unavailable: string
                    }
                }
                highlights: {
                    availableNow: string
                    showMore: string
                    showLess: string
                    highlightsSubtitle: string
                    listingHighlight: {
                        titleWithFieldname: string
                    }
                    locationHighlights: {
                        title: string
                        titleNoDuration: string
                        maxDurationLessThan: string
                        maxDurationApproximately: string
                        atDoorstep: string
                        withinWalkingDistance: string
                        transportType: Record<TransportType, string>
                    }
                }
                map: {
                    searchInThisArea: string
                    loadMoreResults: string
                    houseMarkerShowMore: string
                }
                actions: {
                    editMode: string
                    viewMode: string
                    shareButtonMessage: Record<WhiteLabelContextId, string>
                }
                notFound: {
                    title: string
                    description: string
                }
            }
            shareButton: {
                title: string
                copy: string
            }
            account: {
                login: string
                logout: string
                reloadFlowConfigsButton: string
                menuSubCategories: Record<MenuSubCategory, string>
                accountDetails: {
                    title: string
                    userProfile: {
                        firstname: string
                        lastname: string
                        email: string
                        title: string
                        preferredLanguage: {
                            label: string
                            options: Record<LanguageCode, string>
                        }
                        preferredTheme: {
                            label: string
                            options: Record<PreferredThemeOptionType, string>
                        }
                        companyInformation: {
                            title: string
                            address: {
                                title: string
                                name: string
                                street: string
                                houseNumber: string
                                zipCode: string
                                city: string
                                country: string
                                apartmentNumber: string
                            }
                        }
                        contactInformation: {
                            title: string
                            type: Record<ContactInformationType, string>
                        }
                        additionalInformation: {
                            title: string,
                            type: Record<AdditionalInformationType, string>
                        }
                        save: string
                        saveSucceeded: string
                        saveFailed: string
                        deleteImage: string
                    }
                    furtherOptions: {
                        title: string
                        changePassword: string
                        deleteAccount: {
                            title: string
                            description: string
                            confirm: string
                            cancel: string
                            errorTitle: string
                            errorDescription: string
                        }
                    }
                }
                accountListings: {
                    card: {
                        type: Record<string, Record<string, string>>
                        customStatus: Record<ListingCustomStatus, string>
                    }
                    title: string
                    description: string
                    delete: string
                    dialog: {
                        duplicate: {
                            title: string
                            description: string
                            confirmButton: string
                        }
                        delete: {
                            title: string
                            description: string
                            confirmButton: string
                        }
                        cancel: string
                    }
                    view: string
                    edit: string
                    duplicate: string
                    create: string
                    visits: string
                    remainingDays: {
                        remaining: string
                        days: string
                    }
                    listingFilter: {
                        customStatus: string
                        onlyMyListings: string
                        search: string
                        zeroSearchResults: string
                        showAllButton: string
                        reloadButton: string
                    }
                }
                accountBuildings: {
                    title: string
                }
            }
            footer: {
                appRoutes: {
                    mobileApps: string
                    registration: string
                    faq: string
                    contact: string
                    privacyPolicy: string
                    imprint: string
                    attributions: string
                    termsAndConditions: string
                }
                socialMediaProfiles: {
                    twitter: string
                    facebook: string
                    linkedin: string
                }
            }
        }
        enums: {
            wallType: Record<WallType, string>
            cardinalDirection: Record<CardinalDirectionType, string>
            ifcExportContextTargetCAD: Record<IFCExportContextTargetCAD, string>
            pdfExport: {
                label: string
                levels: Record<PDFExportType, string>
                pageOrientation: Record<"portrait" | "landscape", string>
            }
            buildingExportType: Record<BuildingExportType, string>
            gbXMLExportType: Record<GBXMLExportType, string>
            evexExportType: Record<EvexExportType, string>
            wallOpeningType: Record<WallOpeningType, string>
            shapeType: Record<ShapeType, string>
            attractivenessCategoryType: Record<AttractivenessCategory, string>
            attractivenessCriteriaType: Record<AttractivenessCriteria, string>
            energyEfficiencyCategoryType: Record<EnergyEffiencyCategory, string>
            languageCode: Record<LanguageCode, string>
            listingFieldUnit: Record<ListingFieldUnit, string>
            listingRegionType: Record<ListingRegion, string>
            pointOfInterestDetailType: Record<PointOfInterestDetail, string>
            pointOfInterestDetailTypeValues: { //TODO: warum kein eigener typ?!
                AREA: {
                    SMALL_WOOD: string
                    WOOD: string
                    FOREST: string
                }
            }
            pointOfInterestParentGroupType: Record<PointOfInterestParentGroup, string>
            pointOfInterestType: Record<PointOfInterestType, string>
            populationGroupType: Record<PopulationGroup, string>
        }
        units: {
            squareMeters: string
            squareMetersShort: string
            rooms: string
            roomsShort: string
            currencyPerSquareMeter: string
            squareKilometersShort: string
        }
        formRules: {
            required: string
            email: string
            phoneNumber: string
        }
    }
}