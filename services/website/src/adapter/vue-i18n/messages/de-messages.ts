import {DefineLocaleMessage} from 'vue-i18n';
import {de} from 'vuetify/locale';

const deMessages: DefineLocaleMessage = {
    $vuetify: de,
    network: {
        noInternetConnection: "Keine Internetverbindung",
    },
    dateConverter: {
        timeFormat: "{time} Uhr",
        dateTimeFormat: "{date}, {time} Uhr",
    },
    delete: {
        confirmationHeading: "Bist Du sicher?",
    },
    theme: {
        components: {
            input: {
                counter: "{counter} / {counterMaximum}",
                autocomplete: {
                    address: {
                        placeholder: "<PERSON>ress<PERSON> eingeben …",
                    }
                }
            }
        }
    },
    pageTitle: {
        default: {
            doorbit: "doorbit",
            renaldo: "renaldo",
        },
        custom: {
            doorbit: "{title} {'|'} doorbit",
            renaldo: "{title} {'|'} renaldo",
        },
    },
    documentationButton: "Dokumentation",
    globalErrorDialog: {
        title: "<PERSON><PERSON>",
        description: {
            noInternet: "Es konnte keine Verbindung zum Server hergestellt werden. Bitte überprüfe deine Internetverbindung und versuche es erneut.",
            newVersion: "Es wurde eine neue Version dieser Seite veröffentlicht. Möglicherweise hängt der Fehler damit zusammen, dass Du eine ältere Version verwendest. Bitte lade die Seite neu.",
            unknown: "Ein unbekannter Fehler ist aufgetreten. Bitte versuche es später erneut.",
        },
        reloadButton: "Seite neu laden",
        continueButton: "Fortfahren"
    },
    flowConfig: {
        uiElement: {
            array: {
                itemName: "Element {number}"
            },
            arrayWrapper: {
                size: "{size} / {sizeMaximum}"
            },
            custom: {
                scanner: {
                    buildingNotScannedYet: "Gebäude noch nicht gescannt",
                    deleteBuildingDialog: {
                        title: "Gebäude löschen",
                        description: "Möchtest Du den Gebäude-Scan und das -Modell wirklich löschen? Der vollständige Scan, alle gepflegten Bauteildaten und Änderungen, die im doorbit Studio an diesem Gebäude vorgenommen wurden, werden unwiderruflich gelöscht.",
                        confirm: "Löschen",
                        cancel: "Abbrechen",
                    }
                }
            },
            date: {
                ymdt: {
                    time: "Uhrzeit",
                }
            },
            suggestion: {
                currentValue: "Aktueller Wert",
                suggestedValue: "Vorgeschlagener Wert",
                closeButton: "Schließen",
                abortButton: "Abbrechen",
                applyButton: "Übernehmen",
            },
            file: {
                image: {
                    noImagesAvailable: "Keine Bilder verfügbar",
                    showAllButton: "Alle anzeigen",
                }
            }
        }
    },
    key: {
        control: "Strg",
    },
    listing: {
        loadingError: {
            message: "Beim Laden ist ein Fehler aufgetreten. Bitte überprüfe deine Verbindung und versuche es später erneut.",
            backButton: "Zurück",
        },
        edit: {
            backButton: "Zurück",
            showButton: "Fertig",
            duplicateButton: "Duplizieren",
            pageIndicator: "{currentPage} / {totalPages}",
            navigateBackwardButton: "Zurück",
            navigateForwardButton: "Weiter",
            saveButton: "Speichern",
            doneButton: "Fertig",
            unsavedChangesWarning: "Die vorgenommenen Änderungen wurden nicht gespeichert. Möchtest Du die Seite wirklich verlassen?",
        },
        view: {
            backButton: "Zurück",
            editButton: "Bearbeiten",
        },
        building: {
            creationProcess: {
                loadingBuildingScan: "Lade Gebäude-Scan …",
                buildingScanNotCompleted: "Gebäude-Scan nicht abgeschlossen",
                buildingScanNotFound: "Gebäude-Scan nicht gefunden",
                creatingBuilding: "Erstelle Gebäude …",
                optimizeBuilding: "Optimiere Gebäude …",
            },
            wallType: {
                INTERIOR_WALL: "Innenwand",
                EXTERIOR_WALL: "Außenwand",
                INTERIOR_PARTITION_WALL: "Innere Trennwand",
                EXTERIOR_PARTITION_WALL: "Äußere Trennwand",
                INTERMEDIATE_WALL: "Zwischenwand",
            },
            cad: {
                history: {
                    undo: "Rückgängig machen",
                    redo: "Wiederholen",
                },
                displayMode: "Anzeigemodus",
                renderType: "Darstellung",
                doorbitStudioButton: {
                    doorbit: "doorbit Studio",
                    renaldo: "Gebäudemodell öffnen"
                },
                building: "Gebäude",
                selection: {
                    evebiDisplayIds: "Evebi-ID | Evebi-IDs",
                    evebiRoomNumbers: "Evebi-Raumnummer | Evebi-Raumnummern",
                    wallRoofPoints: {
                        info: "Bei der Auswahl der Dachflächenpunkte ist die Reihenfolge entscheidend. Bewege dich am besten entlang des Randes der zu erstellenden Dachfläche und versuche keine Punkte zu überspringen. Du kannst die Punkte im oder gegen den Uhrzeigersinn auswählen. Sobald du die ersten Punkte ausgewählt hast, wird die Dachfläche einem Raum zugeordnet. Wählst du einen bereits ausgewählten Punkt erneut aus, wird dieser abgewählt und ist nicht mehr Teil der zu erstellenden Dachfläche. Entsteht bei der Auswahl der Punkte nicht die gewünschte Dachfläche, beginne von vorn und versuche sie in kleinere Teilflächen zu unterteilen.",
                        warning: "Bitte wähle mindestens drei Dachflächenpunkte aus, um eine Dachfläche erstellen zu können.",
                        deselectButton: "Auswahl verwerfen",
                        createRoofAreaButton: "Dachfläche erstellen",
                    },
                    wall: {
                        evebi: {
                            ringWallWarning: "Runde Wände werden im Evebi-Modus noch nicht unterstützt.",
                            neighborWallThicknessShares: "Anteile an Nachbarwandstärken",
                            interiorWidth: "Innenlänge",
                            exteriorWidth: "Außenlänge",
                            slabHeightShares: "Anteile an Plattenhöhen",
                            slabHeightSharesPolygon: "{percent} von {height}",
                            interiorHeight: "Innenhöhe",
                            exteriorHeight: "Außenhöhe",
                            slabTypes: {
                                FLOOR: "Boden",
                                CEILING: "Decke",
                            },
                            exteriorWallThickness: "Außenwandstärke",
                            correction: "Manuelle Anpassung",
                            addRelatedWallPlaceholder: "Wand hinzufügen …",
                            description: "Die unteren Angaben dienen dazu, die Evebi-Offsets sowie Außenflächen (Bruttoflächen) für den Export von .evex-Dateien zu bestimmen.",
                            info: "Wandstärken wirken sich automatisch auf die Außenlängen orthogonal verbundener Außenwände aus. Die im Selektionsdialog der Stockwerke pflegbaren Boden- und Deckenplattenhöhen wirken sich automatisch auf die Geschosshöhen der Stockwerke aus.",
                        },
                        shape: "Form",
                        isIntermediate: {
                            label: "Zwischenwand",
                            info: "Zwischenwände trennen zwei Gebäude/Wohnungen voneinander.",
                        },
                        width: "Länge",
                        height: "Höhe",
                        startAngle: "Startwinkel",
                        endAngle: "Endwinkel",
                        innerRadius: "Innenradius",
                        thickness: "Stärke",
                        editShapeButton: "Form bearbeiten",
                        wallEditorDialog: {
                            title: "Wandform bearbeiten",
                            leftClickInfo: "Punkt hinzufügen",
                            rightClickInfo: "Punkt entfernen",
                            resetVerticesButton: "Zurücksetzen",
                            saveVerticesButton: "Speichern",
                        },
                        overwriteMenu: {
                            menuItems: {
                                allOfTypeOnFloor: {
                                    INTERIOR: "Alle Innenwände des selben Stockwerks",
                                    INTERMEDIATE: "Alle Zwischenwände des selben Stockwerks",
                                    EXTERIOR: "Alle Außenwände des selben Stockwerks",
                                },
                                allOnFloor: "Alle Wände des selben Stockwerks",
                                allOfType: {
                                    INTERIOR: "Alle Innenwände",
                                    INTERMEDIATE: "Alle Zwischenwände",
                                    EXTERIOR: "Alle Außenwände",
                                },
                                all: "Alle Wände",
                            },
                            variant: {
                                thickness: {
                                    button: "Wandstärken überschreiben",
                                    description: "Setzt die Wandstärke anderer Wände auf die Wandstärke dieser Wand. Auf welche Wände möchtest du die Wandstärke übertragen?",
                                },
                                height: {
                                    button: "Wandhöhen überschreiben",
                                    description: "Setzt die Wandhöhen anderer Wände auf die Wandhöhe dieser Wand. Auf welche Wände möchtest du die Wandhöhe übertragen?",
                                },
                                evebi: {
                                    thickness: {
                                        button: "Außenwandstärken überschreiben",
                                        description: "Setzt die Außenwandstärke anderer Wände auf die Außenwandstärke dieser Wand. Auf welche Wände möchtest du die Außenwandstärke übertragen?",
                                    },
                                },
                                uValue: {
                                    button: "U-Werte überschreiben",
                                    description: "Setzt die U-Werte anderer Wände auf den U-Wert dieser Wand. Auf welche Wände möchtest du den U-Wert übertragen?",
                                }
                            },
                        }
                    },
                    opening: {
                        relocateButton: "Verschieben",
                        type: "Typ",
                        width: "Breite",
                        height: "Höhe",
                        sillHeight: "Brüstungshöhe",
                        overwriteMenu: {
                            menuItems: {
                                allOfTypeOnWall: {
                                    DOOR: "Alle Türen der selben Wand",
                                    WINDOW: "Alle Fenster der selben Wand",
                                    OPENING: "Alle Öffnungen der selben Wand",
                                },
                                allOfTypeOnFloor: {
                                    DOOR: "Alle Türen des selben Stockwerks",
                                    WINDOW: "Alle Fenster des selben Stockwerks",
                                    OPENING: "Alle Öffnungen des selben Stockwerks",
                                },
                                allOfType: {
                                    DOOR: "Alle Türen",
                                    WINDOW: "Alle Fenster",
                                    OPENING: "Alle Öffnungen",
                                },
                            },
                            variant: {
                                uValue: {
                                    button: {
                                        DOOR: "U-Werte überschreiben",
                                        WINDOW: "U-Werte überschreiben",
                                        OPENING: "U-Werte überschreiben",
                                    },
                                    description: {
                                        DOOR: "Setzt die U-Werte anderer Türen auf den U-Wert dieser Tür. Auf welche Türen möchtest du den U-Wert übertragen?",
                                        WINDOW: "Setzt die U-Werte anderer Fenster auf den U-Wert dieses Fensters. Auf welche Fenster möchtest du den U-Wert übertragen?",
                                        OPENING: "Setzt die U-Werte anderer Öffnungen auf den U-Wert dieser Öffnung. Auf welche Öffnungen möchtest du den U-Wert übertragen?",
                                    }
                                }
                            },
                        }
                    },
                    floor: {
                        autoRotateButton: "Automatisch",
                        rotation: "Drehung des Stockwerkes im Gebäude",
                        floorSlabHeight: "Bodenplattenhöhe",
                        ceilingSlabHeight: "Deckenplattenhöhe",
                    },
                    building: {
                        rotation: "Gebäudeausrichtung nach Norden",
                    }
                },
                defaultRoomName: "Raum {roomNumber}",
                componentTypes: {
                    BUILDING: "Gebäude | {n} Gebäude",
                    FLOOR: "Stockwerk | {n} Stockwerke",
                    FURNITURE: "Möbel | {n} Möbel",
                    ROOM: "Raum | {n} Räume",
                    WALL_OPENING_DOOR: "Tür | {n} Türen",
                    WALL_OPENING_OPENING: "Öffnung | {n} Öffnungen",
                    WALL_OPENING_WINDOW: "Fenster | {n} Fenster",
                    WALL: "Wand | {n} Wände",
                    POINT_OF_INTEREST: "Notiz | {n} Notizen",
                    POINT_OF_INTEREST_PHOTO: "Foto | {n} Fotos",
                    WALL_ROOF_POINT: "Dachflächenpunkt | {n} Dachflächenpunkte",
                    ROOF_AREA: "Dachfläche | {n} Dachflächen",
                    UNKNOWN: "Unbekannt | {n} Unbekannte",
                },
                subflow: {
                    deleteButton: "{constructionPart} löschen"
                },
                floorLevelInfo: {
                    type: {
                        short: {
                            EG: "EG",
                            UG: "UG | 1.UG | {n}.UG",
                            OG: "OG | 1.OG | {n}.OG",
                            DG: "DG",
                        },
                        long: {
                            EG: "Erdgeschoss",
                            UG: "Untergeschoss | 1. Untergeschoss | {n}. Untergeschoss",
                            OG: "Obergeschoss | 1. Obergeschoss | {n}. Obergeschoss",
                            DG: "Dachgeschoss",
                        }
                    }
                },
                toolbar: {
                    mode: {
                        selection: {
                            tooltip: "Auswählen",
                        },
                        dragAndDrop: {
                            tooltip: "Wände verschieben",
                            subItem: {
                                grouped: {
                                    tooltip: "Gemeinsam verschieben",
                                },
                                ungrouped: {
                                    tooltip: "Frei verschieben",
                                }
                            },
                        },
                        wallCreation: {
                            tooltip: "Wand erstellen",
                        },
                        poiAdding: {
                            tooltip: "Notiz hinzufügen",
                        },
                        roofAreaCreation: {
                            tooltip: "Dachfläche erstellen",
                        },
                        openingCreation: {
                            tooltip: "Öffnung hinzufügen",
                            subItem: {
                                door: {
                                    tooltip: "Tür",
                                },
                                window: {
                                    tooltip: "Fenster",
                                },
                                opening: {
                                    tooltip: "Öffnung",
                                }
                            }
                        }
                    },
                    selection: {
                        building: {
                            tooltip: "Gebäude",
                        },
                    },
                    floors: {
                        addMode: {
                            tooltip: "Stockwerke hinzufügen",
                        },
                        addFloor: {
                            tooltip: "Stockwerk hinzufügen",
                        }
                    }
                },
            }
        }
    },
    serviceWorkerStatusDialog: {
        title: "Offline-Modus",
        state: {
            update: "Ein neue Version von doorbit ist verfügbar!",
            offline: "Du bist offline. Einige Funktionen sind möglicherweise nur eingeschränkt verfügbar.",
            error: "Es kam zu einem Fehler und Du kannst doorbit derzeit nicht offline verwenden. Sollte das Problem länger bestehen, probiere die Funktion „Neu einrichten“ aus. Hilft das nicht, starte die App neu oder installiere sie erneut. Im Web kannst du alle Fenster schließen und doorbit erneut aufrufen.",
            preparing: "doorbit wird für den Offline-Modus vorbereitet. Das kann einen Moment dauern.",
            waiting: "doorbit wartet noch auf den richtigen Moment, um den Offline-Modus vorzubereiten. Das kann einen Moment dauern.",
            ready: "doorbit ist für den Offline-Modus bereit. Du kannst doorbit jetzt auch offline verwenden.",
            unauthorized: "Du bist nicht angemeldet. Bitte melde dich an, um doorbit offline zu verwenden.",
        },
        newVersion: {
            updateAvailableButton: "Neue Version",
            applyUpdateButton: "Jetzt aktualisieren",
        },
        reregisterServiceWorker: {
            button: "Neu einrichten",
            description: "Lädt die Seite neu und versucht alle notwendigen Daten für den Offline-Modus erneut zu laden. Bitte schließe alle anderen Tabs und Fenster, bevor Du fortfährst (gilt nur für Browser).",
        },
        clearOfflineData: {
            button: "Offline-Daten löschen",
            description: "Löscht alle gespeicherten Exposés und die Warteschlange für Offline-Speicheroperationen. Diese Aktion kann zu Datenverlusten führen, führe sie also nur aus, wenn Du Probleme hast oder dir sicher bist, was Du tust.",
        },
        queue: "Warteschlangenlänge: {queueLength}",
    },
    validation: {
        required: "Bitte gebe einen Wert an.",
        step: "Bitte gebe einen ganzzahligen Wert an.",
        lengthMin: "Bitte gebe einen Wert von mindestens {lengthMin} Zeichen an.",
        lengthMax: "Bitte gebe einen Wert von maximal {lengthMax} Zeichen an.",
        min: "Bitte gebe einen Wert größer-gleich {min} an.",
        max: "Bitte gebe einen Wert kleiner-gleich {max} an.",
        arraySizeMin: "Bitte gebe mindestens {arraySizeMin} Elemente an.",
        arraySizeMax: "Bitte gebe maximal {arraySizeMax} Elemente an.",
    },
    iOSAppClip: {
        success: "Vielen Dank für die Nutzung des Gebäude-Scanners.",
        error: "Wir konnten deinen Scan leider nicht speichern. Bitte versuche es erneut.",
    },
    boolean: {
        yes: "Ja",
        no: "Nein",
    },
    addressFormat: {
        singleLine: "{street} {houseNumber}, {zipcode} {city}",
        singleLineNoHouseNumber: "{street}, {zipcode} {city}",
        streetAndHouseNumber: "{street} {houseNumber}",
        zipcodeAndCity: "{zipcode} {city}",
    },
    pullToRefresh: {
        pullToRefresh: "Zum Aktualisieren herunterziehen",
        releaseToRefresh: "Zum Aktualisieren loslassen",
    },
    nativeApp: {
        rooms: {
            title: "Räume",
        },
        bottomNavigation: {
            account: "Profil",
            reloadButton: "Neu laden",
        },
        account: {
            details: "Details",
            logout: "Abmelden",
        },
        settings: {
            tac: "AGB",
            privacy: "Datenschutz",
            imprint: "Impressum",
            app: "Sonstiges",
        },
        renderer: {
            floorWithFocusedRoom: {
                newFloorMessage: "Neues Stockwerk begonnen"
            }
        }
    },
    components: {
        topMenu: {
            offerType: "Was möchtest Du tun?",
            mainType: "Welche Art von Immobilie?",
            nearby: {
                inputPlaceholder: "Ort, Postleitzahl, Landkreis",
                radius: "Umkreis",
            }
        },
        components: {
            aiSearch: {
                buttonLabel: "AI Suche",
            }
        },
        contact: {
            title: "Kontakt",
            subtitle: "Wir freuen uns über ihr Feedback, ihre Fragen und Anregungen. Bitte nutzen Sie das untenstehende Kontaktformular, um uns zu kontaktieren. Bei besonders dringenden Anliegen erreichen Sie uns auch unter der angegebenen Telefonnummer und E-Mail-Adresse.",
            tel: "Tel.:",
            email: "E-Mail:",
            formData: {
                title: "Kontaktformular",
                name: "Ihr Name",
                email: "E-Mail-Adresse",
                message: "Ihre Nachricht",
                registerForUpdates: "Ich möchte über Neuigkeiten informiert werden.",
                contactReason: "Grund der Kontaktaufnahme",
            },
            contactReason: {
                OTHER: "Sonstiges",
                BUSINESS: "Geschäftliche Anfrage",
                FEEDBACK: "Feedback",
                SUPPORT: "Hilfe benötigt",
                GENERAL: "Allgemeine Anfrage",
            },
            submit: "Absenden",
            submitSuccess: "Ihre Nachricht wurde erfolgreich versendet.",
            submitError: "Beim Senden ihrer Nachricht ist ein Fehler aufgetreten. Bitte versuchen Sie es später erneut.",
        },
        facet: {
            searchBox: {
                placeholder: "Filtersuche …",
            },
            distanceFacet: {
                title: "In der Nähe",
                description: "Hiermit kannst Du bestimmen, wie weit ein Ort deines Interesses höchstens von deiner Wunschimmobilie entfernt sein soll.",
                transportType: {
                    WALKING: "zu Fuß",
                    BIKING: "mit dem Fahrrad",
                    DRIVING: "mit dem Auto",
                },
                travelDuration: "{maxDuration} {transportType}",
            },
            facetTypeCheckbox: {
                yes: "Ja",
                no: "Nein",
            },
            facetTypeRangeSliderRead: {
                min: "ab {min}",
                max: "bis {max}",
                minMax: "{min} bis {max}",
            },
            facetSelection: {
                chip: "{key}:",
            },
            showLess: "Weniger anzeigen",
            showMore: "mehr anzeigen",
            showResults: "Ergebnisse anzeigen",
        },
        notFound: {
            title: "Seite nicht gefunden.",
            description: "Die von Ihnen aufgerufene Seite wurde nicht gefunden.",
            backToAccount: "Profil öffnen"
        },
        map: {
            clickToInteract: "Klicken, um mit der Karte zu interagieren."
        },
        layout: {
            app: {
                feedback: {
                    buttonText: "Feedback?",
                    dialog: {
                        title: "Ihr Feedback",
                        description: "Bitte teilen Sie uns mit, was Ihnen gefällt und was wir besser machen können. Oder hat etwas nicht funktioniert? Wir freuen uns über ihr Feedback!",
                        textCaption: "Ihre Nachricht an uns",
                        close: "Schließen",
                        submit: "Absenden",
                        registerForUpdates: "Ich möchte über Neuigkeiten informiert werden.",
                        nameCaption: "Name",
                        emailCaption: "E-Mail",
                        errorMessage: "Beim Senden ihres Feedbacks ist ein Fehler aufgetreten. Bitte versuchen Sie es später erneut.",
                        successTitle: "Vielen Dank für ihr Feedback!",
                        successMessage: "Wir haben ihr Feedback erhalten."
                    }
                }
            },
        },
        listing: {
            labels: {
                hasFloorPlan: "Grundriss",
                has3DModel: "3D",
            },
            list: {
                filter: "Filter",
                filterWithActiveCount: "Filter ({activeCount})",
                results: "Ergebnisse"
            },
            edit: {
                location: {
                    markerInfoStatus: {
                        geolocalization: "Position wird ermittelt …",
                        invalid: "Wähle einen Standort",
                        valid: "Öffentlich sichtbar",
                        obfuscated: "Nicht öffentlich sichtbar"
                    },
                    editAddress: "Adresse bearbeiten",
                    lastAddresses: {
                        single: "Zuletzt gefundene Adresse",
                        multiple: "Zuletzt gefundene Adressen"
                    },
                    obfuscateLocation: {
                        label: "Adresse verbergen?",
                        hint: "Wird die Adresse verborgen, ist sie nicht öffentlich sichtbar. Stattdessen wird ein ungefährer Standort präsentiert.",
                    },
                    geolocalizationError: {
                        permissionDenied: {
                            nativeApp: {
                                goToSettings: {
                                    introduction: "Die Standorterkennung wurde abgelehnt. Bitte erlaube die Standorterkennung in den Einstellungen deines Gerätes.",
                                    button: "App-Einstellungen öffnen"
                                },
                                reloadIntroduction: "Lade die Seite neu, um deine aktualisierten Einstellungen anzuwenden."
                            },
                            reload: {
                                introduction: "Die Standorterkennung wurde abgelehnt. Bitte erlaube die Standorterkennung in den Einstellungen deines Gerätes bzw. deines Browsers. Lade die Seite anschließend neu, um deine aktualisierten Einstellungen anzuwenden. Möglicherweise musst du sogar deinen Browser neustarten.",
                                button: "Seite neu laden"
                            }
                        },
                        positionUnavailable: "Die Standorterkennung ist nicht verfügbar.",
                        timeout: "Die Standorterkennung ist abgelaufen.",
                        unknown: "Ein unbekannter Fehler ist während der Standorterkennung aufgetreten."
                    },
                    confirmButton: "Bestätigen",
                },
                images: {
                    name: "Titel (optional)",
                    delete: "Löschen",
                    deleteImage: {
                        description: "Möchtest Du das Bild wirklich unwiderruflich löschen?",
                        confirm: "Löschen",
                        cancel: "Abbrechen"
                    },
                    statusIndicator: {
                        uploading: "Foto wird hochgeladen …",
                        uploaded: "Erfolgreich hochgeladen",
                        failed: "Fehler beim Hochladen",
                    },
                    imageUploadError: "Upload fehlerhaft. Bitte versuche es später erneut.",
                    isImageUploading: "Upload läuft …",
                    addImage: "Bild(er) hinzufügen",
                    savingSucceeded: "Änderungen gespeichert.",
                    savingFailed: "Fehler beim Speichern der Änderungen.",
                    noImages: "Die Vorschau ist leer. Bitte lade mindestens ein Bild hoch.",
                },
                details: {
                    furnishings: "Ausstattung",
                    otherFurnishings: "Sonstige Ausstattung",
                    outdoors: "Außenbereich",
                    storage: "Abstellfläche",
                    wellbeing: "Erholung",
                    spaceUtilization: "Raumnutzung",
                },
                buildingPlan: {
                    startScannerButton: "Scan starten",
                    completeScanButton: "Scan abschließen",
                    continueScanButton: "Scan fortsetzen",
                    intro: {
                        title: "Gebäude-Scan",
                        description: "Scanne das Gebäude, um gleichzeitig einen Grundriss und ein 3D-Modell zu erstellen. Die Eingabefelder werden im Anschluss automatisch ausgefüllt.",
                        tutorial: {
                            question: "Was muss ich beachten?",
                            steps: {
                                closeWindowsAndDoors: "Schließe alle Fenster und Türen.",
                                lighting: "Sorge für ausreichend Helligkeit.",
                                roomByRoom: "Gehe Stockwerk für Stockwerk und Raum für Raum vor. Flure, Durchbrüche und Übergänge zählen als eigene Räume.",
                                cameraCover: "Verdecke nie die Kamera — auch nicht, während du den Raum wechselst.",
                            },
                            closeDialogButton: "Verstanden"
                        },
                        noLIDAR: "Leider hat dein Gerät keinen eingebauten LiDAR-Sensor. Bitte verwende ein anderes Gerät.",
                        qrCode: {
                            caption: "Scanne den QR-Code mit einem iPhone 12 Pro oder höher oder einem iPad Pro, um mit dem Gebäude-Scan zu beginnen.",
                            error: "Beim Erzeugen des QR-Codes ist ein Fehler aufgetreten.",
                            share: {
                                title: "Gebäude-Scan",
                                message: "Bitte scanne das Gebäude für mein Exposé auf doorbit.",
                            }
                        }
                    },
                },
                energy: {
                    efficiency: "Effizienz",
                    heating: "Heizung",
                    waterHeating: "Warmwasser",
                    ventilationAndTightness: "Belüftung & Dichtigkeit",
                    solar: "PV-Anlage",
                    other: "Sonstige"
                },
                saveListing: "Speichern",
                saveListingSucceeded: "Speichern erfolgreich.",
                saveListingFailed: "Beim Speichern des Exposés ist ein Fehler aufgetreten. Bitte versuche es später erneut.",
                aiText: {
                    generating: "KI-Text wird erzeugt",
                    applyButton: {
                        label: "KI-Text übernehmen",
                        confirmationDialogQuestion: "Möchtest Du wirklich deinen eigenen Text ersetzen?"
                    }
                },
                asteriskText: "Felder, die mit einem * gekennzeichnet sind, müssen ausgefüllt werden.",
            },
            detail: {
                attractivity: {
                    percentageTotal: "Total",
                    attractivityScoreTitle: "Attraktivitätsindex",
                    attractivityScoreDescription: "Die Attraktivität dieser Immobilie wird anhand ihrer Lage, ihrer Lebensqualität, sowie von soziodemografischen und wirtschaftlichen Aspekten bewertet. Je höher der Wert, desto attraktiver ist die Immobilie.",
                    ratingCriteriaDescription: {
                        LOCATION_DAILY_NEEDS: "Die Immobilie befindet sich in der Nähe von Geschäften, Restaurants, Cafés, Bars, Ärzten, Apotheken, Schulen, Kindergärten, etc.",
                        LOCATION_SHOPPING: "Die Immobilie befindet sich in der Nähe von Einkaufsmöglichkeiten.",
                        LOCATION_LEISURE: "Die Immobilie befindet sich in der Nähe von Freizeitangeboten.",
                        LOCATION_TRAFFIC_CONNECTION: "Die Immobilie befindet sich in der Nähe von öffentlichen Verkehrsmitteln.",
                        LIVING_QUALITY_INDOOR: "Die Immobilie verfügt über eine gute Wohnqualität.",
                        LIVING_QUALITY_OUTDOOR: "Die Immobilie verfügt über eine gute Außenqualität.",
                        SOZIODEMOGRAPHY_AGE_DISTRIBUTION_BELOW_50: "Die Immobilie befindet sich in einer Region mit einer jungen Bevölkerung.",
                        SOZIODEMOGRAPHY_AGE_DISTRIBUTION_50_TO_65: "Die Immobilie befindet sich in einer Region mit einer mittelalten Bevölkerung.",
                        SOZIODEMOGRAPHY_CRIME_TREND_5_YEARS: "Die Immobilie befindet sich in einer Region mit einer niedrigen Kriminalitätsrate.",
                        SOZIODEMOGRAPHY_POPULATION_TREND: "Die Immobilie befindet sich in einer Region mit einer wachsenden Bevölkerung.",
                        ECONOMY_UNEMPLOYMENT_RATE: "Die Immobilie befindet sich in einer Region mit einer niedrigen Arbeitslosigkeit.",
                        ECONOMY_UNEMPLOYMENT_RATE_TREND_10_YEARS: "Die Immobilie befindet sich in einer Region mit einer sinkenden Arbeitslosigkeit.",
                        ECONOMY_PER_HEAD_OUTPUT: "Die Immobilie befindet sich in einer Region mit einer hohen Wirtschaftsleistung.",
                        ECONOMY_GROWTH_10_YEARS: "Die Immobilie befindet sich in einer Region mit einer wachsenden Wirtschaftsleistung.",
                        ECONOMY_INCOME: "Die Immobilie befindet sich in einer Region mit einem hohen Durchschnittseinkommen.",
                        ECONOMY_EDUCATION_LEVEL: "Die Immobilie befindet sich in einer Region mit einem hohen Bildungsniveau.",
                    }
                },
                overview: {
                    highlightsTitle: "Highlights",
                    moreInformationTitle: "Weitere Eckdaten",
                    propertyDescriptionTitle: "Beschreibung",
                },
                energy: {
                    noInformation: "Keine Informationen.",
                },
                contact: {
                    vendorDetails: {
                        title: "Informationen über den Anbieter",
                        privateVendor: "privater Anbieter",
                        ratings: "Bewertungen",
                        fields: {
                            EMAIL: "E-Mail Adresse",
                            WEBSITE: "Webseite",
                            TELEPHONE: "Telefonnummer",
                            COMPANY_ADDRESS: "Firmenadresse",
                            VAT_NUMBER: "Umsatzsteuer-ID",
                            PROFESSION: "Berufsbezeichnung",
                            CONSULTANT_NUMBER: "Berater-Nr.",
                        }
                    }
                },
                location: {
                    locationDescriptionTitle: "Lagebeschreibung",
                    pointsOfInterestTitle: "In der Nähe",
                    addressTitle: "Adresse",
                    nearestMetropolitanCitiesTitle: "Nächste Großstadt",
                },
                cta: {
                    contact: "Anfragen",
                    downloadImages: "Download"
                }
            },
            fieldUnitValue: "{value} {unit}",
            buildingModel: {
                backToListingButton: "Zurück",
                floorLevels: "Stockwerk",
                exportDialog: {
                    button: "Export",
                    title: "Export",
                    downloadButton: "Herunterladen",
                    onlyEnergeticRelevantParts: "Nur energetisch relevante Bauteile",
                    onlyEnergeticRelevantPartsFilenameToken: "e. r. Bauteile",
                    optimizeExportFor: "Optimieren für …",
                    pageOrientation: "Seitenorientierung",
                },
                visibilityMenu: {
                    button: "Sichtbarkeiten",
                    items: {
                        outlines: "Umrisse",
                        generic: "Allgemein",
                        roomTexts: "Raumnamen",
                        compass: "Kompass",
                        pointsOfInterest: "Notizen & Fotos",
                        furniture: "Möbel",
                        walls: "Wände",
                        wallWidths: "Wandlängen (㎝)",
                        wallThicknesses: "Wandstärken (㎝)",
                        slabs: "Boden- & Deckenplatten",
                        roofAreas: "Dachflächen",
                        displayIds: "Evebi-IDs",
                    }
                },
            },
            buildingModelPreview: {
                openDetailsButton: "Details"
            },
            print: "Drucken",
            report: "Melden",
            objectDescription: {
                showMore: "Mehr anzeigen",
                showLess: "Weniger anzeigen",
            },
            offer: {
                noEndOfRentDate: "unbefristet",
                hasEndOfRent: "Mietdauer befristet"
            },
            images: {
                more: "Mehr",
                backToListing: "Zurück",
                toggleBlackBackground: "Hintergrund",
            },
            imagesDownload: {
                yourDownloadWillStartShortly: "Dein Download startet in Kürze …"
            },
            adminBoundary: {
                deviationFromAverage: {
                    aboveAverage: "{percent} über dem Durchschnitt",
                    belowAverage: "{percent} unter dem Durchschnitt",
                    average: "im Durchschnitt"
                },
                deviationFromAverageTitle: "Abweichung vom Durchschnitt: {percent}",
                populationGroup: {
                    title: "Siedlungstyp",
                    plural: {
                        METROPOLITAN_CITY: "Großstädten",
                        CITY: "Städten",
                        TOWN: "Kleinstädten",
                        VILLAGE: "Dörfern",
                        HAMLET: "Weilern",
                    }
                },
                areaInSquareKilometers: {
                    title: "Fläche",
                },
                population: {
                    totalPopulation: {
                        title: "Gesamtbevölkerung",
                        subtitle: "Einwohner"
                    },
                    threeYearsTrend: "Zuwachs in den letzten 3 J.",
                    fiveYearsTrend: "Zuwachs in den letzten 5 J.",
                    tenYearsTrend: "Zuwachs in den letzten 10 J.",
                    density: {
                        title: "Bevölkerungsdichte",
                        HIGH: "hoch",
                        LOW: "niedrig",
                        MEDIUM: "mittel",
                        VERY_HIGH: "sehr hoch",
                        VERY_LOW: "sehr niedrig",
                    },
                    ageDistribution: {
                        title: "Altersverteilung",
                        deviationFromAverageDescription: "Die Altersgruppe {ageGroup} liegt {deviation} im Vergleich zu anderen {populationGroup}.",
                    },
                    ageGroup: {
                        BELOW_3: "unter 3 Jahre",
                        FROM_3_TO_6: "3-6 Jahre",
                        FROM_6_TO_15: "6-15 Jahre",
                        FROM_15_TO_18: "15-18 Jahre",
                        FROM_18_TO_25: "18-25 Jahre",
                        FROM_25_TO_30: "25-30 Jahre",
                        FROM_30_TO_40: "30-40 Jahre",
                        FROM_40_TO_50: "40-50 Jahre",
                        FROM_50_TO_60: "50-60 Jahre",
                        ABOVE_60: "über 60 Jahre",
                    }
                },
                income: {
                    title: "Durchschnittseinkommen",
                    deviationFromAverageDescription: "Das Durschnittseinkommen von {income} liegt {deviation} im Vergleich zu anderen {populationGroup}.",

                },
                perHeadOutput: {
                    title: "Wirtschaftsleistung (Zuwachs in den letzten 10 J.)",
                },
                education: {
                    title: "Bildungsniveau",
                    withoutVocationalTraining: "ohne Berufsausbildung",
                    withVocationalTraining: "mit Berufsausbildung",
                    withHigherEducation: "höherer Bildungsabschluss",
                    rest: "Andere"
                },
                unemploymentStatistics: {
                    title: "Arbeitslosigkeit",
                    currentYearTotal: "Arbeitslose aktuell",
                    tenYearsTrend: "Veränderung in den letzten 10 J.",
                },
                crimeStatistics: {
                    title: "Kriminalitätsentwicklung",
                    fiveYearsTrend: "Veränderung in den letzten 5 J.",
                    tenYearsTrend: "Veränderung in den letzten 10 J.",
                }
            },
            attractiveness: {
                title: "Attraktivität",
                description: "Diese Immobilie erreicht mit einer Gesamtpunktzahl von {score} von {achievableScore} erreichbaren Punkten eine Attraktivitätsbewertung von {percentage}.",
                score: "{score}/{achievableScore}",
                poiTooltip: {
                    available: "{poi} im Suchradius von {searchRadius} gefunden",
                    unavailable: "{poi} im Suchradius von {searchRadius} nicht gefunden",
                }
            },
            map: {
                searchInThisArea: "In diesem Kartenausschnitt suchen",
                loadMoreResults: "{count} weitere Ergebnisse anzeigen",
                houseMarkerShowMore: "Klicken für mehr Informationen",
            },
            highlights: {
                availableNow: "ab sofort",
                showMore: "Mehr anzeigen",
                showLess: "Weniger anzeigen",
                highlightsSubtitle: "Highlights",
                listingHighlight: {
                    titleWithFieldname: "{fieldName} {value}",
                },
                locationHighlights: {
                    title: "{poiName} {travelDuration}",
                    titleNoDuration: "{poiName} in der Nähe",
                    transportType: {
                        WALKING: "zu Fuß",
                        BIKING: "mit dem Fahrrad",
                        DRIVING: "mit dem Auto"
                    },
                    atDoorstep: "direkt vor der Haustür",
                    withinWalkingDistance: "fußläufig",
                    maxDurationLessThan: "< {maxDuration} Minuten {transportType}",
                    maxDurationApproximately: "~ {maxDuration} Minuten {transportType}",
                },
            },
            actions: {
                shareButtonMessage: {
                    doorbit: "Schau dir dieses Exposé auf doorbit an.",
                    renaldo: "Schau dir dieses Projekt von renaldo an.",
                },
                editMode: "Editieren",
                viewMode: "Vorschau",

            },
            notFound: {
                title: "Exposé nicht gefunden.",
                description: "Das von dir aufgerufene Exposé wurde nicht gefunden.",
            }
        },
        shareButton: {
            title: "Teilen",
            copy: "Kopieren",
        },
        login: {
            redirectMessage: "Du wirst in Kürze weitergeleitet …"
        },
        logout: {
            redirectMessage: "Du wirst in Kürze weitergeleitet …"
        },
        account: {
            login: "Anmelden",
            logout: "Abmelden",
            reloadFlowConfigsButton: "Neu laden",
            menuSubCategories: {
                "ACCOUNT": "Account",
                "VENDOR": "Anbieter",
                "PROSPECT": "Interessent",
            },
            accountDetails: {
                title: "Profil",
                userProfile: {
                    firstname: "Vorname",
                    lastname: "Nachname",
                    email: "E-Mail",
                    title: "Profildetails",
                    preferredLanguage: {
                        label: "Bevorzugte Sprache",
                        options: {
                            de: "Deutsch",
                            en: "Englisch",
                            es: "Spanisch",
                            ca: "Katalanisch"
                        }
                    },
                    preferredTheme: {
                        label: "Bevorzugtes Theme",
                        options: {
                            SYSTEM_DEFAULT: "Systemeinstellung",
                            LIGHT: "Hell",
                            DARK: "Dunkel"
                        }
                    },
                    companyInformation: {
                        title: "Firmenprofil",
                        address: {
                            title: "Adresse",
                            apartmentNumber: "Adresszusatz",
                            name: "Name",
                            street: "Straße",
                            houseNumber: "Hausnummer",
                            zipCode: "PLZ",
                            city: "Stadt",
                            country: "Land",
                        }
                    },
                    contactInformation: {
                        title: "Kontaktinformationen",
                        type: {
                            DISCORD: "Discord",
                            EMAIL: "E-Mail",
                            FAX: "Fax",
                            LINKED_IN: "LinkedIn",
                            OTHER: "Sonstige",
                            PINTEREST: "Pinterest",
                            SIGNAL: "Signal",
                            SKYPE: "Skype",
                            SNAPCHAT: "Snapchat",
                            TELEGRAM: "Telegram",
                            TELEPHONE: "Telefon",
                            THREEMA: "Threema",
                            TIKTOK: "TikTok",
                            TWITTER: "Twitter",
                            VIBER: "Viber",
                            WEBSITE: "Webseite",
                            XING: "Xing",
                            YOUTUBE: "YouTube",
                        }
                    },
                    additionalInformation: {
                        title: "Zusätzliche Informationen",
                        type: {
                            ABOUT_US: "Über uns",
                            COMPANY_LOGO: "Firmenlogo",
                            COMPANY_NAME: "Firmenname",
                            PROFILE_PICTURE: "Profilbild",
                            SOCIAL_SECURITY_NUMBER: "Sozialversicherungsnummer",
                            VAT_NUMBER: "Umsatzsteuer-ID",
                            PROFESSION: "Berufsbezeichnung",
                            CONSULTANT_NUMBER: "Berater-Nr.",
                        }
                    },
                    save: "Speichern",
                    saveSucceeded: "Deine Änderungen wurden erfolgreich gespeichert.",
                    saveFailed: "Beim Speichern deiner Änderungen ist ein Fehler aufgetreten.",
                    deleteImage: "Bild löschen",
                },
                furtherOptions: {
                    title: "Weitere Optionen",
                    changePassword: "Passwort ändern",
                    deleteAccount: {
                        title: "Account löschen",
                        description: "Möchtest Du deinen Account wirklich löschen? Alle Daten (inklusive Exposés, Lagereporte, usw.) werden unwiderruflich gelöscht.",
                        confirm: "Account löschen",
                        cancel: "Abbrechen",
                        errorTitle: "Account löschen fehlgeschlagen",
                        errorDescription: "Beim Löschen deines Accounts ist ein Fehler aufgetreten. Bitte versuche es später erneut.",
                    },
                }
            },
            accountListings: {
                card: {
                    type: {
                        HOUSE: {
                            DEFAULT: "Haus",
                            SELLING: "Haus verkaufen",
                            RENTING: "Haus vermieten",
                        },
                        APARTMENT: {
                            DEFAULT: "Wohnung",
                            SELLING: "Wohnung verkaufen",
                            RENTING: "Wohnung vermieten",
                        },
                        LAND: {
                            DEFAULT: "Grundstück",
                            SELLING: "Grundstück verkaufen",
                            RENTING: "Grundstück vermieten",
                        }
                    },
                    customStatus: {
                        PLANNED: "Geplant",
                        IN_PROGRESS: "In Bearbeitung",
                        DONE: "Fertig",
                        ARCHIVED: "Archiviert"
                    }
                },
                title: "Exposés",
                description: "Erstelle jetzt ein umfangreiches Exposé, angereichert um Geodaten, Lageinformationen, Attraktivitätsbewertung sowie eines von dir erstellbaren 3D-Grundrisses, binnen weniger Minuten.",
                visits: "Besuche",
                view: "Ansehen",
                edit: "Bearbeiten",
                duplicate: "Duplizieren",
                delete: "Löschen",
                dialog: {
                    duplicate: {
                        title: "Möchtest Du das Projekt wirklich duplizieren?",
                        description: "Erzeugt eine Kopie des Projekts. Alle Daten und Fotos werden übernommen.",
                        confirmButton: "Duplizieren"
                    },
                    delete: {
                        title: "Möchtest Du das Projekt wirklich löschen?",
                        description: "Alle Daten und Fotos werden unwiderruflich gelöscht.",
                        confirmButton: "Löschen"
                    },
                    cancel: "Abbrechen"
                },
                create: "Exposé erstellen",
                remainingDays: {
                    remaining: "Noch",
                    days: "Tage",
                },
                listingFilter: {
                    customStatus: "Status",
                    onlyMyListings: "Nur meine anzeigen",
                    search: "Suche …",
                    zeroSearchResults: "Keine Ergebnisse gefunden, bitte versuche es mit einem anderen Suchbegriff.",
                    showAllButton: "Alle anzeigen",
                    reloadButton: "Neu laden",
                },
            },
            accountBuildings: {
                title: "Gebäude (alt)"
            },
        },
        footer: {
            appRoutes: {
                mobileApps: "Mobile Apps",
                registration: "Registrieren",
                faq: "FAQ",
                contact: "Kontakt",
                privacyPolicy: "Datenschutz",
                imprint: "Impressum",
                attributions: "Quellen",
                termsAndConditions: "AGB",
            },
            socialMediaProfiles: {
                twitter: "Twitter-Profil",
                facebook: "Facebook-Profil",
                linkedin: "LinkedIn-Profil",
            }
        }
    },
    enums: {
        wallType: {
            INTERIOR: "Innenwand | Innenwände",
            EXTERIOR: "Außenwand | Außenwände",
            INTERMEDIATE: "Zwischenwand | Zwischenwände",
        },
        cardinalDirection: {
            NORTH: "N",
            NORTH_EAST: "NO",
            EAST: "O",
            SOUTH_EAST: "SO",
            SOUTH: "S",
            SOUTH_WEST: "SW",
            WEST: "W",
            NORTH_WEST: "NW",
        },
        ifcExportContextTargetCAD: {
            DEFAULT: "Alle CADs",
            ECAD: "E-CAD5",
        },
        pdfExport: {
            label: "Seiten",
            levels: {
                ALL: "Alle Stockwerke",
                CURRENT_FLOOR: "Aktuelles Stockwerk",
            },
            pageOrientation: {
                portrait: "Hochformat",
                landscape: "Querformat",
            }
        },
        buildingExportType: {
            IFC: "IFC: Industry Foundation Classes (.ifc)",
            EVEX: "EVEX: Evebi Projektdatei (.evex)",
            IMAGES: "Fotos (.zip)",
            PDF: "PDF (.pdf)",
        },
        gbXMLExportType: {
            EVEBI: "EVEBI 13+",
        },
        evexExportType: {
            EVEBI: "EVEBI 13+",
        },
        wallOpeningType: {
            OPENING: "Öffnung",
            WINDOW: "Fenster",
            DOOR: "Tür",
        },
        shapeType: {
            Box: "rechteckig",
            Polygon: "schräg",
            Ring: "rund",
        },
        attractivenessCategoryType: {
            LOCATION: "Lage",
            LIVING_QUALITY: "Lebensqualität",
            SOCIODEMOGRAPHY: "Soziodemographie",
            ECONOMY: "Wirtschaft"
        },
        attractivenessCriteriaType: {
            LOCATION_DAILY_NEEDS: "Täglicher Bedarf",
            LOCATION_SHOPPING: "Einkaufsmöglichkeiten",
            LOCATION_LEISURE: "Freizeitaktivitäten",
            LOCATION_TRAFFIC_CONNECTION: "Verkehrsanbindung",
            LIVING_QUALITY_INDOOR: "Indoor-Freizeitangebote",
            LIVING_QUALITY_OUTDOOR: "Outdoor-Freizeitangebote",
            SOZIODEMOGRAPHY_POPULATION_TREND: "Bevölkerungsentwicklung",
            SOZIODEMOGRAPHY_CRIME_TREND_5_YEARS: "Kriminalitätsentwicklung (5 Jahre)",
            SOZIODEMOGRAPHY_AGE_DISTRIBUTION_50_TO_65: "Bevölkerung 50-65 Jahre",
            SOZIODEMOGRAPHY_AGE_DISTRIBUTION_BELOW_50: "Bevölkerung unter 50 Jahren",
            ECONOMY_UNEMPLOYMENT_RATE: "Arbeitslosigkeit",
            ECONOMY_UNEMPLOYMENT_RATE_TREND_10_YEARS: "Entwicklung der Abeitslosigkeit (10 Jahre)",
            ECONOMY_PER_HEAD_OUTPUT: "Wirtschaftsleistung am Standort",
            ECONOMY_GROWTH_10_YEARS: "Wirtschaltsleistungsentwicklung (10 Jahre)",
            ECONOMY_INCOME: "Pro-Kopf-Einkommen",
            ECONOMY_EDUCATION_LEVEL: "Bildungsniveau",
        },
        energyEfficiencyCategoryType: {
            A_PLUS: "A＋",
            A: "A",
            B: "B",
            C: "C",
            D: "D",
            E: "E",
            F: "F",
            G: "G",
            H: "H",
        },
        languageCode: {
            de: "Deutsch",
            en: "English",
            es: "Español",
            ca: "Catalán",
        },
        listingFieldUnit: {
            CENTIMETER: "㎝",
            CUBIC_METER: "m³",
            CURRENCY: "€",
            CURRENCY_PER_SQUARE_METER: "€/m²",
            DEGREE_CELSIUS: "℃",
            DEGREE_OF_ARC: "°",
            KG_CARBON_DI_OXIDE_PER_SQUARE_METER_PER_YEAR: "㎏CO₂/m²a",
            KILOWATT: "㎾",
            KILOWATT_HOUR: "㎾h",
            KILOWATT_HOUR_PER_SQUARE_METER_PER_YEAR: "㎾h/m²a",
            LITER: "l",
            METER: "m",
            PERCENTAGE: "%",
            SQUARE_METER: "m²",
            WATT_PER_SQUARE_METER_PER_KELVIN: "W/m²K",
            YEAR: "a",
        },
        listingRegionType: {
            GERMANY: "In Deutschland",
            SPAIN: "In Spanien",
        },
        pointOfInterestDetailType: {
            HEALTHCARE_SPECIALITY: "Fachrichtung",
            IATA_CODE: "IATA-Code",
            ROLLERCOASTER_COUNT: "Achterbahnen",
            SCHOOL_LEGAL_STATUS: "Form",
            SCHOOL_TYPE: "Typ",
            SHOP_COUNT: "Geschäfte",
            WEBSITE: "Webseite",
            AREA: "Größe",
        },
        pointOfInterestDetailTypeValues: {
            AREA: {
                SMALL_WOOD: "Kleines Waldstück",
                WOOD: "Waldstück",
                FOREST: "Wald"
            }
        },
        pointOfInterestParentGroupType: {
            BASIC_SERVICES: "Grundversorgung",
            SHOPPING: "Einkaufen",
            LEISURE: "Freizeit",
            SPORTS: "Sport",
            TRANSPORT: "Verkehr",
        },
        pointOfInterestType: {
            AMUSEMENT_PARK: "Freizeitpark",
            ANIMAL_TRAINING: "Tierschule",
            AQUARIUM: "Aquarium",
            BAKERY: "Bäckerei",
            BANK: "Bank",
            BAR_PUB: "Bar / Kneipe / Biergarten",
            BATHING_PLACE: "Badestelle",
            BOWLING_ALLEY: "Bowlingbahn",
            BUS_STOP: "Bushaltestelle",
            CHEMIST: "Drogerie",
            CHILDCARE: "Kita & Co.",
            CINEMA: "Kino",
            COASTLINE: "Meeresküste",
            DOCTORS: "Arzt / Praxis",
            DOG_PARK: "Hundeauslauf",
            ELECTRIC_CAR_CHARGER: "E-Ladestation",
            FITNESS_CENTER: "Fitnessstudio",
            FOREST: "Wald",
            FURNITURE_AND_INTERIOR: "Möbel und Deko",
            GARDEN_CENTER: "Gartencenter",
            HAIR_DRESSER: "Friseur",
            HARDWARE_STORE: "Baumarkt",
            HOSPITAL: "Krankenhaus",
            INTERNATIONAL_AIRPORT: "Flughafen",
            KIOSK: "Kiosk",
            LAKE: "See",
            LIBRARY: "Bibliothek",
            MALL: "Einkaufszentrum",
            MARKETPLACE: "Wochenmarkt",
            METROPOLITAN_CITY: "Großstadt",
            MOUNTAIN_RANGE: "Gebirge",
            MUSIC_SCHOOL: "Musikschule",
            NATURE_RESERVE: "Naturschutzgebiet",
            NIGHTCLUB: "Disco",
            PARCEL_LOCKER: "Paketstation",
            PARK: "Park",
            PHARMACY: "Apotheke",
            PLACE_OF_WORSHIP: "Gotteshaus",
            PLAYGROUND: "Spielplatz",
            RESTAURANT_CAFE: "Restaurant / Cafè",
            RIVER: "Fluss",
            SAFETY_AMENITY: "Polizei / Feuerwehr",
            SCHOOL: "Schule",
            SHOPPING_CENTER: "Einkaufsstraße",
            SPORT_BASKETBALL: "Basketballplatz",
            SPORT_GOLF_COURSE: "Golfplatz",
            SPORT_HORSE_RIDING: "Reiterhof",
            SPORT_ICE_RINK: "Eislaufbahn",
            SPORT_PADDLE_SPORTS: "Paddelsport",
            SPORT_SHOOTING: "Schützensport",
            SPORT_SOCCER: "Fußballplatz",
            SPORT_SWIMMING: "Schwimmbad",
            SPORT_TENNIS: "Tennisplatz",
            SPORT_WINTER: "Wintersport",
            SUPERMARKET: "Supermarkt",
            THEATER: "Theater",
            TRAIN_STATION: "Bahnhof",
            UNIVERSITY: "Universität / Hochschule",
            WATER_PARK: "Freizeitbad",
            ZOO: "Zoo"
        },
        populationGroupType: {
            METROPOLITAN_CITY: "Großstadt",
            CITY: "Stadt",
            TOWN: "Kleinstadt",
            VILLAGE: "Dorf",
            HAMLET: "Weiler",
        },
    },
    units: {
        squareMeters: "{squareMeters} Quadratmeter",
        squareMetersShort: "{squareMeters} ㎡",
        rooms: "{roomCount} Zimmer",
        roomsShort: "{roomCount} Zi.",
        currencyPerSquareMeter: "{currency}/㎡",
        squareKilometersShort: "{squareKilometers} ㎢",
    },
    formRules: {
        required: "Dieses Feld muss ausgefüllt werden.",
        email: "Bitte gebe eine gültige E-Mail-Adresse ein.",
        phoneNumber: "Bitte gebe eine gültige Telefonnummer ein.",
    }
};

export default deMessages;
