import {DefineLocaleMessage} from 'vue-i18n';

import {es} from 'vuetify/locale';

const esMessages: DefineLocaleMessage = {
    $vuetify: es,
    delete: {
        confirmationHeading: "¿Estás seguro?",
    },
    network: {
        noInternetConnection: "No hay conexión a Internet",
    },
    dateConverter: {
        timeFormat: "{time}",
        dateTimeFormat: "{date}, {time}",
    },
    theme: {
        components: {
            input: {
                counter: "{counter} / {counterMaximum}",
                autocomplete: {
                    address: {
                        placeholder: "Introduce la dirección …",
                    }
                }
            }
        }
    },
    documentationButton: "Documentación",
    key: {
        control: "Ctrl",
    },
    globalErrorDialog: {
        title: "Error",
        description: {
            noInternet: "No se pudo establecer una conexión con el servidor. Por favor, comprueba tu conexión a Internet e inténtalo de nuevo.",
            newVersion: "Se ha publicado una nueva versión de esta página. El error podría estar relacionado con el uso de una versión anterior. Por favor, recarga la página.",
            unknown: "Se ha producido un error desconocido. Por favor, inténtalo de nuevo más tarde.",
        },
        reloadButton: "Recargar página",
        continueButton: "Continuar",
    },
    serviceWorkerStatusDialog: {
        title: "Modo Offline",
        state: {
            update: "¡Una nueva versión de doorbit está disponible!",
            offline: "Estás sin conexión. Algunas funciones pueden estar limitadas.",
            error: "Se produjo un error y no puedes usar doorbit sin conexión en este momento. Si el problema persiste, prueba la función «Reconfigurar». Si eso no ayuda, reinicia la aplicación o reinstálala. En la web, cierra todas las ventanas y vuelve a abrir doorbit.",
            preparing: "doorbit se está preparando para el modo sin conexión. Esto puede tardar un momento.",
            waiting: "doorbit aún está esperando el momento adecuado para preparar el modo sin conexión. Esto puede tardar un momento.",
            ready: "doorbit está listo para el modo offline. Ahora puedes usar doorbit sin conexión.",
            unauthorized: "No has iniciado sesión. Por favor, inicia sesión para usar doorbit sin conexión.",
        },
        newVersion: {
            updateAvailableButton: "Nueva versión",
            applyUpdateButton: "Actualizar ahora",
        },
        reregisterServiceWorker: {
            button: "Reconfigurar",
            description: "Recarga la página e intenta cargar de nuevo todos los datos necesarios para el modo offline. Por favor, cierra todas las demás pestañas y ventanas antes de continuar (aplica solo a navegadores).",
        },
        clearOfflineData: {
            button: "Eliminar datos sin conexión",
            description: "Elimina todas las fichas guardadas y la cola para operaciones de almacenamiento sin conexión. Esta acción puede resultar en pérdida de datos, así que solo ejecútala si tienes problemas o estás seguro de lo que estás haciendo.",
        },
        queue: "Longitud de la cola: {queueLength}",
    },
    flowConfig: {
        uiElement: {
            array: {
                itemName: "Elemento {number}"
            },
            arrayWrapper: {
                size: "{size} / {sizeMaximum}"
            },
            custom: {
                scanner: {
                    buildingNotScannedYet: "Edificio no escaneado todavía",
                    deleteBuildingDialog: {
                        title: "Eliminar edificio",
                        description: "¿Quieres eliminar el escaneo y el modelo del edificio? Se eliminará de forma irreversible el escaneo completo, todos los datos de los componentes y los cambios realizados en el doorbit Studio en este edificio.",
                        confirm: "Eliminar",
                        cancel: "Cancelar",
                    }
                }
            },
            date: {
                ymdt: {
                    time: "Hora",
                }
            },
            suggestion: {
                currentValue: "Valor actual",
                suggestedValue: "Valor sugerido",
                closeButton: "Cerrar",
                abortButton: "Cancelar",
                applyButton: "Aplicar",
            },
            file: {
                image: {
                    noImagesAvailable: "No hay imágenes disponibles",
                    showAllButton: "Mostrar todo",
                }
            }
        }
    },
    listing: {
        loadingError: {
            message: "Se ha producido un error al cargar. Por favor, comprueba tu conexión e inténtalo de nuevo más tarde.",
            backButton: "Volver",
        },
        edit: {
            backButton: "Volver",
            showButton: "Mostrar",
            duplicateButton: "Duplicar",
            pageIndicator: "{currentPage} / {totalPages}",
            navigateBackwardButton: "Atrás",
            navigateForwardButton: "Siguiente",
            saveButton: "Guardar",
            doneButton: "Hecho",
            unsavedChangesWarning: "Los cambios que realizaste no se guarden. ¿Quieres guardarlos antes de salir?",
        },
        view: {
            backButton: "Volver",
            editButton: "Editar",
        },
        building: {
            creationProcess: {
                loadingBuildingScan: "Cargando escaneo de edificio …",
                buildingScanNotCompleted: "Escaneo de edificio no completado",
                buildingScanNotFound: "Escaneo de edificio no encontrado",
                creatingBuilding: "Creando edificio …",
                optimizeBuilding: "Optimizando edificio …",
            },
            wallType: {
                INTERIOR_WALL: "Pared Interior",
                EXTERIOR_WALL: "Pared Exterior",
                INTERIOR_PARTITION_WALL: "Pared de Separación Interior",
                EXTERIOR_PARTITION_WALL: "Pared de Separación Exterior",
                INTERMEDIATE_WALL: "Pared Intermedia",
            },
            cad: {
                history: {
                    undo: "Deshacer",
                    redo: "Rehacer",
                },
                displayMode: "Modo de visualización",
                renderType: "Visualización",
                doorbitStudioButton: {
                    doorbit: "doorbit Studio",
                    renaldo: "Abrir modelo"
                },
                building: "edificio",
                selection: {
                    evebiDisplayIds: "ID de Evebi | IDs de Evebi",
                    evebiRoomNumbers: "Número de habitación de Evebi | Números de habitación de Evebi",
                    wallRoofPoints: {
                        info: "Al seleccionar los puntos de la superficie del techo, el orden es crucial. Lo mejor es moverse a lo largo del borde de la superficie del techo que se va a crear e intentar no saltarse ningún punto. Puedes seleccionar los puntos en el sentido de las agujas del reloj o en sentido contrario. Una vez que hayas seleccionado los primeros puntos, la superficie del techo se asignará a una habitación. Si seleccionas un punto ya seleccionado de nuevo, se deseleccionará y ya no formará parte de la superficie del techo que se va a crear. Si al seleccionar los puntos no se crea la superficie del techo deseada, comienza de nuevo e intenta dividirla en áreas más pequeñas.",
                        warning: "Por favor, selecciona al menos tres puntos de la superficie del techo para crear una superficie del techo.",
                        deselectButton: "Deseleccionar",
                        createRoofAreaButton: "Crear área de techo",
                    },
                    wall: {
                        evebi: {
                            ringWallWarning: "Las paredes redondas aún no son compatibles en el modo Evebi.",
                            neighborWallThicknessShares: "Participaciones en el grosor de la pared vecina",
                            interiorWidth: "Longitud interior",
                            exteriorWidth: "Longitud exterior",
                            slabHeightShares: "Participaciones en la altura de la placa",
                            slabHeightSharesPolygon: "{percent} de {height}",
                            interiorHeight: "Altura interior",
                            exteriorHeight: "Altura exterior",
                            slabTypes: {
                                FLOOR: "Suelo",
                                CEILING: "Techo",
                            },
                            exteriorWallThickness: "Grosor de la pared exterior",
                            correction: "Ajuste manual",
                            addRelatedWallPlaceholder: "Añadir pared …",
                            description: "Los valores inferiores se utilizan para determinar los desplazamientos de Evebi y las superficies exteriores (superficies brutas) para la exportación de archivos .evex.",
                            info: "Los grosores de las paredes afectan automáticamente a las longitudes exteriores de las paredes exteriores conectadas ortogonalmente. Las alturas de las placas de suelo y techo mantenidas en el diálogo de selección de plantas afectan automáticamente a las alturas de las plantas de las plantas.",
                        },
                        shape: "Forma",
                        isIntermediate: {
                            label: "Intermedia",
                            info: "Las paredes intermedias separan dos edificios/viviendas.",
                        },
                        width: "Ancho",
                        height: "Alto",
                        startAngle: "Ángulo de inicio",
                        endAngle: "Ángulo final",
                        innerRadius: "Radio interior",
                        thickness: "Grosor",
                        editShapeButton: "Editar forma",
                        wallEditorDialog: {
                            title: "Editar forma de pared",
                            leftClickInfo: "Agregar punto",
                            rightClickInfo: "Eliminar punto",
                            resetVerticesButton: "Restablecer",
                            saveVerticesButton: "Guardar",
                        },
                        overwriteMenu: {
                            menuItems: {
                                allOfTypeOnFloor: {
                                    INTERIOR: "Todas las paredes interiores del mismo piso",
                                    INTERMEDIATE: "Todas las paredes intermedias del mismo piso",
                                    EXTERIOR: "Todas las paredes exteriores del mismo piso",
                                },
                                allOnFloor: "Todas las paredes del mismo piso",
                                allOfType: {
                                    INTERIOR: "Todas las paredes interiores",
                                    INTERMEDIATE: "Todas las paredes intermedias",
                                    EXTERIOR: "Todas las paredes exteriores",
                                },
                                all: "Todas las paredes",
                            },
                            variant: {
                                thickness: {
                                    button: "Sobrescribir espesores de pared",
                                    description: "Establece el grosor de las otras paredes en el grosor de esta pared. ¿En qué paredes quieres transferir el grosor?",
                                },
                                height: {
                                    button: "Sobrescribir altura de pared",
                                    description: "Establece la altura de las otras paredes en la altura de esta pared. ¿En qué paredes quieres transferir la altura?",
                                },
                                evebi: {
                                    thickness: {
                                        button: "Sobrescribir espesores de pared",
                                        description: "Establece el grosor de otras paredes en el grosor de esta pared. ¿En qué paredes quieres transferir el grosor?",
                                    },
                                },
                                uValue: {
                                    button: "Sobrescribir U-Valores",
                                    description: "Establece el U-Valor de las otras paredes en el U-Valor de esta pared. ¿En qué paredes quieres transferir el U-Valor?",
                                }
                            },
                        }
                    },
                    opening: {
                        relocateButton: "Mover",
                        type: "Tipo",
                        width: "Ancho",
                        height: "Alto",
                        sillHeight: "Altura del alféizar",
                        overwriteMenu: {
                            menuItems: {
                                allOfTypeOnWall: {
                                    DOOR: "Todas las puertas de la misma pared",
                                    WINDOW: "Todas las ventanas de la misma pared",
                                    OPENING: "Todas las aberturas de la misma pared",
                                },
                                allOfTypeOnFloor: {
                                    DOOR: "Todas las puertas del mismo piso",
                                    WINDOW: "Todas las ventanas del mismo piso",
                                    OPENING: "Todas las aberturas del mismo piso",
                                },
                                allOfType: {
                                    DOOR: "Todas las puertas",
                                    WINDOW: "Todas las ventanas",
                                    OPENING: "Todas las aberturas",
                                },
                            },
                            variant: {
                                uValue: {
                                    button: {
                                        DOOR: "Sobrescribir U-Valores",
                                        WINDOW: "Sobrescribir U-Valores",
                                        OPENING: "Sobrescribir U-Valores",
                                    },
                                    description: {
                                        DOOR: "Establece el U-Valor de otras puertas en el U-Valor de esta puerta. ¿En qué puertas quieres transferir el U-Valor?",
                                        WINDOW: "Establece el U-Valor de otras ventanas en el U-Valor de esta ventana. ¿En qué ventanas quieres transferir el U-Valor?",
                                        OPENING: "Establece el U-Valor de otras aberturas en el U-Valor de esta abertura. ¿En qué aberturas quieres transferir el U-Valor?",
                                    }
                                }
                            },
                        }
                    },
                    floor: {
                        autoRotateButton: "Automático",
                        rotation: "Rotación del piso en el edificio",
                        floorSlabHeight: "Altura de la placa del piso",
                        ceilingSlabHeight: "Altura de la placa del techo",
                    },
                    building: {
                        rotation: "Orientación del edificio hacia el norte",
                    }
                },
                defaultRoomName: "Habitación {roomNumber}",
                componentTypes: {
                    BUILDING: "Edificio | {n} edificios",
                    FLOOR: "Piso | {n} pisos",
                    FURNITURE: "Muebles | {n} muebles",
                    ROOM: "Habitación | {n} habitaciones",
                    WALL_OPENING_DOOR: "Puerta | {n} puertas",
                    WALL_OPENING_OPENING: "Abertura | {n} aberturas",
                    WALL_OPENING_WINDOW: "Ventana | {n} ventanas",
                    WALL: "Pared | {n} paredes",
                    POINT_OF_INTEREST: "Nota | {n} Notas",
                    POINT_OF_INTEREST_PHOTO: "Foto | {n} fotos",
                    WALL_ROOF_POINT: "Punto de superficie del techo | {n} puntos de superficie del techo",
                    ROOF_AREA: "Área de techo | {n} áreas de techo",
                    UNKNOWN: "Desconocido | {n} desconocidos",
                },
                subflow: {
                    deleteButton: "Eliminar {constructionPart}"
                },
                floorLevelInfo: {
                    type: {
                        short: {
                            EG: "PB",
                            UG: "Sótano",
                            OG: "{n}.ps",
                            DG: "Ático",
                        },
                        long: {
                            EG: "Planta baja",
                            UG: "Sótano",
                            OG: "{n}. Piso",
                            DG: "Ático",
                        }
                    }
                },
                toolbar: {
                    mode: {
                        selection: {
                            tooltip: "Seleccionar",
                        },
                        dragAndDrop: {
                            tooltip: "Mover paredes",
                            subItem: {
                                grouped: {
                                    tooltip: "Mover juntas",
                                },
                                ungrouped: {
                                    tooltip: "Mover libremente",
                                }
                            },
                        },
                        wallCreation: {
                            tooltip: "Crear pared",
                        },
                        poiAdding: {
                            tooltip: "Añadir nota",
                        },
                        roofAreaCreation: {
                            tooltip: "Crear área de techo",
                        },
                        openingCreation: {
                            tooltip: "Añadir abertura",
                            subItem: {
                                door: {
                                    tooltip: "Puerta",
                                },
                                window: {
                                    tooltip: "Ventana",
                                },
                                opening: {
                                    tooltip: "Abertura",
                                }
                            }
                        }
                    },
                    selection: {
                        building: {
                            tooltip: "Edificio",
                        },
                    },
                    floors: {
                        addMode: {
                            tooltip: "Añadir pisos",
                        },
                        addFloor: {
                            tooltip: "Añadir piso",
                        }
                    }
                },
            }
        }
    },
    validation: {
        required: "Por favor, introduce un valor.",
        step: "Por favor, introduce un valor entero.",
        lengthMin: "Por favor, introduce un valor de al menos {lengthMin} caracteres.",
        lengthMax: "Por favor, introduce un valor con un máximo de {lengthMax} caracteres.",
        min: "Por favor, introduce un valor mayor o igual a {min}.",
        max: "Por favor, introduce un valor menor o igual a {max}.",
        arraySizeMin: "Por favor, introduce al menos {arraySizeMin} elementos.",
        arraySizeMax: "Por favor, introduce un máximo de {arraySizeMax} elementos.",
    },
    iOSAppClip: {
        success: "Gracias por usar el escáner de edificios.",
        error: "No pudimos guardar tu escaneo. Por favor, inténtalo de nuevo.",
    },
    boolean: {
        yes: "Sí",
        no: "No",
    },
    pullToRefresh: {
        pullToRefresh: "Desliza para actualizar",
        releaseToRefresh: "Suelta para actualizar",
    },
    addressFormat: {
        singleLine: "{street} {houseNumber}, {zipcode} {city}",
        singleLineNoHouseNumber: "{street}, {zipcode} {city}",
        streetAndHouseNumber: "{street} {houseNumber}",
        zipcodeAndCity: "{zipcode} {city}",
    },
    pageTitle: {
        default: {
            doorbit: "doorbit",
            renaldo: "renaldo",
        },
        custom: {
            doorbit: "{title} {'|'} doorbit",
            renaldo: "{title} {'|'} renaldo",
        },
    },
    nativeApp: {
        rooms: {
            title: "Habitaciones",
        },
        bottomNavigation: {
            account: "Perfil",
            reloadButton: "Recargar",
        },
        account: {
            details: "Detalles",
            logout: "Cerrar sesión",
        },
        settings: {
            tac: "Términos y Condiciones",
            privacy: "Política de Privacidad",
            imprint: "Aviso legal",
            app: "Otros",
        },
        renderer: {
            floorWithFocusedRoom: {
                newFloorMessage: "Nuevo piso comenzado",
            }
        }
    },
    components: {
        topMenu: {
            offerType: "Oferta",
            mainType: "Tipo de propiedad",
            nearby: {
                inputPlaceholder: "Calle, código postal, ciudad, distrito",
                radius: "Perímetro",
            }
        },
        components: {
            aiSearch: {
                buttonLabel: "Búsqueda de IA",
            }
        },
        contact: {
            title: "Contacto",
            subtitle: "Esperamos sus comentarios, preguntas y sugerencias. Utilice el formulario de contacto a continuación para ponerse en contacto con nosotros. Para asuntos especialmente urgentes, también puede comunicarse con nosotros en el número de teléfono y la dirección de correo electrónico proporcionados.",
            tel: "Teléfono:",
            email: "Correo electrónico:",
            formData: {
                title: "Formulario de contacto",
                name: "Nombre",
                email: "Correo electrónico",
                message: "Mensaje",
                registerForUpdates: "Regístrese para recibir actualizaciones",
                contactReason: "Motivo del contacto",
            },
            contactReason: {
                OTHER: "Otro",
                BUSINESS: "Negocio",
                FEEDBACK: "Comentarios",
                SUPPORT: "Soporte",
                GENERAL: "General",
            },
            submit: "Enviar",
            submitSuccess: "Su mensaje ha sido enviado. ¡Nos pondremos en contacto con usted lo antes posible!",
            submitError: "Se produjo un error al enviar el formulario. Por favor, inténtelo de nuevo más tarde.",
        },
        facet: {
            searchBox: {
                placeholder: "Buscar",
            },
            distanceFacet: {
                title: "Lugares cercanos",
                description: "Esto le permite determinar la distancia máxima entre un lugar de su interés y la propiedad deseada.",
                transportType: {
                    WALKING: "a pie",
                    BIKING: "en bicicleta",
                    DRIVING: "en coche"
                },
                travelDuration: "{maxDuration} {transportType}",
            },
            facetTypeCheckbox: {
                yes: "Sí",
                no: "No",
            },
            facetTypeRangeSliderRead: {
                min: "de {min}",
                max: "hasta {max}",
                minMax: "de {min} hasta {max}",
            },
            facetSelection: {
                chip: "{key}:",
            },
            showLess: "Mostrar menos",
            showMore: "Mostrar más",
            showResults: "Mostrar resultados",
        },
        notFound: {
            title: "Página no encontrada.",
            description: "La página que ha solicitado no se ha encontrado.",
            backToAccount: "Abrir perfil"
        },
        map: {
            clickToInteract: "Haga clic para interactuar con el mapa."
        },
        layout: {
            app: {
                feedback: {
                    buttonText: "Feedback?",
                    dialog: {
                        title: "Feedback",
                        description: "¿Tiene alguna sugerencia, pregunta o comentario sobre doorbit? ¡Nos encantaría saber de usted!",
                        textCaption: "Mensaje",
                        close: "Cerrar",
                        submit: "Enviar",
                        registerForUpdates: "Regístrese para recibir actualizaciones",
                        nameCaption: "Nombre",
                        emailCaption: "Correo electrónico",
                        errorMessage: "Se produjo un error al enviar el formulario. Por favor, inténtelo de nuevo más tarde.",
                        successTitle: "¡Gracias!",
                        successMessage: "Su mensaje ha sido enviado. ¡Nos pondremos en contacto con usted lo antes posible!",
                    }
                }
            },
        },
        listing: {
            labels: {
                hasFloorPlan: "Plano",
                has3DModel: "3D",
            },
            edit: {
                location: {
                    markerInfoStatus: {
                        geolocalization: "Determinando ubicación …",
                        invalid: "Elige una ubicación",
                        valid: "Visible al público",
                        obfuscated: "No visible al público"
                    },
                    editAddress: "Editar dirección",
                    lastAddresses: {
                        single: "Dirección encontrada recientemente",
                        multiple: "Direcciones encontradas recientemente"
                    },
                    obfuscateLocation: {
                        label: "¿Ocultar dirección?",
                        hint: "Si se oculta la dirección, no estará visible públicamente. En su lugar, se mostrará una ubicación aproximada.",
                    },
                    geolocalizationError: {
                        permissionDenied: {
                            nativeApp: {
                                goToSettings: {
                                    introduction: "Detección de ubicación denegada. Por favor, permite la detección de ubicación en la configuración de tu dispositivo.",
                                    button: "Abrir ajustes de la aplicación"
                                },
                                reloadIntroduction: "Recarga la página para aplicar tus ajustes actualizados."
                            },
                            reload: {
                                introduction: "Detección de ubicación denegada. Por favor, permite la detección de ubicación en la configuración de tu dispositivo o navegador. Recarga la página después de habilitarlo para aplicar tus ajustes actualizados. Tal vez incluso debas reiniciar tu navegador.",
                                button: "Recargar página"
                            }
                        },
                        positionUnavailable: "La detección de ubicación no está disponible.",
                        timeout: "La detección de ubicación ha caducado.",
                        unknown: "Se ha producido un error desconocido durante la detección de ubicación."
                    },
                    confirmButton: "Confirmar",
                },
                images: {
                    savingFailed: "Error al guardar las imágenes.",
                    savingSucceeded: "Imágenes guardadas.",
                    addImage: "Añadir imagen",
                    name: "Nombre (opcional)",
                    delete: "Eliminar",
                    deleteImage: {
                        description: "¿Seguro que quieres eliminar la imagen?",
                        confirm: "Eliminar",
                        cancel: "Cancelar",
                    },
                    statusIndicator: {
                        uploading: "Subiendo imagen …",
                        uploaded: "Imagen subida",
                        failed: "Error al subir imagen",
                    },
                    isImageUploading: "Subiendo imagen …",
                    imageUploadError: "Se produjo un error al subir la imagen.",
                    noImages: "La vista previa está vacía. Por favor, sube al menos una imagen.",
                },
                details: {
                    furnishings: "Muebles",
                    otherFurnishings: "Otros muebles",
                    outdoors: "Aire libre",
                    storage: "Almacenamiento",
                    wellbeing: "Bienestar",
                    spaceUtilization: "Utilización del espacio",
                },
                buildingPlan: {
                    startScannerButton: "Iniciar escaneo",
                    completeScanButton: "Finalizar escaneo",
                    continueScanButton: "Continuar escaneo",
                    intro: {
                        title: "Escaneo de Edificios",
                        description: "Escanea el edificio para crear simultáneamente un plano y un modelo 3D. Los campos de entrada se completarán automáticamente después.",
                        tutorial: {
                            question: "¿Qué debo tener en cuenta?",
                            steps: {
                                closeWindowsAndDoors: "Cierra todas las ventanas y puertas.",
                                lighting: "Asegúrate de tener suficiente iluminación.",
                                roomByRoom: "Recorre piso por piso y habitación por habitación. Los pasillos, aberturas y transiciones se cuentan como habitaciones separadas.",
                                cameraCover: "Nunca cubras la cámara, incluso al cambiar de habitación.",
                            },
                            closeDialogButton: "Entendido"
                        },
                        noLIDAR: "Lamentablemente, tu dispositivo no tiene un sensor LiDAR incorporado. Por favor, utiliza otro dispositivo.",
                        qrCode: {
                            caption: "Escanea el código QR con un iPhone 12 Pro o superior o un iPad Pro para comenzar el escaneo del edificio.",
                            error: "Se produjo un error al generar el código QR.",
                            share: {
                                title: "Escaneo de Edificio",
                                message: "Por favor, escanea el edificio para mi exposición en doorbit.",
                            }
                        }
                    },
                },
                energy: {
                    efficiency: "Eficiencia",
                    heating: "Calefacción",
                    waterHeating: "Agua caliente",
                    solar: "Solar",
                    ventilationAndTightness: "Ventilación y estanqueidad",
                    other: "Otros",
                },
                saveListing: "Guardar",
                saveListingSucceeded: "El anuncio se ha guardado correctamente.",
                saveListingFailed: "Se produjo un error al guardar el anuncio.",
                aiText: {
                    generating: "Se está generando texto con IA",
                    applyButton: {
                        label: "Adoptar texto generado por IA",
                        confirmationDialogQuestion: "¿Estás seguro de que quieres reemplazar tu propio texto?"
                    }
                },
                asteriskText: "Los campos marcados con * son obligatorios.",
            },
            detail: {
                attractivity: {
                    percentageTotal: "total",
                    attractivityScoreTitle: "Puntuación de atractivo",
                    attractivityScoreDescription: "La atractividad de esta propiedad se evalúa en función de su ubicación, su calidad de vida, así como de aspectos socio-demográficos y económicos. Cuanto mayor sea el valor, más atractiva será la propiedad.",
                    ratingCriteriaDescription: {
                        LOCATION_DAILY_NEEDS: "The property is located in the vicinity of daily needs.", // TODO
                        LOCATION_SHOPPING: "The property is located in the vicinity of shopping facilities.", // TODO
                        LOCATION_LEISURE: "The property is located in the vicinity of leisure facilities.", // TODO
                        LOCATION_TRAFFIC_CONNECTION: "The property is located in the vicinity of traffic connections.", // TODO
                        LIVING_QUALITY_INDOOR: "The property has a good indoor quality.", // TODO
                        LIVING_QUALITY_OUTDOOR: "The property has a good outdoor quality.", // TODO
                        SOZIODEMOGRAPHY_AGE_DISTRIBUTION_BELOW_50: "The property is located in a region with a young population.", // TODO
                        SOZIODEMOGRAPHY_AGE_DISTRIBUTION_50_TO_65: "The property is located in a region with a middle-aged population.", // TODO
                        SOZIODEMOGRAPHY_CRIME_TREND_5_YEARS: "The property is located in a region with a low crime rate.", // TODO
                        SOZIODEMOGRAPHY_POPULATION_TREND: "The property is located in a region with a growing population.", // TODO
                        ECONOMY_UNEMPLOYMENT_RATE: "The property is located in a region with a low unemployment rate.", // TODO
                        ECONOMY_UNEMPLOYMENT_RATE_TREND_10_YEARS: "The property is located in a region with a decreasing unemployment rate.", // TODO
                        ECONOMY_PER_HEAD_OUTPUT: "The property is located in a region with a high economic output.", // TODO
                        ECONOMY_GROWTH_10_YEARS: "The property is located in a region with a high economic growth.", // TODO
                        ECONOMY_INCOME: "The property is located in a region with a high income.", // TODO
                        ECONOMY_EDUCATION_LEVEL: "The property is located in a region with a high education level.", // TODO
                    }
                },
                overview: {
                    highlightsTitle: "Destacados",
                    moreInformationTitle: "Más información",
                    propertyDescriptionTitle: "Descripción",
                },
                energy: {
                    noInformation: "No hay información disponible.",
                },
                contact: {
                    vendorDetails: {
                        title: "Información sobre el proveedor",
                        privateVendor: "proveedor privado",
                        ratings: "calificaciones",
                        fields: {
                            EMAIL: "Correo electrónico",
                            WEBSITE: "Sitio web",
                            TELEPHONE: "Teléfono",
                            COMPANY_ADDRESS: "Dirección",
                            VAT_NUMBER: "Número de IVA",
                            PROFESSION: "Profesión",
                            CONSULTANT_NUMBER: "Número de consultor",
                        }
                    }
                },
                location: {
                    locationDescriptionTitle: "Descripción de la ubicación",
                    pointsOfInterestTitle: "Puntos de interés",
                    addressTitle: "Dirección",
                    nearestMetropolitanCitiesTitle: "Ciudades metropolitanas más cercanas",
                },
                cta: {
                    contact: "Contacto",
                    downloadImages: "Download"
                }
            },
            list: {
                filter: "Filtro",
                filterWithActiveCount: "Filtro ({activeCount})",
                results: "resultados",
            },
            fieldUnitValue: "{value} {unit}",
            buildingModel: {
                backToListingButton: "Volver",
                floorLevels: "Planta",
                exportDialog: {
                    button: "Export",
                    title: "Export",
                    downloadButton: "Descargar",
                    onlyEnergeticRelevantParts: "Solo componentes energéticamente relevantes",
                    onlyEnergeticRelevantPartsFilenameToken: "componentes e. r.",
                    optimizeExportFor: "Optimizar exportación para …",
                    pageOrientation: "Orientación de la página",
                },
                visibilityMenu: {
                    button: "Visibilidad",
                    items: {
                        outlines: "Contornos",
                        generic: "General",
                        roomTexts: "Nombres de habitaciones",
                        compass: "Brújula",
                        pointsOfInterest: "Notas y fotos",
                        furniture: "Muebles",
                        walls: "Paredes",
                        wallWidths: "Anchura de las paredes (㎝)",
                        wallThicknesses: "Grosor de las paredes (㎝)",
                        slabs: "Placas de piso y techo",
                        roofAreas: "Áreas de techo",
                        displayIds: "IDs de Evebi"
                    }
                },
            },
            buildingModelPreview: {
                openDetailsButton: "Detalles",
            },
            print: "Imprimir",
            report: "Informar",
            objectDescription: {
                showMore: "Mostrar más",
                showLess: "Mostrar menos",
            },
            offer: {
                noEndOfRentDate: "indefinido",
                hasEndOfRent: "Periodo de alquiler limitado"
            },
            images: {
                more: "Más",
                backToListing: "Volver",
                toggleBlackBackground: "Fondo"
            },
            imagesDownload: {
                yourDownloadWillStartShortly: "Su descarga comenzará en breve …",
            },
            adminBoundary: {
                deviationFromAverage: {
                    aboveAverage: "{porcentaje} por encima de la media",
                    belowAverage: "{porcentaje} por debajo de la media",
                    average: "en promedio"
                },
                deviationFromAverageTitle: "Desviación de la media: {percent}",
                populationGroup: {
                    title: "Grupo de población",
                    plural: {
                        METROPOLITAN_CITY: "grandes ciudades",
                        CITY: "ciudades",
                        TOWN: "pueblos",
                        VILLAGE: "aldeas",
                        HAMLET: "aldehuelas",
                    }
                },
                areaInSquareKilometers: {
                    title: "Área",
                },
                population: {
                    totalPopulation: {
                        title: "Población total",
                        subtitle: "Número"
                    },
                    threeYearsTrend: "Tendencia en los últimos 3 años",
                    fiveYearsTrend: "Tendencia en los últimos 5 años",
                    tenYearsTrend: "Tendencia en los últimos 10 años",
                    density: {
                        title: "Densidad de población",
                        HIGH: "alta",
                        LOW: "baja",
                        MEDIUM: "media",
                        VERY_HIGH: "muy alta",
                        VERY_LOW: "muy baja",
                    },
                    ageDistribution: {
                        title: "Distribución por edades",
                        deviationFromAverageDescription: "El grupo de edad {ageGroup} está {deviation} en comparación con otros {populationGroup}.",
                    },
                    ageGroup: {
                        BELOW_3: "menos de 3 años",
                        FROM_3_TO_6: "3-6 años",
                        FROM_6_TO_15: "6-15 años",
                        FROM_15_TO_18: "15-18 años",
                        FROM_18_TO_25: "18-25 años",
                        FROM_25_TO_30: "25-30 años",
                        FROM_30_TO_40: "30-40 años",
                        FROM_40_TO_50: "40-50 años",
                        FROM_50_TO_60: "50-60 años",
                        ABOVE_60: "más de 60 años",
                    }
                },
                income: {
                    title: "Ingreso promedio",
                    deviationFromAverageDescription: "El ingreso promedio de {ingreso} está {deviation} en comparación con otros {populationGroup}.",

                },
                perHeadOutput: {
                    title: "Producción por habitante (tendencia de 10 años)",
                },
                education: {
                    title: "Nivel de educación",
                    withoutVocationalTraining: "sin formación profesional",
                    withVocationalTraining: "con formación profesional",
                    withHigherEducation: "con educación superior",
                    rest: "otros",
                },
                unemploymentStatistics: {
                    title: "Tasa de desempleo",
                    currentYearTotal: "año actual",
                    tenYearsTrend: "Tendencia en los últimos 10 años",
                },
                crimeStatistics: {
                    title: "Tasa de criminalidad",
                    fiveYearsTrend: "Tendencia en los últimos 5 años",
                    tenYearsTrend: "Tendencia en los últimos 10 años",
                }
            },
            attractiveness: {
                title: "Atractivo",
                description: "Esta propiedad obtiene una calificación de atractivo del {score} de {achievableScore} puntos alcanzables, equivalente al {percentage} por ciento.",
                score: "{score}/{achievableScore}",
                poiTooltip: {
                    available: "{poi} encontrado dentro del radio de búsqueda de {searchRadius}",
                    unavailable: "{poi} no encontrado dentro del radio de búsqueda de {searchRadius}",
                }
            },
            map: {
                searchInThisArea: "Buscar en esta área del mapa",
                loadMoreResults: "Mostrar {count} resultados adicionales",
                houseMarkerShowMore: "Más información",
            },
            highlights: {
                availableNow: "disponible ahora",
                showMore: "Mostrar más",
                showLess: "Mostrar menos",
                highlightsSubtitle: "Destacados",
                listingHighlight: {
                    titleWithFieldname: "{fieldName} {value}",
                },
                locationHighlights: {
                    title: "{poiName} {travelDuration}",
                    titleNoDuration: "{poiName} cercano",
                    transportType: {
                        WALKING: "a pie",
                        BIKING: "en bicicleta",
                        DRIVING: "en coche"
                    },
                    atDoorstep: "en la puerta de entrada", // Wenn ein Highlight in weniger als 2m zu Fuß erreichbar ist
                    withinWalkingDistance: "a poca distancia a pie",
                    maxDurationLessThan: "en menos de {maxDuration} minutos {transportType}",
                    maxDurationApproximately: "en aproximadamente {maxDuration} minutos {transportType}",
                }
            },
            actions: {
                shareButtonMessage: {
                    doorbit: "Mira este anuncio en doorbit.",
                    renaldo: "Mira este anuncio en renaldo.",
                },
                editMode: "Editar",
                viewMode: "Vista",
            },
            notFound: {
                title: "Anuncio no encontrado.",
                description: "El anuncio que intenta acceder no se encuentra disponible.",
            }
        },
        shareButton: {
            title: "Compartir",
            copy: "Copiar",
        },
        login: {
            redirectMessage: "Serás redirigido en breve …"
        },
        logout: {
            redirectMessage: "Serás redirigido en breve …"
        },
        account: {
            login: "Iniciar sesión",
            logout: "Cerrar sesión",
            reloadFlowConfigsButton: "Recargar",
            menuSubCategories: {
                ACCOUNT: "Cuenta",
                VENDOR: "Proveedor",
                PROSPECT: "Cliente",
            },
            accountDetails: {
                title: "Perfil",
                userProfile: {
                    firstname: "Nombre",
                    lastname: "Apellido",
                    email: "Correo electrónico",
                    title: "Título",
                    preferredLanguage: {
                        label: "Idioma preferido",
                        options: {
                            de: "Alemán",
                            en: "Inglés",
                            es: "Español",
                            ca: "Catalán",
                        }
                    },
                    preferredTheme: {
                        label: "Tema preferido",
                        options: {
                            SYSTEM_DEFAULT: "Sistema predeterminado",
                            LIGHT: "Claro",
                            DARK: "Oscuro",
                        }
                    },
                    companyInformation: {
                        title: "Información de la empresa",
                        address: {
                            title: "Dirección",
                            apartmentNumber: "Número de apartamento",
                            name: "Nombre",
                            street: "Calle",
                            houseNumber: "Número",
                            zipCode: "Código postal",
                            city: "Ciudad",
                            country: "País",
                        }
                    },
                    contactInformation: {
                        title: "Información de contacto",
                        type: {
                            DISCORD: "Discord",
                            EMAIL: "Correo electrónico",
                            FAX: "Fax",
                            LINKED_IN: "LinkedIn",
                            OTHER: "Other",
                            PINTEREST: "Pinterest",
                            SIGNAL: "Signal",
                            SKYPE: "Skype",
                            SNAPCHAT: "Snapchat",
                            TELEGRAM: "Telegram",
                            TELEPHONE: "Teléfono",
                            THREEMA: "Threema",
                            TIKTOK: "TikTok",
                            TWITTER: "Twitter",
                            VIBER: "Viber",
                            WEBSITE: "Website",
                            XING: "Xing",
                            YOUTUBE: "YouTube",
                        }
                    },
                    additionalInformation: {
                        title: "Información adicional",
                        type: {
                            ABOUT_US: "Sobre nosotros",
                            COMPANY_LOGO: "Logotipo de la empresa",
                            COMPANY_NAME: "Nombre de la empresa",
                            PROFILE_PICTURE: "Imagen de perfil",
                            SOCIAL_SECURITY_NUMBER: "Número de seguridad social",
                            VAT_NUMBER: "Número de IVA",
                            PROFESSION: "Profesión",
                            CONSULTANT_NUMBER: "Número de consultor",
                        }
                    },
                    save: "Guardar",
                    saveSucceeded: "Los cambios se han guardado correctamente.",
                    saveFailed: "Se produjo un error al guardar los cambios.",
                    deleteImage: "Eliminar imagen",
                },
                furtherOptions: {
                    title: "Opciones adicionales",
                    changePassword: "Cambiar contraseña",
                    deleteAccount: {
                        title: "Eliminar cuenta",
                        description: "¿Realmente quieres eliminar tu cuenta? Todos los datos (incluidos los exposés, informes de ubicación, etc.) se eliminarán de forma irrevocable.",
                        confirm: "Eliminar cuenta",
                        cancel: "Cancelar",
                        errorTitle: "Se produjo un error al eliminar la cuenta.",
                        errorDescription: "Por favor, inténtelo de nuevo más tarde.",
                    },
                }
            },
            accountListings: {
                card: {
                    type: {
                        HOUSE: {
                            DEFAULT: "Casa",
                            SELLING: "Vender la casa",
                            RENTING: "Alquilar la casa",
                        },
                        APARTMENT: {
                            DEFAULT: "Apartamento",
                            SELLING: "Vender apartamento",
                            RENTING: "Alquilar apartamento",
                        },
                        LAND: {
                            DEFAULT: "Terreno",
                            SELLING: "Vender terreno",
                            RENTING: "Alquilar terreno",
                        }
                    },
                    customStatus: {
                        PLANNED: "Planificado",
                        IN_PROGRESS: "En progreso",
                        DONE: "Hecho",
                        ARCHIVED: "Archivado",
                    }
                },
                title: "Anuncios",
                description: "Crea ahora un anuncio completo, enriquecido con geodatos, información de ubicación, evaluación de atractivo y un plano en 3D que puedes crear tú mismo, todo en cuestión de minutos.",
                visits: "Visitas",
                create: "Crear anuncio",
                view: "Mostrar",
                edit: "Editar",
                duplicate: "Duplicar",
                delete: "Eliminar",
                dialog: {
                    duplicate: {
                        title: "¿Quieres duplicar el proyecto?",
                        description: "Se creará una copia del proyecto. Todos los datos y fotos se transferirán.",
                        confirmButton: "Duplicar"
                    },
                    delete: {
                        title: "¿Quieres eliminar el proyecto?",
                        description: "Todos los datos y fotos se eliminarán de forma irrevocable.",
                        confirmButton: "Eliminar"
                    },
                    cancel: "Cancelar",
                },
                remainingDays: {
                    remaining: "Quedan",
                    days: "días",
                },
                listingFilter: {
                    customStatus: "Estado",
                    onlyMyListings: "Mostrar soli mi",
                    search: "Busca …",
                    zeroSearchResults: "No se encontraron resultados para tu búsqueda.",
                    showAllButton: "Mostrar todos",
                    reloadButton: "Recargar",
                },
            },
            accountBuildings: {
                title: "Edificios (viejo)",
            }
        },
        footer: {
            appRoutes: {
                mobileApps: "Aplicaciones móviles",
                registration: "Registrarse",
                faq: "Preguntas frecuentes",
                contact: "Contacto",
                privacyPolicy: "Política de privacidad",
                imprint: "Aviso legal",
                attributions: "Referencias",
                termsAndConditions: "Condiciones generales",
            },
            socialMediaProfiles: {
                twitter: "Twitter",
                facebook: "Facebook",
                linkedin: "LinkedIn",
            }
        }
    },
    enums: {
        wallType: {
            INTERIOR: "Pared interior | Paredes interiores",
            EXTERIOR: "Pared exterior | Paredes exteriores",
            INTERMEDIATE: "Pared intermedia | Paredes intermedias",
        },
        cardinalDirection: {
            NORTH: "N",         // Norte
            NORTH_EAST: "NE",   // Noreste
            EAST: "E",          // Este
            SOUTH_EAST: "SE",   // Sureste
            SOUTH: "S",         // Sur
            SOUTH_WEST: "SO",   // Suroeste
            WEST: "O",          // Oeste
            NORTH_WEST: "NO",   // Noroeste
        },
        ifcExportContextTargetCAD: {
            DEFAULT: "Todos los CADs",
            ECAD: "E-CAD5",
        },
        pdfExport: {
            label: "Páginas",
            levels: {
                ALL: "Todas las páginas",
                CURRENT_FLOOR: "Página actual",
            },
            pageOrientation: {
                portrait: "Vertical",
                landscape: "Horizontal",
            }
        },
        buildingExportType: {
            IFC: "IFC: Industry Foundation Classes (.ifc)",
            EVEX: "EVEX: Evebi Projektdatei (.evex)",
            IMAGES: "Fotos (.zip)",
            PDF: "PDF (.pdf)",
        },
        gbXMLExportType: {
            EVEBI: "EVEBI 13+",
        },
        evexExportType: {
            EVEBI: "EVEBI 13+",
        },
        wallOpeningType: {
            OPENING: "Abertura",
            DOOR: "Puerta",
            WINDOW: "Ventana",
        },
        shapeType: {
            Box: "rectangular",
            Polygon: "inclinado",
            Ring: "redondo",
        },
        attractivenessCategoryType: {
            LOCATION: "Ubicación",
            LIVING_QUALITY: "Calidad de vida",
            SOCIODEMOGRAPHY: "Sociodemografía",
            ECONOMY: "Economía"
        },
        attractivenessCriteriaType: {
            LOCATION_DAILY_NEEDS: "Necesidades diarias",
            LOCATION_SHOPPING: "Compras",
            LOCATION_LEISURE: "Ocio",
            LOCATION_TRAFFIC_CONNECTION: "Conexión de transporte",
            LIVING_QUALITY_INDOOR: "Opciones de ocio en interiores",
            LIVING_QUALITY_OUTDOOR: "Opciones de ocio al aire libre",
            SOZIODEMOGRAPHY_POPULATION_TREND: "Tendencia de población",
            SOZIODEMOGRAPHY_CRIME_TREND_5_YEARS: "Tendencia de criminalidad (5 años)",
            SOZIODEMOGRAPHY_AGE_DISTRIBUTION_50_TO_65: "Población de 50 a 65 años",
            SOZIODEMOGRAPHY_AGE_DISTRIBUTION_BELOW_50: "Población menor de 50 años",
            ECONOMY_UNEMPLOYMENT_RATE: "Tasa de desempleo",
            ECONOMY_UNEMPLOYMENT_RATE_TREND_10_YEARS: "Tendencia de la tasa de desempleo (10 años)",
            ECONOMY_PER_HEAD_OUTPUT: "Producción per cápita",
            ECONOMY_GROWTH_10_YEARS: "Crecimiento económico (10 años)",
            ECONOMY_INCOME: "Ingreso per cápita",
            ECONOMY_EDUCATION_LEVEL: "Nivel educativo",
        },
        energyEfficiencyCategoryType: {
            A_PLUS: "A＋",
            A: "A",
            B: "B",
            C: "C",
            D: "D",
            E: "E",
            F: "F",
            G: "G",
            H: "H",
        },
        listingFieldUnit: {
            CENTIMETER: "㎝",
            CUBIC_METER: "m³",
            CURRENCY: "€",
            CURRENCY_PER_SQUARE_METER: "€/m²",
            DEGREE_CELSIUS: "℃",
            DEGREE_OF_ARC: "°",
            KG_CARBON_DI_OXIDE_PER_SQUARE_METER_PER_YEAR: "㎏CO₂/m²a",
            KILOWATT: "㎾",
            KILOWATT_HOUR: "㎾h",
            KILOWATT_HOUR_PER_SQUARE_METER_PER_YEAR: "㎾h/m²a",
            LITER: "l",
            METER: "m",
            PERCENTAGE: "%",
            SQUARE_METER: "m²",
            WATT_PER_SQUARE_METER_PER_KELVIN: "W/m²K",
            YEAR: "a",
        },
        languageCode: {
            de: "Deutsch",
            en: "English",
            es: "Español",
            ca: "Catalán",
        },
        listingRegionType: {
            GERMANY: "En Alemania",
            SPAIN: "En España",
        },
        pointOfInterestDetailType: {
            HEALTHCARE_SPECIALITY: "Especialidad médica",
            IATA_CODE: "Código IATA",
            ROLLERCOASTER_COUNT: "Número de montañas rusas",
            SCHOOL_LEGAL_STATUS: "Estado legal de la escuela",
            SCHOOL_TYPE: "Tipo de escuela",
            SHOP_COUNT: "Número de tiendas",
            WEBSITE: "Sitio web",
            AREA: "Área",
        },
        pointOfInterestDetailTypeValues: {
            AREA: {
                SMALL_WOOD: "Bosque pequeño",
                WOOD: "Bosque",
                FOREST: "Selva"
            }
        },
        pointOfInterestParentGroupType: {
            BASIC_SERVICES: "Servicios básicos",
            SHOPPING: "Compras",
            LEISURE: "Ocio",
            SPORTS: "Deportes",
            TRANSPORT: "Transporte",
        },
        pointOfInterestType: {
            AMUSEMENT_PARK: "Parque de atracciones",
            ANIMAL_TRAINING: "Entrenamiento de animales",
            AQUARIUM: "Acuario",
            BAKERY: "Panadería",
            BANK: "Banco",
            BAR_PUB: "Bar / Pub / Cervecería",
            BATHING_PLACE: "Lugar de baño",
            BOWLING_ALLEY: "Bolera",
            BUS_STOP: "Parada de autobús",
            CHEMIST: "Farmacia",
            CHILDCARE: "Guardería",
            CINEMA: "Cine",
            COASTLINE: "Línea costera",
            DOCTORS: "Médico",
            DOG_PARK: "Parque para perros",
            ELECTRIC_CAR_CHARGER: "Estación de carga",
            FITNESS_CENTER: "Gimnasio",
            FOREST: "Bosque",
            FURNITURE_AND_INTERIOR: "Muebles y decoración",
            GARDEN_CENTER: "Centro de jardinería",
            HAIR_DRESSER: "Peluquería",
            HARDWARE_STORE: "Tienda de bricolaje",
            HOSPITAL: "Hospital",
            INTERNATIONAL_AIRPORT: "Aeropuerto",
            KIOSK: "Kiosko",
            LAKE: "Lago",
            LIBRARY: "Biblioteca",
            MALL: "Galería comercial",
            MARKETPLACE: "Mercado",
            METROPOLITAN_CITY: "Ciudad metropolitana",
            MOUNTAIN_RANGE: "Cordillera",
            MUSIC_SCHOOL: "Escuela de música",
            NATURE_RESERVE: "Reserva natural",
            NIGHTCLUB: "Discoteca",
            PARCEL_LOCKER: "Taquilla de paquetes",
            PARK: "Parque",
            PHARMACY: "Farmacia",
            PLACE_OF_WORSHIP: "Lugar de culto",
            PLAYGROUND: "Parque infantil",
            RESTAURANT_CAFE: "Restaurante / Cafetería",
            RIVER: "Río",
            SAFETY_AMENITY: "Policía / Bomberos",
            SCHOOL: "Escuela",
            SHOPPING_CENTER: "Centro comercial",
            SPORT_BASKETBALL: "Pista de baloncesto",
            SPORT_GOLF_COURSE: "Campo de golf",
            SPORT_HORSE_RIDING: "Equitación",
            SPORT_ICE_RINK: "Pista de hielo",
            SPORT_PADDLE_SPORTS: "Deportes de remo",
            SPORT_SHOOTING: "Club de tiro",
            SPORT_SOCCER: "Campo de fútbol",
            SPORT_SWIMMING: "Piscina",
            SPORT_TENNIS: "Pista de tenis",
            SPORT_WINTER: "Pista de esquí",
            SUPERMARKET: "Supermercado",
            THEATER: "Teatro",
            TRAIN_STATION: "Estación de tren",
            UNIVERSITY: "Universidad / Instituto",
            WATER_PARK: "Parque acuático",
            ZOO: "Zoológico"
        },
        populationGroupType: {
            METROPOLITAN_CITY: "Ciudad metropolitana",
            CITY: "Ciudad",
            TOWN: "Pueblo",
            VILLAGE: "Pueblo",
            HAMLET: "Aldea",
        },
    },
    units: {
        squareMeters: "{squareMeters} metros cuadrados",
        squareMetersShort: "{squareMeters} ㎡",
        rooms: "{roomCount} habitaciones",
        roomsShort: "{roomCount} hab.",
        currencyPerSquareMeter: "{currency}/㎡",
        squareKilometersShort: "{squareKilometers} ㎢",
    },
    formRules: {
        required: "Este campo es obligatorio.",
        email: "Por favor, introduce una dirección de correo electrónico válida.",
        phoneNumber: "Por favor, introduce un número de teléfono válido.",
    }
};

export default esMessages;
