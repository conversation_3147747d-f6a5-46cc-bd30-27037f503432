import {DefineLocaleMessage} from 'vue-i18n';

import {en} from 'vuetify/locale';

const enMessages: DefineLocaleMessage = {
    $vuetify: en,
    network: {
        noInternetConnection: "No internet connection",
    },
    dateConverter: {
        timeFormat: "{time}",
        dateTimeFormat: "{date}, {time}",
    },
    delete: {
        confirmationHeading: "Are you sure?",
    },
    theme: {
        components: {
            input: {
                counter: "{counter} / {counterMaximum}",
                autocomplete: {
                    address: {
                        placeholder: "Enter address …",
                    }
                }
            }
        }
    },
    documentationButton: "Documentation",
    key: {
        control: "Ctrl",
    },
    globalErrorDialog: {
        title: "Error",
        description: {
            noInternet: "No connection to the server could be established. Please check your internet connection and try again.",
            newVersion: "A new version of this page has been released. The error may be related to your use of an older version. Please reload the page.",
            unknown: "An unknown error occurred. Please try again later.",
        },
        reloadButton: "Reload page",
        continueButton: "Continue"
    },
    flowConfig: {
        uiElement: {
            array: {
                itemName: "Element {number}"
            },
            arrayWrapper: {
                size: "{size} / {sizeMaximum}"
            },
            custom: {
                scanner: {
                    buildingNotScannedYet: "Building not scanned yet",
                    deleteBuildingDialog: {
                        title: "Delete building",
                        description: "Do you really want to delete the building scan and model? The complete scan, all maintained component data, and changes made in the doorbit Studio for this building will be irreversibly deleted.",
                        confirm: "Delete",
                        cancel: "Cancel",
                    }
                }
            },
            date: {
                ymdt: {
                    time: "Time",
                }
            },
            suggestion: {
                currentValue: "Current value",
                suggestedValue: "Suggested value",
                closeButton: "Close",
                abortButton: "Cancel",
                applyButton: "Apply",
            },
            file: {
                image: {
                    noImagesAvailable: "No images available",
                    showAllButton: "Show all",
                }
            }
        }
    },
    listing: {
        loadingError: {
            message: "An error occurred while loading. Please check your connection and try again later.",
            backButton: "Back",
        },
        edit: {
            backButton: "Back",
            showButton: "Show",
            duplicateButton: "Duplicate",
            pageIndicator: "{currentPage} / {totalPages}",
            navigateBackwardButton: "Back",
            navigateForwardButton: "Next",
            saveButton: "Save",
            doneButton: "Done",
            unsavedChangesWarning: "Changes that you made may not be saved. Do you really want to leave this page?",
        },
        view: {
            backButton: "Back",
            editButton: "Edit",
        },
        building: {
            creationProcess: {
                loadingBuildingScan: "Loading building scan …",
                buildingScanNotCompleted: "Building scan not completed",
                buildingScanNotFound: "Building scan not found",
                creatingBuilding: "Creating building …",
                optimizeBuilding: "Optimizing building …",
            },
            wallType: {
                INTERIOR_WALL: "Interior Wall",
                EXTERIOR_WALL: "Exterior Wall",
                INTERIOR_PARTITION_WALL: "Interior Partition Wall",
                EXTERIOR_PARTITION_WALL: "Exterior Partition Wall",
                INTERMEDIATE_WALL: "Intermediate Wall",
            },
            cad: {
                history: {
                    undo: "Undo",
                    redo: "Redo",
                },
                displayMode: "Display mode",
                renderType: "Representation",
                doorbitStudioButton: {
                    doorbit: "doorbit Studio",
                    renaldo: "Open building model"
                },
                building: "building",
                selection: {
                    evebiDisplayIds: "Evebi ID | Evebi IDs",
                    evebiRoomNumbers: "Evebi room number | Evebi room numbers",
                    wallRoofPoints: {
                        info: "When selecting the roof area points, the order is crucial. It is best to move along the edge of the roof area to be created and try not to skip any points. You can select the points clockwise or counterclockwise. As soon as you have selected the first points, the roof area is assigned to a room. If you select a point that has already been selected again, it will be deselected and will no longer be part of the roof area to be created. If the desired roof area is not created when selecting the points, start over and try to divide it into smaller sub-areas.",
                        warning: "Please select at least three roof area points to create a roof area.",
                        deselectButton: "Discard selection",
                        createRoofAreaButton: "Create roof area",
                    },
                    wall: {
                        evebi: {
                            ringWallWarning: "Round walls are not yet supported in Evebi mode.",
                            neighborWallThicknessShares: "Shares of neighbor wall thicknesses",
                            interiorWidth: "Interior length",
                            exteriorWidth: "Exterior length",
                            slabHeightShares: "Shares of slab heights",
                            slabHeightSharesPolygon: "{percent} of {height}",
                            interiorHeight: "Interior height",
                            exteriorHeight: "Exterior height",
                            slabTypes: {
                                FLOOR: "Floor",
                                CEILING: "Ceiling",
                            },
                            exteriorWallThickness: "Exterior wall thickness",
                            correction: "Manual adjustment",
                            addRelatedWallPlaceholder: "Add wall …",
                            description: "The lower entries are used to determine the Evebi offsets as well as exterior surfaces (gross surfaces) for the export of .evex files.",
                            info: "Wall thicknesses automatically affect the exterior lengths of orthogonally connected exterior walls. The floor and ceiling slab heights that can be maintained in the selection dialog of the floors automatically affect the floor heights of the floors.",
                        },
                        shape: "Shape",
                        isIntermediate: {
                            label: "Intermediate",
                            info: "Intermediate walls separate two buildings/apartments from each other.",
                        },
                        width: "Width",
                        height: "Height",
                        startAngle: "Start angle",
                        endAngle: "End angle",
                        innerRadius: "Inner radius",
                        thickness: "Thickness",
                        editShapeButton: "Edit shape",
                        wallEditorDialog: {
                            title: "Edit wall shape",
                            leftClickInfo: "Add point",
                            rightClickInfo: "Remove point",
                            resetVerticesButton: "Reset",
                            saveVerticesButton: "Save",
                        },
                        overwriteMenu: {
                            menuItems: {
                                allOfTypeOnFloor: {
                                    INTERIOR: "All interior walls of the same floor",
                                    INTERMEDIATE: "All intermediate walls of the same floor",
                                    EXTERIOR: "All exterior walls of the same floor",
                                },
                                allOnFloor: "All walls of the same floor",
                                allOfType: {
                                    INTERIOR: "All interior walls",
                                    INTERMEDIATE: "All intermediate walls",
                                    EXTERIOR: "All exterior walls",
                                },
                                all: "All walls",
                            },
                            variant: {
                                thickness: {
                                    button: "Set wall thickness",
                                    description: "Set wall thickness of other walls to the wall thickness of this wall. To which walls would you like to transfer the wall thickness?",
                                },
                                height: {
                                    button: "Set wall height",
                                    description: "Set wall height of other walls to the wall height of this wall. To which walls would you like to transfer the wall height?",
                                },
                                evebi: {
                                    thickness: {
                                        button: "Override exterior wall thicknesses",
                                        description: "Set exterior wall thicknesses of other walls to the exterior wall thickness of this wall. To which walls would you like to transfer the exterior wall thickness?",
                                    },
                                },
                                uValue: {
                                    button: "Override U-values",
                                    description: "Set U-values of other walls to the U-value of this wall. To which walls would you like to transfer the U-value?",
                                }
                            },
                        }
                    },
                    opening: {
                        relocateButton: "Relocate",
                        type: "Type",
                        width: "Width",
                        height: "Height",
                        sillHeight: "Sill height",
                        overwriteMenu: {
                            menuItems: {
                                allOfTypeOnWall: {
                                    DOOR: "All doors of the same wall",
                                    WINDOW: "All windows of the same wall",
                                    OPENING: "All openings of the same wall",
                                },
                                allOfTypeOnFloor: {
                                    DOOR: "All doors of the same floor",
                                    WINDOW: "All windows of the same floor",
                                    OPENING: "All openings of the same floor",
                                },
                                allOfType: {
                                    DOOR: "All doors",
                                    WINDOW: "All windows",
                                    OPENING: "All openings",
                                },
                            },
                            variant: {
                                uValue: {
                                    button: {
                                        DOOR: "Override U-values",
                                        WINDOW: "Override U-values",
                                        OPENING: "Override U-values",
                                    },
                                    description: {
                                        DOOR: "Set U-values of other doors to the U-value of this door. To which doors would you like to transfer the U-value?",
                                        WINDOW: "Set U-values of other windows to the U-value of this window. To which windows would you like to transfer the U-value?",
                                        OPENING: "Set U-values of other openings to the U-value of this opening. To which openings would you like to transfer the U-value?",
                                    }
                                }
                            },
                        }
                    },
                    floor: {
                        autoRotateButton: "Automatic",
                        rotation: "Rotation of the floor in the building",
                        floorSlabHeight: "Floor slab height",
                        ceilingSlabHeight: "Ceiling slab height",
                    },
                    building: {
                        rotation: "Building orientation to the north",
                    }
                },
                defaultRoomName: "Room {roomNumber}",
                componentTypes: {
                    BUILDING: "Building | {n} buildings",
                    FLOOR: "Floor | {n} floors",
                    FURNITURE: "Furniture | {n} furniture",
                    ROOM: "Room | {n} rooms",
                    WALL_OPENING_DOOR: "Door | {n} doors",
                    WALL_OPENING_OPENING: "Opening | {n} openings",
                    WALL_OPENING_WINDOW: "Window | {n} windows",
                    WALL: "Wall | {n} walls",
                    POINT_OF_INTEREST: "Note | {n} Notes",
                    POINT_OF_INTEREST_PHOTO: "Photo | {n} photos",
                    WALL_ROOF_POINT: "Roof area point | {n} roof area points",
                    ROOF_AREA: "Roof area | {n} roof areas",
                    UNKNOWN: "Unknown | {n} unknown",
                },
                subflow: {
                    deleteButton: "Delete {constructionPart}"
                },
                floorLevelInfo: {
                    type: {
                        short: {
                            EG: "gf",
                            UG: "bsmt | 1.bsmt | {n}.bsmt",
                            OG: "{n}.fl",
                            DG: "att.",
                        },
                        long: {
                            EG: "Ground floor",
                            UG: "Basement | 1st basement | 2nd basement | {n}. basement",
                            OG: "{n}. Floor",
                            DG: "Attic",
                        }
                    }
                },
                toolbar: {
                    mode: {
                        selection: {
                            tooltip: "Select",
                        },
                        dragAndDrop: {
                            tooltip: "Move walls",
                            subItem: {
                                grouped: {
                                    tooltip: "Move together",
                                },
                                ungrouped: {
                                    tooltip: "Move freely",
                                }
                            },
                        },
                        wallCreation: {
                            tooltip: "Create wall",
                        },
                        poiAdding: {
                            tooltip: "Add note",
                        },
                        roofAreaCreation: {
                            tooltip: "Create roof area",
                        },
                        openingCreation: {
                            tooltip: "Add opening",
                            subItem: {
                                door: {
                                    tooltip: "Door",
                                },
                                window: {
                                    tooltip: "Window",
                                },
                                opening: {
                                    tooltip: "Opening",
                                }
                            }
                        }
                    },
                    selection: {
                        building: {
                            tooltip: "Building",
                        },
                    },
                    floors: {
                        addMode: {
                            tooltip: "Add floors",
                        },
                        addFloor: {
                            tooltip: "Add floor",
                        }
                    }
                },
            }
        }
    },
    validation: {
        required: "Please provide a value.",
        step: "Please provide an integer.",
        lengthMin: "Please provide a value of at least {lengthMin} characters.",
        lengthMax: "Please provide a value of up to {lengthMax} characters.",
        min: "Please provide a value greater than or equal to {min}.",
        max: "Please provide a value less than or equal to {max}.",
        arraySizeMin: "Please provide at least {arraySizeMin} elements.",
        arraySizeMax: "Please provide a maximum of {arraySizeMax} elements.",
    },
    boolean: {
        yes: "yes",
        no: "no"
    },
    iOSAppClip: {
        success: "Thank you for using the building scanner.",
        error: "We could not save your scan. Please try again.",
    },
    addressFormat: {
        singleLine: "{houseNumber} {street}, {city}, {zipcode}",
        singleLineNoHouseNumber: "{street}, {city}, {zipcode}",
        streetAndHouseNumber: "{houseNumber} {street}",
        zipcodeAndCity: "{city}, {zipcode}",
    },
    pullToRefresh: {
        pullToRefresh: "Pull to refresh",
        releaseToRefresh: "Release to refresh",
    },
    nativeApp: {
        rooms: {
            title: "Rooms",
        },
        bottomNavigation: {
            account: "Account",
            reloadButton: "Reload",
        },
        account: {
            details: "Details",
            logout: "Logout",
        },
        settings: {
            tac: "Terms and Conditions",
            privacy: "Privacy Policy",
            imprint: "Imprint",
            app: "Other",
        },
        renderer: {
            floorWithFocusedRoom: {
                newFloorMessage: "New floor started",
            }
        }
    },
    pageTitle: {
        default: {
            doorbit: "doorbit",
            renaldo: "renaldo",
        },
        custom: {
            doorbit: "{title} {'|'} doorbit",
            renaldo: "{title} {'|'} renaldo",
        },
    },
    serviceWorkerStatusDialog: {
        title: "Offline Mode",
        state: {
            update: "A new version of doorbit is available!",
            offline: "You are offline. Some features may be limited.",
            error: "An error occurred and you cannot use doorbit offline at the moment. If the problem persists, try the “Reconfigure” function. If that doesn't help, restart the app or reinstall it. On the web, close all windows and reload doorbit.",
            preparing: "doorbit is being prepared for offline mode. This may take a moment.",
            waiting: "doorbit is still waiting for the right moment to prepare the offline mode. This may take a moment.",
            ready: "doorbit is ready for offline mode. You can now use doorbit offline.",
            unauthorized: "You are not logged in. Please log in to use doorbit offline.",
        },
        newVersion: {
            updateAvailableButton: "New version",
            applyUpdateButton: "Update now",
        },
        reregisterServiceWorker: {
            button: "Reconfigure",
            description: "Reloads the page and attempts to reload all necessary data for offline mode. Please close all other tabs and windows before you proceed (applies to browsers only).",
        },
        clearOfflineData: {
            button: "Delete Offline Data",
            description: "Deletes all saved listings and the queue for offline storage operations. This action can result in data loss, so only perform it if you are experiencing issues or are sure of what you are doing.",
        },
        queue: "Queue length: {queueLength}",
    },
    components: {
        topMenu: {
            offerType: "What do you want to do?",
            mainType: "What kind of property is it?",
            nearby: {
                inputPlaceholder: "Zipcode, City, Region",
                radius: "Radius",
            }
        },
        components: {
            aiSearch: {
                buttonLabel: "AI Search",
            }
        },
        contact: {
            title: "Contact",
            subtitle: "We look forward to your feedback, questions and suggestions. Please use the contact form below to contact us. For particularly urgent matters, you can also reach us at the telephone number and email address provided.",
            tel: "tel:",
            email: "email:",
            formData: {
                title: "Contact form",
                name: "Name",
                email: "E-Mail",
                message: "Your message",
                registerForUpdates: "I would like to be informed about news.",
                contactReason: "Reason for contact",
            },
            contactReason: {
                OTHER: "Other",
                BUSINESS: "Business",
                FEEDBACK: "Feedback",
                SUPPORT: "Support",
                GENERAL: "General",
            },
            submit: "Submit",
            submitSuccess: "Your message has been sent successfully.",
            submitError: "An error occurred while sending your message. Please try again later.",
        },
        facet: {
            searchBox: {
                placeholder: "Search a filter …",
            },
            distanceFacet: {
                title: "Nearby",
                description: "This allows you to determine the maximum distance between a place of your interest and your desired property.",
                transportType: {
                    WALKING: "on foot",
                    BIKING: "by bike",
                    DRIVING: "by car",
                },
                travelDuration: "{maxDuration} {transportType}",
            },
            facetTypeCheckbox: {
                yes: "Yes",
                no: "No",
            },
            facetTypeRangeSliderRead: {
                // A range slider for search filter navigation. Like a price range slider 5 € to 500 €.
                min: "from {min}",
                max: "to {max}",
                minMax: "{min} to {max}",
            },
            facetSelection: {
                chip: "{key}:",
            },
            showLess: "Show less",
            showMore: "Show more",
            showResults: "Show results",
        },
        notFound: {
            title: "Not found.",
            description: "The page you requested could not be found.",
            backToAccount: "Open Account"
        },
        map: {
            clickToInteract: "Click to interact",
        },
        layout: {
            app: {
                feedback: {
                    buttonText: "Feedback?",
                    dialog: {
                        title: "Your feedback",
                        description: "Please tell us what you like and what we can do better. Or did something not work? We appreciate your feedback!",
                        textCaption: "Your message",
                        close: "Close",
                        submit: "Submit",
                        registerForUpdates: "I would like to be informed about news.",
                        nameCaption: "Name",
                        emailCaption: "E-Mail",
                        errorMessage: "An error occurred while sending your feedback. Please try again later.",
                        successTitle: "Thank you for your feedback!",
                        successMessage: "Your feedback has been sent successfully.",
                    }
                }
            },
        },
        listing: {
            labels: {
                hasFloorPlan: "Floor plan",
                has3DModel: "3D",
            },
            edit: {
                location: {
                    markerInfoStatus: {
                        geolocalization: "Determining location …",
                        invalid: "Choose a location",
                        valid: "Publicly visible",
                        obfuscated: "Not publicly visible"
                    },
                    editAddress: "Edit address",
                    lastAddresses: {
                        single: "Recently found address",
                        multiple: "Recently found addresses",
                    },
                    obfuscateLocation: {
                        label: "Hide address?",
                        hint: "If the address is hidden, it won't be publicly visible. Instead, an approximate location will be displayed.",
                    },
                    geolocalizationError: {
                        permissionDenied: {
                            nativeApp: {
                                goToSettings: {
                                    introduction: "Location detection denied. Please allow location detection in your device settings.",
                                    button: "Open app settings"
                                },
                                reloadIntroduction: "Reload the page to apply your updated settings."
                            },
                            reload: {
                                introduction: "Location detection denied. Please allow location detection in your device or browser settings. Reload the page after enabling this to apply your updated settings. Maybe you'll have to restart your browser.",
                                button: "Reload page"
                            }
                        },
                        positionUnavailable: "Location detection is not available.",
                        timeout: "Location detection has expired.",
                        unknown: "An unknown error occurred during location detection."
                    },
                    confirmButton: "Confirm",
                },
                images: {
                    savingFailed: "Save images failed.",
                    savingSucceeded: "Images saved.",
                    addImage: "Add image",
                    name: "Name (optional)",
                    delete: "Delete",
                    deleteImage: {
                        description: "Do you really want to delete the image? This cannot be undone.",
                        confirm: "Delete",
                        cancel: "Cancel",
                    },
                    statusIndicator: {
                        uploading: "Uploading image …",
                        uploaded: "Image uploaded",
                        failed: "Image upload failed",
                    },
                    isImageUploading: "Uploading image …",
                    imageUploadError: "An error occurred while uploading the image.",
                    noImages: "The preview is empty. Please upload at least one image.",
                },
                details: {
                    furnishings: "Furnishings",
                    otherFurnishings: "Other furnishings",
                    outdoors: "Outdoors",
                    storage: "Storage",
                    wellbeing: "Wellbeing",
                    spaceUtilization: "Space utilization",
                },
                buildingPlan: {
                    startScannerButton: "Start scan",
                    completeScanButton: "Complete scan",
                    continueScanButton: "Continue scan",
                    intro: {
                        title: "Building Scan",
                        description: "Scan the building to simultaneously create a floor plan and a 3D model. The input fields will be automatically filled out afterward.",
                        tutorial: {
                            question: "What do I need to consider?",
                            steps: {
                                closeWindowsAndDoors: "Close all windows and doors.",
                                lighting: "Ensure sufficient brightness.",
                                roomByRoom: "Go floor by floor and room by room. Hallways, openings, and transitions count as separate rooms.",
                                cameraCover: "Never cover the camera — not even while changing rooms.",
                            },
                            closeDialogButton: "Understood"
                        },
                        noLIDAR: "Unfortunately, your device does not have a built-in LiDAR sensor. Please use a different device.",
                        qrCode: {
                            caption: "Scan the QR code with an iPhone 12 Pro or higher or an iPad Pro to begin the building scan.",
                            error: "An error occurred while generating the QR code.",
                            share: {
                                title: "Building Scan",
                                message: "Please scan the building for my listing on doorbit.",
                            }
                        }
                    },
                },
                energy: {
                    efficiency: "Efficiency",
                    ventilationAndTightness: "Ventilation and tightness",
                    heating: "Heating",
                    solar: "Solar",
                    waterHeating: "Water heating",
                    other: "Other"
                },
                saveListing: "Save",
                saveListingSucceeded: "Your listing has been saved successfully.",
                saveListingFailed: "An error occurred while saving your listing. Please try again later.",
                aiText: {
                    generating: "AI text is being generated",
                    applyButton: {
                        label: "Adopt AI-generated text",
                        confirmationDialogQuestion: "Are you sure you want to replace your own text?"
                    }
                },
                asteriskText: "Fields marked with * are required.",
            },
            list: {
                filter: "Filter",
                filterWithActiveCount: "Filter ({activeCount})",
                results: "results"
            },
            detail: {
                attractivity: {
                    percentageTotal: "total",
                    attractivityScoreTitle: "Attractivity score",
                    attractivityScoreDescription: "The attractiveness of this property is assessed based on its location, its quality of life, as well as socio-demographic and economic aspects. The higher the value, the more attractive the property is.",
                    ratingCriteriaDescription: {
                        LOCATION_DAILY_NEEDS: "The property is located in the vicinity of daily needs.", // TODO
                        LOCATION_SHOPPING: "The property is located in the vicinity of shopping facilities.", // TODO
                        LOCATION_LEISURE: "The property is located in the vicinity of leisure facilities.", // TODO
                        LOCATION_TRAFFIC_CONNECTION: "The property is located in the vicinity of traffic connections.", // TODO
                        LIVING_QUALITY_INDOOR: "The property has a good indoor quality.", // TODO ...
                        LIVING_QUALITY_OUTDOOR: "The property has a good outdoor quality.", // TODO
                        SOZIODEMOGRAPHY_AGE_DISTRIBUTION_BELOW_50: "The property is located in a region with a young population.", // TODO
                        SOZIODEMOGRAPHY_AGE_DISTRIBUTION_50_TO_65: "The property is located in a region with a middle-aged population.", // TODO
                        SOZIODEMOGRAPHY_CRIME_TREND_5_YEARS: "The property is located in a region with a low crime rate.", // TODO
                        SOZIODEMOGRAPHY_POPULATION_TREND: "The property is located in a region with a growing population.", // TODO
                        ECONOMY_UNEMPLOYMENT_RATE: "The property is located in a region with a low unemployment rate.", // TODO
                        ECONOMY_UNEMPLOYMENT_RATE_TREND_10_YEARS: "The property is located in a region with a decreasing unemployment rate.", // TODO
                        ECONOMY_PER_HEAD_OUTPUT: "The property is located in a region with a high economic output.", // TODO
                        ECONOMY_GROWTH_10_YEARS: "The property is located in a region with a high economic growth.", // TODO
                        ECONOMY_INCOME: "The property is located in a region with a high income.", // TODO
                        ECONOMY_EDUCATION_LEVEL: "The property is located in a region with a high education level.", // TODO
                    }
                },
                overview: {
                    highlightsTitle: "Highlights",
                    moreInformationTitle: "More information",
                    propertyDescriptionTitle: "Description",
                },
                energy: {
                    noInformation: "No information.",
                },
                contact: {
                    vendorDetails: {
                        title: "Information about the vendor",
                        privateVendor: "private vendor",
                        ratings: "ratings",
                        fields: {
                            EMAIL: "E-Mail",
                            WEBSITE: "Website",
                            TELEPHONE: "Telephone",
                            COMPANY_ADDRESS: "Company address",
                            VAT_NUMBER: "VAT",
                            PROFESSION: "Profession",
                            CONSULTANT_NUMBER: "Consultant number",
                        }
                    }
                },
                location: {
                    locationDescriptionTitle: "Location description",
                    pointsOfInterestTitle: "Points of interest",
                    addressTitle: "Address",
                    nearestMetropolitanCitiesTitle: "Nearest metropolitan city",
                },
                cta: {
                    contact: "Contact",
                    downloadImages: "Download"
                }
            },
            fieldUnitValue: "{value} {unit}",
            buildingModel: {
                backToListingButton: "Back",
                floorLevels: "Levels",
                exportDialog: {
                    button: "Export",
                    title: "Export",
                    downloadButton: "Download",
                    onlyEnergeticRelevantParts: "Only energetically relevant components",
                    onlyEnergeticRelevantPartsFilenameToken: "e. r. components",
                    optimizeExportFor: "Optimize export for …",
                    pageOrientation: "Page orientation",
                },
                visibilityMenu: {
                    button: "Visibilities",
                    items: {
                        outlines: "Outlines",
                        generic: "Generic",
                        roomTexts: "Room names",
                        compass: "Compass",
                        pointsOfInterest: "Notes & photos",
                        furniture: "Furniture",
                        walls: "Walls",
                        wallWidths: "Wall widths (㎝)",
                        wallThicknesses: "Wall thicknesses (㎝)",
                        slabs: "Floor & ceiling slabs",
                        roofAreas: "Roof areas",
                        displayIds: "Evebi IDs",
                    }
                },
            },
            buildingModelPreview: {
                openDetailsButton: "Details",
            },
            print: "Print",
            report: "Report",
            objectDescription: {
                showMore: "Show More",
                showLess: "Show Less",
            },
            offer: {
                noEndOfRentDate: "unlimited",
                hasEndOfRent: "Limited rental period"
            },
            images: {
                more: "More",
                backToListing: "Back",
                toggleBlackBackground: "Background",
            },
            imagesDownload: {
                yourDownloadWillStartShortly: "Your download will start shortly …",
            },
            adminBoundary: {
                deviationFromAverage: {
                    aboveAverage: "{percent} above average",
                    belowAverage: "{percent} below average",
                    average: "average"
                },
                deviationFromAverageTitle: "Deviation from average: {percent}",
                populationGroup: {
                    title: "Population Group",
                    plural: {
                        METROPOLITAN_CITY: "Metropolitan Cities",
                        CITY: "Cities",
                        TOWN: "Towns",
                        VILLAGE: "Villages",
                        HAMLET: "Hamlets"
                    }
                },
                areaInSquareKilometers: {
                    title: "Area"
                },
                population: {
                    totalPopulation: {
                        title: "Total Population",
                        subtitle: "Number"
                    },
                    threeYearsTrend: "Trend in the Last 3 Years",
                    fiveYearsTrend: "Trend in the Last 5 Years",
                    tenYearsTrend: "Trend in the Last 10 Years",
                    density: {
                        title: "Population Density",
                        HIGH: "high",
                        LOW: "low",
                        MEDIUM: "medium",
                        VERY_HIGH: "very high",
                        VERY_LOW: "very low"
                    },
                    ageDistribution: {
                        title: "Age Distribution",
                        deviationFromAverageDescription: "The age group {ageGroup} is {deviation} compared to other {populationGroup}."
                    },
                    ageGroup: {
                        BELOW_3: "under 3 years",
                        FROM_3_TO_6: "3-6 years",
                        FROM_6_TO_15: "6-15 years",
                        FROM_15_TO_18: "15-18 years",
                        FROM_18_TO_25: "18-25 years",
                        FROM_25_TO_30: "25-30 years",
                        FROM_30_TO_40: "30-40 years",
                        FROM_40_TO_50: "40-50 years",
                        FROM_50_TO_60: "50-60 years",
                        ABOVE_60: "over 60 years"
                    }
                },
                income: {
                    title: "Average Income",
                    deviationFromAverageDescription: "The average income of {income} is {deviation} compared to other {populationGroup}."
                },
                perHeadOutput: {
                    title: "Economic Output (10 Years trend)",
                },
                education: {
                    title: "Education Level",
                    withoutVocationalTraining: "without vocational training",
                    withVocationalTraining: "with vocational training",
                    withHigherEducation: "higher education degree",
                    rest: "Other"
                },
                unemploymentStatistics: {
                    title: "Unemployment Rate",
                    currentYearTotal: "current year",
                    tenYearsTrend: "Trend in the Last 10 Years"
                },
                crimeStatistics: {
                    title: "Crime Rate",
                    fiveYearsTrend: "Trend in the Last 5 Years",
                    tenYearsTrend: "Trend in the Last 10 Years"
                }
            },
            attractiveness: {
                title: "Attraction",
                description: "This property achieves an attractiveness rating of {percentage} with a total score of {score} out of {achievableScore} possible points.",
                score: "{score}/{achievableScore}",
                poiTooltip: {
                    available: "{poi} found within a search radius of {searchRadius}",
                    unavailable: "{poi} not found within a search radius of {searchRadius}"
                }
            },
            map: {
                searchInThisArea: "Search in this map area",
                loadMoreResults: "Show {count} more results",
                houseMarkerShowMore: "Click for more information"
            },
            highlights: {
                availableNow: "from today",
                showMore: "Show More",
                showLess: "Show Less",
                highlightsSubtitle: "Highlights",
                listingHighlight: {
                    titleWithFieldname: "{fieldName} {value}"
                },
                locationHighlights: {
                    title: "{poiName} {travelDuration}",
                    titleNoDuration: "{poiName} Nearby",
                    transportType: {
                        WALKING: "on foot",
                        BIKING: "by bike",
                        DRIVING: "by car"
                    },
                    atDoorstep: "at your doorstep",
                    withinWalkingDistance: "within walking distance",
                    maxDurationLessThan: "in less than {maxDuration} minutes by {transportType}",
                    maxDurationApproximately: "in about {maxDuration} minutes by {transportType}"
                }
            },
            actions: {
                shareButtonMessage: {
                    doorbit: "Check out this listing on doorbit.",
                    renaldo: "Check out this project by renaldo.",
                },
                editMode: "Edit",
                viewMode: "View",
            },
            notFound: {
                title: "Listing not found.",
                description: "The listing you requested could not be found.",
            }
        },
        shareButton: {
            title: "Share",
            copy: "Copy",
        },
        login: {
            redirectMessage: "You will be redirected shortly …"
        },
        logout: {
            redirectMessage: "You will be redirected shortly …"
        },
        account: {
            login: "Login",
            logout: "Logout",
            reloadFlowConfigsButton: "Reload",
            menuSubCategories: {
                ACCOUNT: "Account",
                VENDOR: "Vendor",
                PROSPECT: "Prospect",
            },
            accountDetails: {
                title: "Profile",
                userProfile: {
                    firstname: "First name",
                    lastname: "Last name",
                    email: "E-Mail",
                    title: "Profile details",
                    preferredLanguage: {
                        label: "Preferred language",
                        options: {
                            de: "German",
                            en: "English",
                            es: "Spanish",
                            ca: "Catalan",
                        }
                    },
                    preferredTheme: {
                        label: "Preferred theme",
                        options: {
                            SYSTEM_DEFAULT: "System default",
                            LIGHT: "Light",
                            DARK: "Dark",
                        }
                    },
                    companyInformation: {
                        title: "Company profile",
                        address: {
                            title: "Address",
                            apartmentNumber: "Apartment number",
                            name: "Name",
                            street: "Street",
                            houseNumber: "House number",
                            zipCode: "Zip code",
                            city: "City",
                            country: "Country",
                        }
                    },
                    contactInformation: {
                        title: "Contact information",
                        type: {
                            DISCORD: "Discord",
                            EMAIL: "E-Mail",
                            FAX: "Fax",
                            LINKED_IN: "LinkedIn",
                            OTHER: "Other",
                            PINTEREST: "Pinterest",
                            SIGNAL: "Signal",
                            SKYPE: "Skype",
                            SNAPCHAT: "Snapchat",
                            TELEGRAM: "Telegram",
                            TELEPHONE: "Telephone",
                            THREEMA: "Threema",
                            TIKTOK: "TikTok",
                            TWITTER: "Twitter",
                            VIBER: "Viber",
                            WEBSITE: "Website",
                            XING: "Xing",
                            YOUTUBE: "YouTube",
                        }
                    },
                    additionalInformation: {
                        title: "Additional information",
                        type: {
                            ABOUT_US: "About us",
                            COMPANY_LOGO: "Company logo",
                            COMPANY_NAME: "Company name",
                            PROFILE_PICTURE: "Profile picture",
                            SOCIAL_SECURITY_NUMBER: "Social security number",
                            VAT_NUMBER: "VAT number",
                            PROFESSION: "Profession",
                            CONSULTANT_NUMBER: "Consultant number",
                        }
                    },
                    save: "Save",
                    saveSucceeded: "Your changes have been saved successfully.",
                    saveFailed: "An error occurred while saving your changes. Please try again later.",
                    deleteImage: "Delete image",
                },
                furtherOptions: {
                    title: "Further options",
                    changePassword: "Change password",
                    deleteAccount: {
                        title: "Delete account",
                        description: "Do you really want to delete your account? All data (including exposés, location reports, etc.) will be deleted irrevocably.",
                        confirm: "Delete account",
                        cancel: "Cancel",
                        errorTitle: "Delete account failed",
                        errorDescription: "An error occurred while deleting your account. Please try again later.",
                    },
                }
            },
            accountListings: {
                card: {
                    type: {
                        HOUSE: {
                            DEFAULT: "House",
                            SELLING: "House for sale",
                            RENTING: "House for rent",
                        },
                        APARTMENT: {
                            DEFAULT: "Apartment",
                            SELLING: "Apartment for sale",
                            RENTING: "Apartment for rent",
                        },
                        LAND: {
                            DEFAULT: "Land",
                            SELLING: "Land for sale",
                            RENTING: "Land for rent",
                        }
                    },
                    customStatus: {
                        PLANNED: "Planned",
                        IN_PROGRESS: "In Progress",
                        DONE: "Done",
                        ARCHIVED: "Archived"
                    }
                },
                title: "Listings",
                description: "Create a comprehensive listing now, enriched with geodata, location information, attractiveness rating, and a 3D floor plan that you can create yourself, all within a few minutes.",
                visits: "visits",
                create: "Create listing",
                view: "Display",
                edit: "Edit",
                duplicate: "Duplicate",
                delete: "Delete",
                dialog: {
                    duplicate: {
                        title: "Do you really want to duplicate the listing?",
                        description: "Creates a copy of the listing. All data and photos will be taken over.",
                        confirmButton: "Duplicate"
                    },
                    delete: {
                        title: "Do you really want to delete the listing?",
                        description: "All data and photos will be deleted irrevocably.",
                        confirmButton: "Delete"
                    },
                    cancel: "Cancel"
                },
                remainingDays: {
                    remaining: "Still",
                    days: "days",
                },
                listingFilter: {
                    customStatus: "Status",
                    onlyMyListings: "Show only my listings",
                    search: "Search …",
                    zeroSearchResults: "No results found.",
                    showAllButton: "Show all",
                    reloadButton: "Reload",
                },
            },
            accountBuildings: {
                title: "Buildings (old)"
            }
        },
        footer: {
            appRoutes: {
                mobileApps: "Mobile apps",
                registration: "Register",
                faq: "FAQ",
                contact: "Contact",
                privacyPolicy: "Privacy policy",
                imprint: "Imprint",
                attributions: "References",
                termsAndConditions: "Terms and conditions",
            },
            socialMediaProfiles: {
                twitter: "Twitter",
                facebook: "Facebook",
                linkedin: "LinkedIn",
            }
        }
    },
    enums: {
        wallType: {
            INTERIOR: "Interior wall | Interior walls",
            EXTERIOR: "Exterior wall | Exterior walls",
            INTERMEDIATE: "Intermediate wall | Intermediate walls",
        },
        cardinalDirection: {
            NORTH: "N",
            NORTH_EAST: "NE",
            EAST: "E",
            SOUTH_EAST: "SE",
            SOUTH: "S",
            SOUTH_WEST: "SW",
            WEST: "W",
            NORTH_WEST: "NW",
        },
        ifcExportContextTargetCAD: {
            DEFAULT: "All CADs",
            ECAD: "E-CAD5",
        },
        pdfExport: {
            label: "Pages",
            levels: {
                ALL: "All levels",
                CURRENT_FLOOR: "Current level",
            },
            pageOrientation: {
                portrait: "Portrait",
                landscape: "Landscape",
            }
        },
        buildingExportType: {
            IFC: "IFC: Industry Foundation Classes (.ifc)",
            EVEX: "EVEX: Evebi Projektdatei (.evex)",
            IMAGES: "Pictures (.zip)",
            PDF: "PDF (.pdf)",
        },
        gbXMLExportType: {
            EVEBI: "EVEBI 13+",
        },
        evexExportType: {
            EVEBI: "EVEBI 13+",
        },
        wallOpeningType: {
            OPENING: "Opening",
            WINDOW: "Window",
            DOOR: "Door",
        },
        shapeType: {
            Box: "rectangular",
            Polygon: "slanted",
            Ring: "round",
        },
        attractivenessCategoryType: {
            LOCATION: "Location",
            LIVING_QUALITY: "Living Quality",
            SOCIODEMOGRAPHY: "Sociodemography",
            ECONOMY: "Economy"
        },
        attractivenessCriteriaType: {
            LOCATION_DAILY_NEEDS: "Daily Needs",
            LOCATION_SHOPPING: "Shopping",
            LOCATION_LEISURE: "Leisure Activities",
            LOCATION_TRAFFIC_CONNECTION: "Traffic Connection",
            LIVING_QUALITY_INDOOR: "Indoor Leisure Facilities",
            LIVING_QUALITY_OUTDOOR: "Outdoor Leisure Facilities",
            SOZIODEMOGRAPHY_POPULATION_TREND: "Population Trend",
            SOZIODEMOGRAPHY_CRIME_TREND_5_YEARS: "Crime Trend (5 Years)",
            SOZIODEMOGRAPHY_AGE_DISTRIBUTION_50_TO_65: "Population 50-65 Years",
            SOZIODEMOGRAPHY_AGE_DISTRIBUTION_BELOW_50: "Population Below 50 Years",
            ECONOMY_UNEMPLOYMENT_RATE: "Unemployment Rate",
            ECONOMY_UNEMPLOYMENT_RATE_TREND_10_YEARS: "Unemployment Trend (10 Years)",
            ECONOMY_PER_HEAD_OUTPUT: "Economic Output at Location",
            ECONOMY_GROWTH_10_YEARS: "Economic Growth (10 Years)",
            ECONOMY_INCOME: "Per Capita Income",
            ECONOMY_EDUCATION_LEVEL: "Education Level"
        },
        energyEfficiencyCategoryType: {
            A_PLUS: "A＋",
            A: "A",
            B: "B",
            C: "C",
            D: "D",
            E: "E",
            F: "F",
            G: "G",
            H: "H"
        },
        languageCode: {
            de: "Deutsch",
            en: "English",
            es: "Español",
            ca: "Catalán",
        },
        listingFieldUnit: {
            CENTIMETER: "cm",
            CUBIC_METER: "m³",
            CURRENCY: "€",
            CURRENCY_PER_SQUARE_METER: "€/m²",
            DEGREE_CELSIUS: "℃",
            DEGREE_OF_ARC: "°",
            KG_CARBON_DI_OXIDE_PER_SQUARE_METER_PER_YEAR: "㎏CO₂/m²a",
            KILOWATT: "㎾",
            KILOWATT_HOUR: "㎾h",
            KILOWATT_HOUR_PER_SQUARE_METER_PER_YEAR: "㎾h/m²a",
            LITER: "l",
            METER: "m",
            PERCENTAGE: "%",
            SQUARE_METER: "m²",
            WATT_PER_SQUARE_METER_PER_KELVIN: "W/m²K",
            YEAR: "a",
        },
        listingRegionType: {
            GERMANY: "In Germany",
            SPAIN: "In Spain",
        },
        pointOfInterestDetailType: {
            HEALTHCARE_SPECIALITY: "Healthcare specialty",
            IATA_CODE: "IATA code",
            ROLLERCOASTER_COUNT: "Rollercoaster count",
            SCHOOL_LEGAL_STATUS: "School legal status",
            SCHOOL_TYPE: "School type",
            SHOP_COUNT: "Shop count",
            WEBSITE: "Website",
            AREA: "Area",
        },
        pointOfInterestDetailTypeValues: {
            AREA: {
                SMALL_WOOD: "Small wood",
                WOOD: "Wood",
                FOREST: "Forest"
            }
        },
        pointOfInterestParentGroupType: {
            BASIC_SERVICES: "Basic services",
            SHOPPING: "Shopping",
            LEISURE: "Leisure",
            SPORTS: "Sports",
            TRANSPORT: "Transport",
        },
        pointOfInterestType: {
            AMUSEMENT_PARK: "Amusement park",
            ANIMAL_TRAINING: "Animal training",
            AQUARIUM: "Aquarium",
            BAKERY: "Bakery",
            BANK: "Bank",
            BAR_PUB: "Bar / Pub / Beer garden",
            BATHING_PLACE: "Bathing place",
            BOWLING_ALLEY: "Bowling alley",
            BUS_STOP: "Bus stop",
            CHEMIST: "Pharmacy",
            CHILDCARE: "Childcare",
            CINEMA: "Cinema",
            COASTLINE: "Coastline",
            DOCTORS: "Doctor / Practice",
            DOG_PARK: "Dog park",
            ELECTRIC_CAR_CHARGER: "E-charging station",
            FITNESS_CENTER: "Fitness center",
            FOREST: "Forest",
            FURNITURE_AND_INTERIOR: "Furniture and interior",
            GARDEN_CENTER: "Garden center",
            HAIR_DRESSER: "Hairdresser",
            HARDWARE_STORE: "Hardware store",
            HOSPITAL: "Hospital",
            INTERNATIONAL_AIRPORT: "International airport",
            KIOSK: "Kiosk",
            LAKE: "Lake",
            LIBRARY: "Library",
            MALL: "Shopping mall",
            MARKETPLACE: "Marketplace",
            METROPOLITAN_CITY: "Metropolitan city",
            MOUNTAIN_RANGE: "Mountain range",
            MUSIC_SCHOOL: "Music school",
            NATURE_RESERVE: "Nature reserve",
            NIGHTCLUB: "Nightclub",
            PARCEL_LOCKER: "Parcel locker",
            PARK: "Park",
            PHARMACY: "Pharmacy",
            PLACE_OF_WORSHIP: "Place of worship",
            PLAYGROUND: "Playground",
            RESTAURANT_CAFE: "Restaurant / Cafe",
            RIVER: "River",
            SAFETY_AMENITY: "Safety amenity",
            SCHOOL: "School",
            SHOPPING_CENTER: "Shopping center",
            SPORT_BASKETBALL: "Basketball court",
            SPORT_GOLF_COURSE: "Golf course",
            SPORT_HORSE_RIDING: "Horse riding",
            SPORT_ICE_RINK: "Ice rink",
            SPORT_PADDLE_SPORTS: "Paddle sports",
            SPORT_SHOOTING: "Shooting range",
            SPORT_SOCCER: "Soccer field",
            SPORT_SWIMMING: "Swimming pool",
            SPORT_TENNIS: "Tennis court",
            SPORT_WINTER: "Winter sports",
            SUPERMARKET: "Supermarket",
            THEATER: "Theater",
            TRAIN_STATION: "Train station",
            UNIVERSITY: "University / College",
            WATER_PARK: "Water park",
            ZOO: "Zoo"
        },
        populationGroupType: {
            METROPOLITAN_CITY: "Metropolitan city",
            CITY: "City",
            TOWN: "Town",
            VILLAGE: "Village",
            HAMLET: "Hamlet",
        },
    },
    units: {
        squareMeters: "{squareMeters} square meters",
        squareMetersShort: "{squareMeters} sqm",
        rooms: "{roomCount} rooms",
        roomsShort: "{roomCount} rm.",
        currencyPerSquareMeter: "{currency}/sqm",
        squareKilometersShort: "{squareKilometers} sqkm",
    },
    formRules: {
        required: "This field is required.",
        email: "Please enter a valid email address.",
        phoneNumber: "Please enter a valid phone number.",
    }
};

export default enMessages;