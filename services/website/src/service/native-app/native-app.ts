import {NativeAppService} from "@/service/native-app/NativeAppService";
import {Optional} from "@/model/Optional";
import {LS__HAS_LIDAR, LS__IS_NATIVE_ANDROID_APP, LS__IS_NATIVE_APP, LS__IS_NATIVE_IOS_APP} from "@/service/local-storage/local-storage";

export let NATIVE_APP_SERVICE: Optional<NativeAppService> = null;

const queryString = window.location.search
const urlParameters = new URLSearchParams(queryString)


// ### IOS ###
const NATIVE_APP_URL_PARAMETER_IOS = "isIOS"

const existsNativeIOSAppURLParameter = urlParameters.has(NATIVE_APP_URL_PARAMETER_IOS)
const isNativeIOSAppURLParameterEnabled = urlParameters.get(NATIVE_APP_URL_PARAMETER_IOS) === 'true'
const isNativeIOSAppLocalStorageEnabled = LS__IS_NATIVE_IOS_APP.state.value
export const IS_NATIVE_IOS_APP = (existsNativeIOSAppURLParameter && isNativeIOSAppURLParameterEnabled) || isNativeIOSAppLocalStorageEnabled

if (existsNativeIOSAppURLParameter) {
    LS__IS_NATIVE_IOS_APP.state.value = isNativeIOSAppURLParameterEnabled
}

if (IS_NATIVE_IOS_APP) {
    NATIVE_APP_SERVICE = new NativeAppService('IOS')
}


// ### ANDROID ###
const NATIVE_APP_URL_PARAMETER_ANDROID = "isAndroid"

const existsNativeAndroidAppURLParameter = urlParameters.has(NATIVE_APP_URL_PARAMETER_ANDROID)
const isNativeAndroidAppURLParameterEnabled = urlParameters.get(NATIVE_APP_URL_PARAMETER_ANDROID) === 'true'
const isNativeAndroidAppLocalStorageEnabled = LS__IS_NATIVE_ANDROID_APP.state.value
export const IS_NATIVE_ANDROID_APP = (existsNativeAndroidAppURLParameter && isNativeAndroidAppURLParameterEnabled) || isNativeAndroidAppLocalStorageEnabled

if (existsNativeAndroidAppURLParameter) {
    LS__IS_NATIVE_ANDROID_APP.state.value = isNativeAndroidAppURLParameterEnabled
}

if (IS_NATIVE_ANDROID_APP) {
    NATIVE_APP_SERVICE = new NativeAppService('ANDROID')
}


// ### IS_NATIVE ###
const NATIVE_APP_URL_PARAMETER_NATIVE = "isNativeApp"

const existsNativeAppURLParameter = urlParameters.has(NATIVE_APP_URL_PARAMETER_NATIVE)
const isNativeAppURLParameterEnabled = urlParameters.get(NATIVE_APP_URL_PARAMETER_NATIVE) === 'true'
const isNativeAppLocalStorageEnabled = LS__IS_NATIVE_APP.state.value
const isNativeApp = (existsNativeAppURLParameter && isNativeAppURLParameterEnabled) || isNativeAppLocalStorageEnabled
export const IS_NATIVE_APP = IS_NATIVE_IOS_APP || IS_NATIVE_ANDROID_APP || isNativeApp

if (existsNativeAppURLParameter) {
    LS__IS_NATIVE_APP.state.value = isNativeAppURLParameterEnabled
}


// ### HAS_LIDAR ###
const NATIVE_APP_URL_PARAMETER_HAS_LIDAR = "hasLIDAR"

const existsHasLidarURLParameter = urlParameters.has(NATIVE_APP_URL_PARAMETER_HAS_LIDAR)
const hasLidarURLParameterEnabled = urlParameters.get(NATIVE_APP_URL_PARAMETER_HAS_LIDAR) === 'true'
const hasLidarLocalStorageEnabled = LS__HAS_LIDAR.state.value
//TODO: sobald es möglich ist, per Sensor API von JavaScript herauszufinden, ob ein Gerät LIDAR unterstützt, sollte diese Variable angepasst werden.
export const HAS_LIDAR = (existsHasLidarURLParameter && hasLidarURLParameterEnabled) || hasLidarLocalStorageEnabled

if (existsHasLidarURLParameter) {
    LS__HAS_LIDAR.state.value = hasLidarURLParameterEnabled
}