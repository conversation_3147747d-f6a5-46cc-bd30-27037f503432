<template>
    <template v-if="embedded">
        <d-progress-linear v-if="(isQueryResultBuildingLoading)"
                           indeterminate
                           type="default"/>
        <div v-else
             style="height: 4px;"/>
    </template>

    <v-slide-y-transition>
        <d-loading v-if="(isQueryResultBuildingLoading) && !embedded"/>
    </v-slide-y-transition>

    <v-slide-y-transition>
        <div v-if="embedded && subflowType !== null">
            <d-listing-subflow-building-component-content :loading="isLoading"
                                                          :save-custom-data-building-error="saveCustomDataBuildingError"
                                                          :subflow="subflow"
                                                          :subflow-type="subflowType"
                                                          @save="save"/>
        </div>
    </v-slide-y-transition>

    <v-slide-y-transition>
        <d-fc-layout v-if="!embedded && subflowType !== null">
            <template #left>
                <d-listing-subflow-building-component-content :loading="isLoading"
                                                              :save-custom-data-building-error="saveCustomDataBuildingError"
                                                              :subflow="subflow"
                                                              :subflow-type="subflowType"
                                                              @save="save"/>
            </template>
        </d-fc-layout>
    </v-slide-y-transition>
</template>

<script lang="ts"
        setup>
    import {Building, CustomUiElement, DListingSubflowBuildingComponentLoadCustomDataQueryVariables, DListingSubflowBuildingComponentLoadListingDocument, FlowConfigFieldValue, FlowConfigFieldValueInput, Listing, Subflow, useDListingSubflowBuildingComponentLoadCustomDataLazyQuery, useDListingSubflowBuildingComponentSaveCustomDataMutation} from "@/adapter/graphql/generated/graphql";
    import {computed, inject, onMounted, onUnmounted, provide, ref, shallowRef, toRaw, watch, watchEffect} from "vue";
    import {LFlowDataInjection, LListingContextInjection, LListingIdInjection, LListingInjection, LMutableFlowDataInjection} from "@/components/listing/ListingInjectionKeys";
    import {onBeforeRouteLeave} from "vue-router";
    import {createEmptyMutableFlowData, MutableFlowData} from "@/model/listing/FlowData";
    import {useI18n} from "vue-i18n";
    import {Optional} from "@/model/Optional";
    import DLoading from "@/components/fragment/d-loading.vue";
    import DFcLayout from "@/components/flow-config/page/d-fc-layout.vue";
    import {SubflowBuildingComponentTarget} from "@/components/listing/subflow/building-component/SubflowBuildingComponentTarget";
    import DListingSubflowBuildingComponentContent from "@/components/listing/subflow/building-component/d-listing-subflow-building-component-content.vue";
    import DProgressLinear from "@/adapter/vuetify/theme/components/progress/d-progress-linear.vue";
    import {NATIVE_APP_SERVICE} from "@/service/native-app/native-app";
    import {mergeFieldValues} from "@/adapter/graphql/mapper/buildinginput-to-building-mapper";
    import {createNativeAppMethod, destroyNativeAppMethod} from "@/service/native-app/native-app-function";
    import {useDisplay} from "vuetify";
    import {EnsureDefined, mapFlowConfigFieldValueToInput, mapInputToFlowConfigFieldValue} from "@/adapter/graphql/mapper/graphql-mapper";
    import {createMutableBuildingFrom} from "@/components/listing/building/building";
    import {mapToBuilding} from "@/adapter/graphql/mapper/building-to-building-mapper";
    import {ListingContext} from "@/model/listing/ListingContext";
    import {TraversalBuildingComponent, traverseBuildingComponent} from "@/components/listing/building/renderer/TraversableBuilding";

    const props = defineProps<{
        customUiElement: Optional<CustomUiElement>
        subflowType: Optional<string>
        target: SubflowBuildingComponentTarget
        componentIds: readonly string[]
        embedded?: boolean
        forcedContext?: ListingContext
    }>()

    const queryString = window.location.search
    const urlParameters = new URLSearchParams(queryString)
    const useNativeAppCommunication = urlParameters.get("useNativeAppCommunication") === 'true'

    const isLoading = defineModel<boolean>("loading", {
        required: false,
        default: false
    })

    const emits = defineEmits<{
        change: [oldData: readonly FlowConfigFieldValue[], newData: readonly FlowConfigFieldValue[]]
    }>()

    const listingId = inject(LListingIdInjection)!
    const listing = inject(LListingInjection)!

    const rawContext = inject(LListingContextInjection)!
    const context = computed<ListingContext>(() => props.forcedContext ?? rawContext.value)
    provide(LListingContextInjection, context)

    const subflow = computed<Optional<Subflow>>(() => props.subflowType === null ? null : props.customUiElement?.subflows.find(subflow => subflow.type === props.subflowType) ?? null)

    const {
        result: queryResultBuilding,
        loading: isQueryResultBuildingLoading,
        load: loadQueryBuilding,
        refetch: refetchQueryBuilding,
        //error //TODO: ....
    } = useDListingSubflowBuildingComponentLoadCustomDataLazyQuery()

    const nativeAppRoomCustomData = shallowRef<Optional<readonly FlowConfigFieldValueInput[]>>(null) //null => keine daten empfangen
    const nativeAppPointOfInterestCustomData = shallowRef<Optional<readonly FlowConfigFieldValueInput[]>>(null) //null => keine daten empfangen

    const customData = computed<Optional<readonly FlowConfigFieldValue[]>>(() => { //null => keine daten empfangen
        switch (props.target) {
            case "BUILDING":
                return queryResultBuilding.value?.listingBuildingCustomData as FlowConfigFieldValue[] ?? []
            case "ROOM_SCAN":
                return nativeAppRoomCustomData.value?.map(fv => mapInputToFlowConfigFieldValue(fv, false)) ?? []
            case "BUILDING_SCAN_POINT_OF_INTEREST":
                return nativeAppPointOfInterestCustomData.value?.map(fv => mapInputToFlowConfigFieldValue(fv, false)) ?? []
            default:
                return null
        }
    })

    const {t} = useI18n()

    const oldFlowData = ref<MutableFlowData>(createEmptyMutableFlowData()) //ref okay
    const newFlowData = ref<MutableFlowData>(createEmptyMutableFlowData()) //ref okay
    // Track changes made during save operation
    const pendingSaveOperation = shallowRef<boolean>(false)
    // Track if there are changes that need to be saved after the current save operation completes
    const hasChangesAfterSave = shallowRef<boolean>(false)
    // Track user activity for inactivity timer
    const lastUserActivity = shallowRef<number>(Date.now())
    // Flag to prevent refetching data after a save operation
    const skipNextRefetch = shallowRef<boolean>(false)

    provide(LFlowDataInjection, newFlowData)
    provide(LMutableFlowDataInjection, newFlowData)

    function setFlowDataFromCustomData(customData: readonly FlowConfigFieldValue[]) {
        const flowData = createEmptyMutableFlowData()
        flowData.setFromInput(customData)

        oldFlowData.value = flowData.mutableDeepCopy()
        newFlowData.value = flowData.mutableDeepCopy()
    }

    watch(customData, customData => {
        if (customData === null) {
            return
        }

        // Only set flow data from custom data if we're not in the middle of a save operation
        // or if there are no pending changes after a save
        if (!pendingSaveOperation.value && !hasChangesAfterSave.value) {
            setFlowDataFromCustomData(customData);
        }
    }, {
        deep: true,
        immediate: true
    })

    const deltaState = computed<readonly FlowConfigFieldValue[]>(() => oldFlowData.value.generateInputDelta(newFlowData.value, true))

    const canSave = computed<boolean>(() =>
        listing.value.isEditable &&
        context.value === "EDIT" &&
        deltaState.value.length > 0 &&
        props.componentIds.length > 0 &&
        (!useNativeAppCommunication || (nativeAppRoomCustomData.value !== null || nativeAppPointOfInterestCustomData.value !== null))
    )

    const isAutoSaving = shallowRef<boolean>(false)
    const shouldDisplayUnsavedChangesWarning = computed<boolean>(() => isAutoSaving.value || canSave.value)

    function beforeUnloadCheck(event: BeforeUnloadEvent): string | boolean {
        if (shouldDisplayUnsavedChangesWarning.value) {
            event.preventDefault()
            return t('listing.edit.unsavedChangesWarning')
        }
        return false
    }

    function requestNativeDataForRoom() {
        if (!isMounted || !useNativeAppCommunication) {
            return
        }
        nativeAppRoomCustomData.value = null
        NATIVE_APP_SERVICE!.onBuildingScanRoomCustomDataRequested(props.componentIds[0])
    }

    function requestNativeDataForPointOfInterest() {
        if (!isMounted || !useNativeAppCommunication) {
            return
        }
        nativeAppPointOfInterestCustomData.value = null
        NATIVE_APP_SERVICE!.onBuildingScanPointOfInterestCustomDataRequested(props.componentIds[0])
    }

    const nativeAppRoomMethod = "nativeAppRoomCustomDataReceived"
    const nativeAppPointOfInterestMethod = "nativeAppPointOfInterestCustomDataReceived"
    let isMounted = false
    onMounted(() => {
        window.addEventListener("beforeunload", beforeUnloadCheck);

        isMounted = true //muss vor requestNativeDataForRoom/requestNativeDataForPointOfInterest stehen

        if (useNativeAppCommunication) {
            switch (props.target) {
                case "ROOM_SCAN":
                    createNativeAppMethod(nativeAppRoomMethod, (roomCustomData: readonly FlowConfigFieldValueInput[]) => {
                        nativeAppRoomCustomData.value = roomCustomData
                    })
                    requestNativeDataForRoom()
                    break
                case "BUILDING_SCAN_POINT_OF_INTEREST":
                    createNativeAppMethod(nativeAppPointOfInterestMethod, (pointOfInterestCustomData: readonly FlowConfigFieldValueInput[]) => {
                        nativeAppPointOfInterestCustomData.value = pointOfInterestCustomData
                    })
                    requestNativeDataForPointOfInterest()
                    break
            }
        }
    })
    onUnmounted(async () => {
        // If there are unsaved changes, save them before unmounting
        if (canSave.value && !pendingSaveOperation.value) {
            // Cancel any pending inactivity timer
            if (inactivitySaveTimer.value) {
                window.clearTimeout(inactivitySaveTimer.value)
                inactivitySaveTimer.value = null
            }

            // Save immediately without waiting for inactivity
            await save()
        }

        switch (props.target) {
            case "ROOM_SCAN":
                destroyNativeAppMethod(nativeAppRoomMethod)
                break
            case "BUILDING_SCAN_POINT_OF_INTEREST":
                destroyNativeAppMethod(nativeAppPointOfInterestMethod)
                break
        }

        window.removeEventListener("beforeunload", beforeUnloadCheck)

        isMounted = false
    })

    watch(() => props.componentIds, componentIds => {
        if (componentIds.length <= 0) {
            return
        }

        if (useNativeAppCommunication) {
            switch (props.target) {
                case "ROOM_SCAN":
                    requestNativeDataForRoom()
                    break
                case "BUILDING_SCAN_POINT_OF_INTEREST":
                    requestNativeDataForPointOfInterest()
                    break
            }
        } else {
            switch (props.target) {
                case "BUILDING":
                    // If we just completed a save operation, skip this refetch
                    if (skipNextRefetch.value) {
                        skipNextRefetch.value = false
                        return
                    }

                    // eslint-disable-next-line
                    const variablesBuilding: DListingSubflowBuildingComponentLoadCustomDataQueryVariables = {
                        listingId: listing.value.id,
                        componentIds: [...componentIds]
                    }
                    // eslint-disable-next-line
                    loadQueryBuilding(null, variablesBuilding, {fetchPolicy: "cache-and-network"})
                    break
                default:
                    throw new Error(`Unknown target: ${props.target}`)
            }
        }
    }, {
        deep: true,
        immediate: true
    })

    onBeforeRouteLeave(async (to, from) => {
        // If there are unsaved changes, save them before leaving
        if (canSave.value) {
            // Cancel any pending inactivity timer
            if (inactivitySaveTimer.value) {
                window.clearTimeout(inactivitySaveTimer.value)
                inactivitySaveTimer.value = null
            }

            // Save immediately without waiting for inactivity
            await save()

            // If save failed or there are still changes, show warning
            if (shouldDisplayUnsavedChangesWarning.value) {
                return confirm(t('listing.edit.unsavedChangesWarning'))
            }
        }
        return true
    })

    const {
        mutate: saveCustomDataBuilding,
        loading: isSaveCustomDataBuildingLoading,
        error: saveCustomDataBuildingError
    } = useDListingSubflowBuildingComponentSaveCustomDataMutation()

    const {
        mobile
    } = useDisplay()

    // Inactivity timeout in milliseconds before saving
    const inactivityTimeout = computed(() => mobile.value ? 1000 : 1250)

    // Function to update the last user activity timestamp
    function updateUserActivity() {
        lastUserActivity.value = Date.now()
        // If we're not already waiting to save after inactivity, start the timer
        if (!inactivitySaveTimer.value) {
            startInactivityTimer()
        }
    }

    // Timer reference for inactivity save
    const inactivitySaveTimer = shallowRef<number | null>(null)

    // Start the inactivity timer
    function startInactivityTimer() {
        // Clear any existing timer
        if (inactivitySaveTimer.value) {
            window.clearTimeout(inactivitySaveTimer.value)
        }

        // Set a new timer
        inactivitySaveTimer.value = window.setTimeout(() => {
            if (canSave.value && !pendingSaveOperation.value) {
                autosave()
            }
            inactivitySaveTimer.value = null
        }, inactivityTimeout.value)
    }

    // Clear the inactivity timer when component is unmounted
    onUnmounted(() => {
        if (inactivitySaveTimer.value) {
            window.clearTimeout(inactivitySaveTimer.value)
            inactivitySaveTimer.value = null
        }
    })

    // Watch for changes and update user activity
    watch(deltaState, () => {
        if (canSave.value) {
            updateUserActivity()

            // If there's a pending save operation, mark that we have changes after save
            if (pendingSaveOperation.value) {
                hasChangesAfterSave.value = true
            }
        }
    }, {
        deep: true
    })

    async function autosave() {
        // Don't start a new save if one is already in progress
        if (pendingSaveOperation.value) {
            hasChangesAfterSave.value = true
            return
        }

        isAutoSaving.value = true
        await save()

        // If changes were made during the save operation, trigger another save
        if (hasChangesAfterSave.value) {
            hasChangesAfterSave.value = false
            // Small delay to allow UI to update
            setTimeout(() => {
                autosave()
            }, 100)
        }
    }

    async function save() {
        try {
            if (!canSave.value) {
                return
            }

            // Mark that we have a pending save operation
            pendingSaveOperation.value = true

            // Set flag to skip any refetch that might be triggered during the save operation
            skipNextRefetch.value = true

            type CachedListing = {
                listing: Optional<Listing>
            }

            // Create a snapshot of the current state before saving
            const oldInput = oldFlowData.value.generateInput(false) // das muss beriets hier berechnet werden, weil der offline cache oldFlowData direkt überschreibt und sonst new und old identisch wären

            switch (props.target) {
                case "BUILDING":
                    // eslint-disable-next-line
                    const newCustomData: readonly FlowConfigFieldValue[] = JSON.parse(JSON.stringify(toRaw(deltaState.value)))
                    // eslint-disable-next-line
                    const newCustomDataInput = newCustomData.map(fv => mapFlowConfigFieldValueToInput(fv));

                    // eslint-disable-next-line
                    const componentIds = [...props.componentIds]

                    // eslint-disable-next-line
                    const resultBuilding = await saveCustomDataBuilding({
                        listingBuildingCustomData: {
                            listingId: listingId.value,
                            componentIds,
                            customData: newCustomDataInput
                        },
                    }, {
                        update: (cache, result) => {
                            const cachedListing = cache.readQuery<CachedListing>({
                                query: DListingSubflowBuildingComponentLoadListingDocument,
                                variables: {
                                    listingId: listing.value.id
                                },
                                returnPartialData: true
                            })
                            if (cachedListing === null) {
                                console.warn("Cached listing not found")
                                return;
                            }
                            const building = cachedListing.listing?.building ?? null
                            if (building === null) {
                                console.warn("Building not found")
                                return;
                            }

                            const newComponentCustomData = result.data?.saveListingBuildingCustomData === undefined ? null : result.data.saveListingBuildingCustomData as FlowConfigFieldValue[]
                            const newBuilding: Building = createMutableBuildingFrom(building)

                            const componentIdToComponent = new Map<string, TraversalBuildingComponent>()
                            traverseBuildingComponent(newBuilding, component => {
                                componentIdToComponent.set(component.id, component)
                            })

                            for (const componentId of componentIds) {
                                const component = componentIdToComponent.get(componentId)
                                if (component === undefined) {
                                    console.warn(`Component with id ${componentId} not found in traversable building`)
                                    continue
                                }

                                let customData: EnsureDefined<FlowConfigFieldValue>[]
                                if (newComponentCustomData === null) {
                                    customData = mergeFieldValues(component.customData, newCustomData)
                                } else {
                                    const flowData = createEmptyMutableFlowData()
                                    flowData.setFromInput(newComponentCustomData)
                                    customData = flowData.generateInput(false)
                                }

                                component.customData = customData
                            }

                            const updatedBuilding = mapToBuilding(newBuilding)

                            cache.modify({
                                id: cache.identify(listing.value),
                                broadcast: false,
                                fields: {
                                    building(): EnsureDefined<Building> {
                                        return updatedBuilding;
                                    }
                                }
                            });
                        }
                    });

                    if (!resultBuilding?.data?.saveListingBuildingCustomData) {
                        return
                    }

                    // Create a temporary flow data from the server response
                    const serverFlowData = createEmptyMutableFlowData()
                    serverFlowData.setFromInput(resultBuilding.data.saveListingBuildingCustomData as FlowConfigFieldValue[])

                    oldFlowData.value = serverFlowData.mutableDeepCopy()

                    // Preserve any changes made while save operation was in progress
                    if (!hasChangesAfterSave.value) {
                        newFlowData.value = serverFlowData.mutableDeepCopy()
                    }

                    // Set flag to skip the next refetch that would be triggered by the save response
                    skipNextRefetch.value = true

                    break
                case "ROOM_SCAN":
                    NATIVE_APP_SERVICE!.onBuildingScanRoomCustomDataReceived({
                        roomFileId: props.componentIds[0],
                        customData: newFlowData.value.mutableDeepCopy().generateInput(false) //kein delta hier verwenden, wir brauchen in der app die gesamten daten
                    })
                    break
                case "BUILDING_SCAN_POINT_OF_INTEREST":
                    NATIVE_APP_SERVICE!.onBuildingScanPointOfInterestCustomDataReceived({
                        pointOfInterestId: props.componentIds[0],
                        customData: newFlowData.value.mutableDeepCopy().generateInput(false) //kein delta hier verwenden, wir brauchen in der app die gesamten daten
                    })
                    break
                default:
                    throw new Error(`Unknown target: ${props.target}`)
            }

            const newData = newFlowData.value.mutableDeepCopy()
            const newInput = newData.generateInput(false)
            oldFlowData.value = newData
            emits("change", oldInput, newInput)
        } finally {
            isAutoSaving.value = false
            pendingSaveOperation.value = false
        }
    }

    /**
     * Speichert Änderungen, falls welche vorhanden sind.
     * @returns true, wenn Änderungen gespeichert wurden, false sonst
     */
    async function saveIfNeeded(): Promise<boolean> {
        if (canSave.value && !pendingSaveOperation.value) {
            await save()
            return true
        }
        return false
    }

    // Mache Methoden und Eigenschaften öffentlich zugänglich
    defineExpose({
        canSave,
        pendingSaveOperation,
        save,
        saveIfNeeded
    })

    watchEffect(() => {
        isLoading.value = isSaveCustomDataBuildingLoading.value || isAutoSaving.value
    })
</script>

<style scoped>
</style>