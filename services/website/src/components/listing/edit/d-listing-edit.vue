<template>
    <d-flow-pages v-model:is-navigation-drawer-visible="isNavigationDrawerVisible"
                  v-model:selected-page-index="selectedPageId"
                  :pages="pages">
        <template #header>
            <d-card class="header"
                    rounded="0"
                    variant="elevated">
                <d-toolbar class="px-4">
                    <v-row align="center">
                        <v-col>
                            <d-btn :icon="mdiMenu"
                                   size="large"
                                   type="tertiary"
                                   variant="text"
                                   @click="onToggleNavigationDrawer"/>

                            <d-listing-button-back :route="{
                                                        name: 'flowListings',
                                                        params: {
                                                            flowConfigId
                                                        }
                                                    }"
                                                   :text="t('listing.edit.backButton')"/>
                        </v-col>

                        <v-col v-if="smAndUp && selectedPage"
                               class="text-center">
                            <d-h4>{{ flowConfigTranslator(selectedPage.titleLong) ?? flowConfigTranslator(flowConfig?.titleLong ?? []) }}</d-h4>
                        </v-col>

                        <v-col class="text-end">
                            <v-layout class="align-center justify-end">
                                <!-- LOADING -->
                                <v-slide-x-reverse-transition>
                                    <d-progress-circular v-if="isSaveListingLoading || isAutoSaving"
                                                         class="me-2"
                                                         indeterminate
                                                         size="24"
                                                         width="2"/>
                                </v-slide-x-reverse-transition>

                                <!-- ERROR -->
                                <v-slide-x-reverse-transition>
                                    <!-- wir brauchen einen div wrapper hier, sonst kämpfen die animationen der transition und dem blinken des buttons gegeneinander -->
                                    <div v-if="saveListingError"
                                         class="d-inline-block">
                                        <d-btn :blink="!isSaveListingLoading && !isAutoSaving"
                                               :icon="mdiSyncAlert"
                                               size="large"
                                               type="error"
                                               variant="text"
                                               @click="save(false)"/>
                                    </div>
                                </v-slide-x-reverse-transition>

                                <div v-if="IS_NATIVE_APP"
                                     class="d-inline-block">
                                    <d-service-worker-status/>
                                </div>

                                <d-confirmation-wrapper @onconfirm="onDuplicateListing">
                                    <template #default="{props: confirmProps}">
                                        <d-btn :icon="smAndDown ? mdiContentCopy : undefined"
                                               :loading="isDuplicateListingLoading"
                                               :prepend-icon="smAndDown ? undefined : mdiContentCopy"
                                               :text="smAndDown ? undefined : t('listing.edit.duplicateButton')"
                                               type="default"
                                               v-bind="confirmProps"
                                               variant="text"/>
                                    </template>
                                </d-confirmation-wrapper>

                                <!-- VIEW -->
                                <d-btn :icon="smAndDown ? mdiCheck : undefined"
                                       :prepend-icon="smAndDown ? undefined : mdiCheck"
                                       :size="smAndDown ? 'default' : 'large'"
                                       :text="smAndDown ? undefined : t('listing.edit.showButton')"
                                       :to="selectedPage
                                            ? listingContextRouteForFlowConfigPage(listingId, selectedPage, 'VIEW')
                                            : {
                                                name: 'viewListing',
                                                params: {
                                                     id: listingId
                                                }
                                              }
                                   "
                                       class="ms-2"
                                       type="secondary"/>
                            </v-layout>
                        </v-col>
                    </v-row>
                </d-toolbar>
            </d-card>

            <v-container v-if="DEBUG_FLOW_CONFIG"
                         class="position-relative"
                         fluid
                         style="z-index: 1000; background-color: black; max-height: 300px; overflow-y: auto;">
                <v-row>
                    <v-col>
                        ALT
                    </v-col>
                    <v-col>
                        NEU
                    </v-col>
                    <v-col :class="{'text-red font-weight-bold': canSave}">
                        DELTA
                    </v-col>
                    <v-col>
                        SUGGESTIONS
                    </v-col>
                </v-row>
                <v-row>
                    <v-col class="overflow-auto">
                        <pre>
                            {{ oldState }}
                        </pre>
                    </v-col>
                    <v-col class="overflow-auto">
                        <pre>
                            {{ newState }}
                        </pre>
                    </v-col>
                    <v-col class="overflow-auto">
                        <pre>
                            {{ deltaState }}
                        </pre>
                    </v-col>
                    <v-col class="overflow-auto">
                        <pre>
                            {{ suggestionsState }}
                        </pre>
                    </v-col>
                </v-row>
            </v-container>
        </template>

        <template #footer>
            <div :class="{small: smAndDown}"
                 class="footer">
                <d-divider v-if="mdAndUp"/>

                <div :class="{'pa-0': smAndDown, 'pa-4': mdAndUp}">
                    <d-fc-center-container :ignore-max-width="smAndDown"
                                           no-y-padding>
                        <d-card :elevation="smAndDown ? 4 : undefined"
                                :rounded="mdAndUp ? 'xl' : false"
                                class="footerCard"
                                variant="elevated">
                            <v-container :class="{'py-1 px-3': smAndDown}"
                                         class="py-2 px-3"
                                         fluid>
                                <v-row align="center"
                                       justify="space-between">
                                    <v-col>
                                        <d-btn :disabled="!canGoBack"
                                               :icon="xs ? mdiChevronLeft : undefined"
                                               :prepend-icon="xs ? undefined : mdiChevronLeft"
                                               :size="xs ? 'large' : 'x-large'"
                                               :text="xs ? undefined : t('listing.edit.navigateBackwardButton')"
                                               type="default"
                                               variant="tonal"
                                               @click="goBack"/>
                                    </v-col>
                                    <v-col class="text-body-2 text-center pageInfo"
                                           cols="auto">
                                        <div v-if="selectedPage"
                                             class="singleLine">
                                            <d-btn :text="flowConfigTranslator(selectedPage.titleShort)"
                                                   block
                                                   type="tertiary"
                                                   variant="text"
                                                   @click="onToggleNavigationDrawer"/>
                                        </div>
                                        {{
                                            t('listing.edit.pageIndicator', {
                                                currentPage: selectedPageIndex + 1,
                                                totalPages: pages.length
                                            })
                                        }}
                                    </v-col>
                                    <v-col class="text-end">
                                        <!--<d-btn v-if="canSave"
                                               :append-icon="!canGoNext || xs ? undefined : mdiChevronRight"
                                               :icon="xs ? mdiContentSave : undefined"
                                               :loading="isSaveLoading"
                                               :prepend-icon="xs ? undefined : mdiContentSave"
                                               :size="xs ? 'large' : 'x-large'"
                                               :text="xs ? undefined : t('listing.edit.saveButton')"
                                               type="primary"
                                               @click="save"/>-->
                                        <!--v-else-if="canGoNext"-->
                                        <d-btn v-if="canGoNext"
                                               :append-icon="xs ? undefined : mdiChevronRight"
                                               :icon="xs ? mdiChevronRight : undefined"
                                               :size="xs ? 'large' : 'x-large'"
                                               :text="xs ? undefined : t('listing.edit.navigateForwardButton')"
                                               type="primary"
                                               @click="goNext"/>
                                        <d-btn v-else
                                               :icon="xs ? mdiCheck : undefined"
                                               :prepend-icon="xs ? undefined : mdiCheck"
                                               :size="xs ? 'large' : 'x-large'"
                                               :text="xs ? undefined : t('listing.edit.doneButton')"
                                               :to="{name: 'viewListing'}"
                                               type="primary"/>
                                    </v-col>
                                </v-row>
                            </v-container>
                        </d-card>
                    </d-fc-center-container>
                </div>
            </div>
        </template>
    </d-flow-pages>
</template>

<script lang="ts"
        setup>
    import {computed, inject, onMounted, onUnmounted, provide, ref, shallowRef, toRaw, toRef, watch} from "vue";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import {DListingEditSubscribeToListingSuggestionsSubscriptionVariables, FlowConfigFieldValue, FlowConfigPage, ListingFieldSuggestion, useDListingEditDuplicateListingMutation, useDListingEditSaveListingMutation, useDListingEditSubscribeToListingSuggestionsSubscription} from "@/adapter/graphql/generated/graphql";
    import {createEmptyMutableFlowData, MutableFlowData} from "@/model/listing/FlowData";
    import {deserializeFlowConfigPageIndexFromRoute, isUIElementVisible, listingContextRouteForFlowConfigPage, useFlowConfig} from "@/components/flow-config/use-flow-config";
    import DFlowPages from "@/components/listing/d-flow-pages.vue";
    import {ListingContext} from "@/model/listing/ListingContext";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DFcCenterContainer from "@/components/flow-config/d-fc-center-container.vue";
    import {mdiCheck, mdiChevronLeft, mdiChevronRight, mdiContentCopy, mdiMenu, mdiSyncAlert} from "@mdi/js";
    import {useDisplay} from "vuetify";
    import DToolbar from "@/adapter/vuetify/theme/components/d-toolbar.vue";
    import DH4 from "@/adapter/vuetify/theme/components/text/headline/d-h4.vue";
    import DDivider from "@/adapter/vuetify/theme/components/divider/d-divider.vue";
    import {onBeforeRouteLeave, useRoute, useRouter} from "vue-router";
    import DListingButtonBack from "@/components/listing/fragments/d-listing-button-back.vue";
    import {useI18n} from "vue-i18n";
    import {Optional} from "@/model/Optional";
    import {LArrayIndexInjection, LFlowDataInjection, LListingContextInjection, LListingEnvironmentInjection, LListingIdInjection, LListingInjection, LMutableFlowDataInjection, LSelectedPageInjection, LSuggestionsInjection} from "@/components/listing/ListingInjectionKeys";
    import {FCFlowConfigIdInjection, FCFlowConfigInjection} from "@/components/flow-config/FlowConfigInjectionKey";
    import {ListingEnvironment} from "@/model/listing/ListingEnvironment";
    import debounce from "debounce";
    import DProgressCircular from "@/adapter/vuetify/theme/components/progress/d-progress-circular.vue";
    import {UseSubscriptionOptions} from "@vue/apollo-composable";
    import {ApolloError} from "@apollo/client";
    import {mergeFieldValues} from "@/adapter/graphql/mapper/buildinginput-to-building-mapper";
    import {INTERNET_SPEED, InternetSpeed} from "@/service/pwa/internet-speed";
    import {IS_NATIVE_APP} from "@/service/native-app/native-app";
    import DServiceWorkerStatus from "@/components/layout/d-service-worker-status.vue";
    import {EnsureDefined, mapFlowConfigFieldValueToInput} from "@/adapter/graphql/mapper/graphql-mapper";
    import {DEBUG_FLOW_CONFIG} from "@/components/flow-config/debug-flow-config";
    import DConfirmationWrapper from "@/components/fragment/d-confirmation-wrapper.vue";

    const flowConfig = inject(FCFlowConfigInjection)!
    const flowConfigId = inject(FCFlowConfigIdInjection)!
    const listing = inject(LListingInjection)!
    const listingId = inject(LListingIdInjection)!

    const context: ListingContext = "EDIT"
    provide(LListingContextInjection, toRef(context))

    const env = computed<ListingEnvironment>(() => ({
        context,
        layout: {
            fill: false,
            position: "DEFAULT"
        }
    }))
    provide(LListingEnvironmentInjection, env)

    provide(LArrayIndexInjection, toRef(null))

    const {xs, smAndDown, smAndUp, mdAndUp, mobile} = useDisplay()
    const {t} = useI18n()

    watch(listing, listing => {
        if (!listing.isEditable) {
            console.warn("Listing is not editable")
            router.replace({
                name: 'viewListing',
            })
            return
        }
    }, {
        immediate: true
    })

    const {
        mutate: duplicateListing,
        loading: isDuplicateListingLoading,
    } = useDListingEditDuplicateListingMutation()

    async function onDuplicateListing() {
        try {
            const result = await duplicateListing({
                listingId: listing.value.id
            })
            const newListingId = result?.data?.cloneListing.id ?? null
            if (newListingId) {
                await router.push({
                    name: 'editListing',
                    params: {
                        id: newListingId
                    }
                })
            } else {
                console.warn("Could not duplicate listing")
            }
        } catch (e) {
            console.warn("Could not duplicate listing", e)
        }
    }

    const route = useRoute()
    const router = useRouter()
    const isNavigationDrawerVisible = shallowRef<boolean>(false)

    const subscriptionOptions = computed<UseSubscriptionOptions>(() => ({
        enabled: INTERNET_SPEED.value >= InternetSpeed.MEDIUM
    }))
    const queryVariables = computed<DListingEditSubscribeToListingSuggestionsSubscriptionVariables>(() => ({
        listingId: listingId.value,
    }))
    const {
        result: listingSuggestionData,
        loading: areSuggestionsLoading, //TODO: <<<<<<<<<<
        onError,
        restart: restartSubscription
    } = useDListingEditSubscribeToListingSuggestionsSubscription(queryVariables, subscriptionOptions)

    const listingFieldSuggestions = computed<readonly ListingFieldSuggestion[]>(() => listingSuggestionData.value?.subscribeToListingFieldSuggestions as (ListingFieldSuggestion[] | undefined) ?? [])

    const listingSuggestionsError = shallowRef<Optional<ApolloError>>(null) //TODO: use it
    const retriesAfterError = 3
    let retriesLeft = retriesAfterError

    onError(async (error: ApolloError) => {
        console.warn("An error occurred while subscribing to listing suggestions", error)

        if (retriesLeft > 0) {
            console.log("Trying to resubscribe to listing suggestions …")

            --retriesLeft

            await new Promise(resolve => setTimeout(resolve, 1000))
            if (INTERNET_SPEED.value >= InternetSpeed.MEDIUM) {
                restartSubscription();
            }

            return
        }
        listingSuggestionsError.value = error
    })

    const listingSuggestions = computed<MutableFlowData>(() => {
        const flowData = createEmptyMutableFlowData()

        const suggestions = listingFieldSuggestions.value
            .filter(s => s.status === 'SUCCEEDED')
            .map(s => s.fieldValue);

        flowData.setFromInputRespectingNull(suggestions)
        return flowData
    })

    function restartListingSuggestions() {
        retriesLeft = retriesAfterError
        if (INTERNET_SPEED.value >= InternetSpeed.MEDIUM) {
            restartSubscription();
        }
    }

    const debouncedRestartListingSuggestions = debounce(restartListingSuggestions, 50)

    provide(LSuggestionsInjection, listingSuggestions)

    const flowData = inject(LFlowDataInjection)!

    const oldFlowData = ref<MutableFlowData>(createEmptyMutableFlowData()) //ref okay
    const newFlowData = ref<MutableFlowData>(createEmptyMutableFlowData()) //ref okay

    provide(LFlowDataInjection, newFlowData)
    provide(LMutableFlowDataInjection, newFlowData)

    watch(flowData, flowData => {
        oldFlowData.value = flowData.mutableDeepCopy()
        newFlowData.value = flowData.mutableDeepCopy()
    }, {
        deep: true,
        immediate: true
    })

    const oldState = computed<readonly FlowConfigFieldValue[]>(() => oldFlowData.value.generateInput(false))
    const newState = computed<readonly FlowConfigFieldValue[]>(() => newFlowData.value.generateInput(false))
    const deltaState = computed<readonly FlowConfigFieldValue[]>(() => oldFlowData.value.generateInputDelta(newFlowData.value, true))
    const suggestionsState = computed<readonly FlowConfigFieldValue[]>(() => listingSuggestions.value.generateInput(false))

    const canSave = computed<boolean>(() => listing.value.isEditable && deltaState.value.length > 0)

    const pages = computed<readonly FlowConfigPage[]>(() => {
            const fc = flowConfig.value
            if (fc === null) {
                return []
            }
            return DEBUG_FLOW_CONFIG
                ? fc.pagesEdit
                : fc.pagesEdit.filter(p => isUIElementVisible(
                    p,
                    null,
                    context,
                    listing.value,
                    newFlowData.value
                ))
        }
    )

    const selectedPageId = shallowRef<Optional<string>>(null)

    const selectedPage = computed<Optional<FlowConfigPage>>(() => pages.value.find(p => p.id === selectedPageId.value) ?? null)
    provide(LSelectedPageInjection, selectedPage)

    watch(route, route => {
        selectedPageId.value = deserializeFlowConfigPageIndexFromRoute(pages.value, route)
    }, {
        immediate: true
    })

    const selectedPageIndex = computed<number>({
        get() {
            return pages.value.findIndex(p => p.id === selectedPageId.value)
        },
        set(newIndex) {
            const currentPages = pages.value
            if (currentPages.length <= 0) {
                selectedPageId.value = null
            } else {
                selectedPageId.value = newIndex < 0 ? currentPages[0].id : pages.value[newIndex].id
            }
        }
    })

    const isAutoSaving = shallowRef<boolean>(false)
    // Track changes made during save operation
    const pendingSaveOperation = shallowRef<boolean>(false)
    // Track if there are changes that need to be saved after the current save operation completes
    const hasChangesAfterSave = shallowRef<boolean>(false)
    // Track user activity for inactivity timer
    const lastUserActivity = shallowRef<number>(Date.now())
    // Timer reference for inactivity save
    const inactivitySaveTimer = shallowRef<number | null>(null)

    const canGoBack = computed<boolean>(() => selectedPageIndex.value > 0)
    const canGoNext = computed<boolean>(() => selectedPageIndex.value < pages.value.length - 1)
    const shouldDisplayUnsavedChangesWarning = computed<boolean>(() => isAutoSaving.value || canSave.value)

    const {
        flowConfigTranslator,
    } = useFlowConfig()

    async function goBack() {
        // Save changes before navigating back
        if (canSave.value) {
            await saveIfNeeded()
        }

        if (canGoBack.value) {
            selectedPageIndex.value--
        }
    }

    async function goNext() {
        // Save changes before navigating forward
        if (canSave.value) {
            await saveIfNeeded()
        }

        if (canGoNext.value) {
            selectedPageIndex.value++
        }
    }

    function beforeUnloadCheck(event: BeforeUnloadEvent): string | boolean {
        if (shouldDisplayUnsavedChangesWarning.value) {
            event.preventDefault()
            return t('listing.edit.unsavedChangesWarning')
        }
        return false
    }

    onMounted(() => {
        window.addEventListener("beforeunload", beforeUnloadCheck)
    })
    onUnmounted(() => {
        window.removeEventListener("beforeunload", beforeUnloadCheck)
    })

    onBeforeRouteLeave(async (to, from) => {
        // If there are unsaved changes, save them before leaving
        if (canSave.value) {
            // Cancel any pending inactivity timer
            if (inactivitySaveTimer.value) {
                window.clearTimeout(inactivitySaveTimer.value)
                inactivitySaveTimer.value = null
            }

            // Try to save immediately without waiting for inactivity
            await saveIfNeeded()

            // If save failed or there are still changes, show warning
            if (shouldDisplayUnsavedChangesWarning.value) {
                return confirm(t('listing.edit.unsavedChangesWarning'))
            }
        }
        return true
    })

    const {
        mutate: saveListing,
        loading: isSaveListingLoading,
        error: saveListingError
    } = useDListingEditSaveListingMutation()

    // Inactivity timeout in milliseconds before saving
    const inactivityTimeout = computed(() => mobile.value ? 1000 : 750)

    // Function to update the last user activity timestamp
    function updateUserActivity() {
        lastUserActivity.value = Date.now()
        // If we're not already waiting to save after inactivity, start the timer
        if (!inactivitySaveTimer.value) {
            startInactivityTimer()
        }
    }

    // Start the inactivity timer
    function startInactivityTimer() {
        // Clear any existing timer
        if (inactivitySaveTimer.value) {
            window.clearTimeout(inactivitySaveTimer.value)
        }

        // Set a new timer
        inactivitySaveTimer.value = window.setTimeout(() => {
            if (canSave.value && !pendingSaveOperation.value) {
                isAutoSaving.value = true
                autosave()
            }
            inactivitySaveTimer.value = null
        }, inactivityTimeout.value)
    }

    // Clear the inactivity timer when component is unmounted
    onUnmounted(() => {
        if (inactivitySaveTimer.value) {
            window.clearTimeout(inactivitySaveTimer.value)
            inactivitySaveTimer.value = null
        }
    })

    // Watch for changes and update user activity
    watch(deltaState, () => {
        if (canSave.value) {
            updateUserActivity()

            // If there's a pending save operation, mark that we have changes after save
            if (pendingSaveOperation.value) {
                hasChangesAfterSave.value = true
            }
        }
    }, {
        deep: true
    })

    async function autosave() {
        // Don't start a new save if one is already in progress
        if (pendingSaveOperation.value) {
            hasChangesAfterSave.value = true
            return
        }

        await save(false)

        // If changes were made during the save operation, trigger another save
        if (hasChangesAfterSave.value) {
            hasChangesAfterSave.value = false
            // Small delay to allow UI to update
            setTimeout(() => {
                // Call autosave again to offload pending changes to the server
                autosave()
            }, 100)
        }
    }

    /**
     * Speichert Änderungen, falls welche vorhanden sind.
     * @returns true, wenn Änderungen gespeichert wurden, false sonst
     */
    async function saveIfNeeded(): Promise<boolean> {
        if (canSave.value && !pendingSaveOperation.value) {
            isAutoSaving.value = true
            await save(false)
            return true
        }
        return false
    }

    function handleErrorGracefully(e: unknown) {
        const message = "Could not save listing when editing."
        if (e instanceof Error) {
            console.warn(message, e.name, e.message)
        } else {
            console.warn(message, e);
        }
    }

    async function save(goToNext: boolean = true) {
        try {
            if (!canSave.value) {
                return
            }

            // Mark that we have a pending save operation
            pendingSaveOperation.value = true

            // Create a snapshot of the current state before saving
            const currentFlowData = newFlowData.value.mutableDeepCopy()

            const newFieldValues: readonly FlowConfigFieldValue[] = JSON.parse(JSON.stringify(toRaw(deltaState.value)))
            const newFieldValuesInput = newFieldValues.map(fv => mapFlowConfigFieldValueToInput(fv));

            await saveListing({
                listingFieldValues: {
                    listingId: listingId.value,
                    fieldValues: newFieldValuesInput
                },
                flowConfigId: flowConfigId.value
            }, {
                update: cache => {
                    // Wenn während des Speichervorgangs Änderungen vorgenommen wurden,
                    // müssen wir vorsichtig sein, um diese nicht zu überschreiben
                    if (hasChangesAfterSave.value) {
                        // Wir aktualisieren den Cache "jetzt" nicht, da wir die Änderungen später manuell verwalten.
                        // Da es nach dem Save noch weitere "pending changes" gibt, werden diese sofort nach Abschluss
                        // der aktuellen Save Operation erneut zu einem Save Call führen.
                        // Dieser wird letztlich doch noch den Cache aktualisieren, selbst dann, wenn wir offline sind.
                        return;
                    }

                    cache.modify({
                        id: cache.identify(listing.value),
                        broadcast: false,
                        fields: {
                            fieldValues(existingRawFieldValues: any = []): EnsureDefined<FlowConfigFieldValue>[] {
                                const existingFieldValues = existingRawFieldValues as readonly EnsureDefined<FlowConfigFieldValue>[];
                                return mergeFieldValues(existingFieldValues, newFieldValues);
                            }
                        }
                    });
                }
            });

            // Only update oldFlowData if no changes were made during the save operation
            if (!hasChangesAfterSave.value) {
                oldFlowData.value = currentFlowData
            } else {
                // If changes were made during save, only update oldFlowData with the saved fields
                // but preserve any changes made during the save operation
                const savedFieldIds = new Set(newFieldValues.map(fv => `${fv.field.id}_${fv.arrayIndex ?? 0}`))

                // Create a temporary flow data with the server response
                const serverFlowData = createEmptyMutableFlowData()
                serverFlowData.setFromInput(newFieldValues)

                // Update oldFlowData with only the fields that were saved
                const oldFlowDataCopy = oldFlowData.value.mutableDeepCopy()
                for (const fieldId of savedFieldIds) {
                    const [id, arrayIndex] = fieldId.split('_')
                    const field = {id}
                    const fieldWithArrayIndex = {field, arrayIndex: parseInt(arrayIndex)}
                    const value = serverFlowData.getValue({field, arrayIndex: parseInt(arrayIndex)})
                    if (value !== null) {
                        oldFlowDataCopy.setValue(fieldWithArrayIndex, value, false)
                    }
                }

                oldFlowData.value = oldFlowDataCopy
            }

            if (goToNext) {
                goNext()
            }

            debouncedRestartListingSuggestions()

        } catch (e: unknown) {
            handleErrorGracefully(e);
        } finally {
            isAutoSaving.value = false
            pendingSaveOperation.value = false
        }
    }

    function onToggleNavigationDrawer() {
        isNavigationDrawerVisible.value = !isNavigationDrawerVisible.value
    }
</script>

<style scoped>
    .header {
        border-top: 0;
        border-left: 0;
        border-right: 0;
        z-index: 2;
        padding-top: env(safe-area-inset-top);
    }

    .footer {
        padding-bottom: env(safe-area-inset-bottom);
    }

    .footer.small {
        padding-bottom: 0;
    }

    .footer.small .footerCard {
        padding-bottom: env(safe-area-inset-bottom);
        border-left: 0;
        border-right: 0;
        border-bottom: 0;
    }

    .singleLine {
        width: 12rem;
    }

    .singleLine :deep(.v-btn__content) {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline;
    }

    @media (max-width: 650px) {
        .singleLine {
            width: 10rem;
        }
    }

    @media (max-width: 390px) {
        .singleLine {
            width: 8rem;
        }
    }

    @media (max-width: 360px) {
        .singleLine {
            display: none;
        }
    }

    @media (max-width: 280px) {
        .pageInfo {
            display: none;
        }
    }
</style>