<template>
    <v-container fluid>
        <v-slide-y-transition>
            <v-row v-if="!renderer.isEvebiModeEnabled.value">
                <v-col>
                    <d-select v-model="selectedShapeType"
                              :clearable="false"
                              :display-name-supplier="st => t(`enums.shapeType.${st}`)"
                              :icon-supplier="st => ShapeTypeIcon[st]"
                              :id-supplier="st => st"
                              :items="ShapeTypeValues"
                              :label="t('listing.building.cad.selection.wall.shape')"/>
                </v-col>
            </v-row>
        </v-slide-y-transition>

        <v-slide-y-transition>
            <v-row v-if="wall.isExterior === true && wall.isPartition !== true && !renderer.isEvebiModeEnabled.value">
                <v-col>
                    <d-switch-input v-model="isIntermediate"
                                    :hint="t('listing.building.cad.selection.wall.isIntermediate.info')"
                                    :icon="WallTypeIcon['INTERMEDIATE']"
                                    :label="t('listing.building.cad.selection.wall.isIntermediate.label')"/>
                </v-col>
            </v-row>
        </v-slide-y-transition>

        <v-slide-y-transition>
            <v-row v-if="!renderer.isEvebiModeEnabled.value && (selectedShapeType === 'Box' || selectedShapeType === 'Polygon')">
                <v-col>
                    <v-layout class="align-center justify-space-between overflow-visible">
                        <d-text-field v-model="wallWidthCm"
                                      :label="t('listing.building.cad.selection.wall.width')"
                                      :prepend-inner-icon="smAndDown ? undefined : mdiArrowExpandHorizontal"
                                      :value-max="100*100"
                                      :value-min="Math.round(BUILDING_WALL_MIN_WIDTH * 100)"
                                      center-input-text
                                      no-append-padding
                                      small-prepend-inner-icon
                                      type="number">
                            <template #appendInner>
                                <d-input-unit unit="CENTIMETER"/>
                            </template>
                        </d-text-field>

                        <span class="mx-2">×</span>

                        <d-text-field v-model="wallHeightCm"
                                      :bg-color="isHeightOverwriteMenuVisible ? themeColorBackgroundInfo : undefined"
                                      :class="{highlighted: isHeightOverwriteMenuVisible}"
                                      :label="t('listing.building.cad.selection.wall.height')"
                                      :prepend-inner-icon="smAndDown ? undefined : mdiArrowExpandVertical"
                                      :value-max="10*100"
                                      :value-min="Math.round(BUILDING_WALL_MIN_HEIGHT * 100)"
                                      center-input-text
                                      no-append-padding
                                      small-prepend-inner-icon
                                      type="number">
                            <template #appendInner>
                                <d-input-unit unit="CENTIMETER"/>
                            </template>
                        </d-text-field>

                        <span class="mx-2">×</span>

                        <d-text-field v-model="wallThicknessCm"
                                      :bg-color="isThicknessOverwriteMenuVisible ? themeColorBackgroundInfo : undefined"
                                      :class="{highlighted: isThicknessOverwriteMenuVisible}"
                                      :label="t('listing.building.cad.selection.wall.thickness')"
                                      :prepend-inner-icon="smAndDown ? undefined : mdiArrowExpand"
                                      :value-max="10*100"
                                      :value-min="1"
                                      center-input-text
                                      no-append-padding
                                      small-prepend-inner-icon
                                      type="number">
                            <template #appendInner>
                                <d-input-unit unit="CENTIMETER"/>
                            </template>
                        </d-text-field>
                    </v-layout>
                </v-col>
            </v-row>
        </v-slide-y-transition>

        <v-slide-y-transition>
            <v-row v-if="selectedShapeType === 'Ring' && !renderer.isEvebiModeEnabled.value">
                <v-col>
                    <v-layout class="align-center justify-space-between overflow-visible">
                        <d-text-field v-model="innerRadiusCm"
                                      :class="{'me-4': !smAndDown, 'me-2': smAndDown}"
                                      :label="t('listing.building.cad.selection.wall.innerRadius')"
                                      :prepend-inner-icon="smAndDown ? undefined : mdiRadiusOutline"
                                      :value-max="100*100"
                                      :value-min="1"
                                      center-input-text
                                      no-append-padding
                                      small-prepend-inner-icon
                                      type="number">
                            <template #appendInner>
                                <d-input-unit unit="CENTIMETER"/>
                            </template>
                        </d-text-field>

                        <d-text-field v-model="wallHeightCm"
                                      :class="{'me-4': !smAndDown, 'me-2': smAndDown}"
                                      :label="t('listing.building.cad.selection.wall.height')"
                                      :prepend-inner-icon="smAndDown ? undefined : mdiArrowExpandVertical"
                                      :value-max="10*100"
                                      :value-min="Math.round(BUILDING_WALL_MIN_HEIGHT * 100)"
                                      center-input-text
                                      no-append-padding
                                      small-prepend-inner-icon
                                      type="number">
                            <template #appendInner>
                                <d-input-unit unit="CENTIMETER"/>
                            </template>
                        </d-text-field>

                        <d-text-field v-model="wallThicknessCm"
                                      :label="t('listing.building.cad.selection.wall.thickness')"
                                      :prepend-inner-icon="smAndDown ? undefined : mdiArrowExpand"
                                      :value-max="10*100"
                                      :value-min="1"
                                      center-input-text
                                      no-append-padding
                                      small-prepend-inner-icon
                                      type="number">
                            <template #appendInner>
                                <d-input-unit unit="CENTIMETER"/>
                            </template>
                        </d-text-field>
                    </v-layout>
                </v-col>
            </v-row>
        </v-slide-y-transition>

        <v-slide-y-transition>
            <v-row v-if="!renderer.isEvebiModeEnabled.value"
                   dense>
                <v-col class="pe-0 text-end pb-0">
                    <d-listing-building-selection-overwrite-menu v-model="isHeightOverwriteMenuVisible"
                                                                 :config="heightOverwriteMenuConfig"/>
                </v-col>
            </v-row>
        </v-slide-y-transition>

        <v-slide-y-transition>
            <v-row v-if="!renderer.isEvebiModeEnabled.value"
                   class="mt-0"
                   dense>
                <v-col class="pe-0 text-end pt-0">
                    <d-listing-building-selection-overwrite-menu v-model="isThicknessOverwriteMenuVisible"
                                                                 :config="thicknessOverwriteMenuConfig"/>
                </v-col>
            </v-row>
        </v-slide-y-transition>

        <v-slide-y-transition>
            <v-row v-if="selectedShapeType === 'Polygon' && !renderer.isEvebiModeEnabled.value"
                   class="mt-4"
                   dense>
                <v-col :style="{'aspect-ratio': polygonVerticesAspectRatio}"
                       style="width: 100%; max-height: 300px; min-height: 200px;">
                    <!--                    SUM: {{ Math.round(polygonPerimeter * 100) }}cm<br>-->
                    <!--                    top: {{ Math.round(polygonTopPerimeter * 100) }}cm<br>-->
                    <!--                    topAndCenter: {{ Math.round(polygonTopAndCenterPerimeter * 100) }}cm<br>-->
                    <!--                    factor: {{ (polygonTopPerimeter / polygonTopAndCenterPerimeter * 100).toFixed(2) }}%-->
                    <!--                    <ul>-->
                    <!--                        <li v-for="(edge, index) in polygonEdges"-->
                    <!--                            :key="index">{{ Math.round(edge.lineSegment.start.distanceTo(edge.lineSegment.end) * 100) }}cm ({{ edge.type }})-->
                    <!--                        </li>-->
                    <!--                    </ul>-->
                    <d-wall-editor v-model:vertices="polygonVertices"
                                   :neighbor-heights-left="wallNeighborHeightsLeft"
                                   :neighbor-heights-right="wallNeighborHeightsRight"
                                   embedded/>
                </v-col>
            </v-row>
        </v-slide-y-transition>

        <v-slide-y-transition>
            <v-row v-if="selectedShapeType === 'Polygon' && !renderer.isEvebiModeEnabled.value"
                   class="mt-0"
                   dense
                   justify="end">
                <v-col class="pe-0"
                       cols="auto">
                    <d-btn :disabled="renderer.isLoading.value"
                           :prepend-icon="mdiVectorPolygonVariant"
                           :text="t('listing.building.cad.selection.wall.editShapeButton')"
                           type="default"
                           variant="text"
                           @click="onEditShape"/>
                </v-col>
            </v-row>
        </v-slide-y-transition>

        <v-slide-y-transition>
            <v-row v-if="selectedShapeType === 'Ring' && !renderer.isEvebiModeEnabled.value"
                   dense>
                <v-col>
                    <v-layout class="justify-space-between align-center mx-1">
                        <v-label class="text-center text-caption d-block">{{ t('listing.building.cad.selection.wall.startAngle') }}</v-label>
                        <v-label class="text-center text-caption d-block">{{ t('listing.building.cad.selection.wall.endAngle') }}</v-label>
                    </v-layout>
                    <v-range-slider v-model="angleRange"
                                    :max="360"
                                    :min="0"
                                    class="mx-0"
                                    step="1">
                        <template #prepend>
                            <d-text-field v-model="angleRange[0]"
                                          :prepend-inner-icon="smAndDown ? undefined : mdiCircleSlice1"
                                          :style="{'max-width': `${smAndDown ? 75 : 101}px`}"
                                          :value-max="360"
                                          :value-min="0"
                                          center-input-text
                                          no-append-padding
                                          small-prepend-inner-icon
                                          type="number">
                                <template #appendInner>
                                    <d-input-unit unit="DEGREE_OF_ARC"/>
                                </template>
                            </d-text-field>
                        </template>
                        <template #append>
                            <d-text-field v-model="angleRange[1]"
                                          :prepend-inner-icon="smAndDown ? undefined : mdiCircleSlice5"
                                          :style="{'max-width': `${smAndDown ? 75 : 101}px`}"
                                          :value-max="360"
                                          :value-min="0"
                                          center-input-text
                                          no-append-padding
                                          small-prepend-inner-icon
                                          type="number">
                                <template #appendInner>
                                    <d-input-unit unit="DEGREE_OF_ARC"/>
                                </template>
                            </d-text-field>
                        </template>
                    </v-range-slider>
                </v-col>
            </v-row>
        </v-slide-y-transition>

        <!--        <v-row>-->
        <!--            <v-col>-->
        <!--                LINKS-->
        <!--            </v-col>-->
        <!--            <v-col cols="auto">-->
        <!--                <ul>-->
        <!--                    <li v-for="leftNeighborWall in neighborWallsLeft"-->
        <!--                        :key="leftNeighborWall.id">-->
        <!--                        #{{ leftNeighborWall.displayId }} [{{ calculateNeighborAngle(renderer, wall, leftNeighborWall, 'LEFT') }}°]-->
        <!--                    </li>-->
        <!--                </ul>-->
        <!--            </v-col>-->
        <!--        </v-row>-->
        <!--        <v-row>-->
        <!--            <v-col>-->
        <!--                LINKS INNEN VALID-->
        <!--            </v-col>-->
        <!--            <v-col cols="auto">-->
        <!--                <ul>-->
        <!--                    <li v-for="leftNeighborWall in neighborWallsLeftInteriorValid"-->
        <!--                        :key="leftNeighborWall.id">-->
        <!--                        #{{ leftNeighborWall.displayId }} [{{ calculateNeighborAngle(renderer, wall, leftNeighborWall, 'LEFT') }}°]-->
        <!--                    </li>-->
        <!--                </ul>-->
        <!--            </v-col>-->
        <!--        </v-row>-->
        <!--        <v-row>-->
        <!--            <v-col>-->
        <!--                LINKS AUßEN VALID (-)-->
        <!--            </v-col>-->
        <!--            <v-col cols="auto">-->
        <!--                <ul>-->
        <!--                    <li v-for="leftNeighborWall in neighborWallsLeftExteriorMinusValid"-->
        <!--                        :key="leftNeighborWall.id">-->
        <!--                        #{{ leftNeighborWall.displayId }} [{{ calculateNeighborAngle(renderer, wall, leftNeighborWall, 'LEFT') }}°]-->
        <!--                    </li>-->
        <!--                </ul>-->
        <!--            </v-col>-->
        <!--        </v-row>-->
        <!--        <v-row>-->
        <!--            <v-col>-->
        <!--                LINKS AUßEN VALID (+)-->
        <!--            </v-col>-->
        <!--            <v-col cols="auto">-->
        <!--                <ul>-->
        <!--                    <li v-for="leftNeighborWall in neighborWallsLeftExteriorPlusValid"-->
        <!--                        :key="leftNeighborWall.id">-->
        <!--                        #{{ leftNeighborWall.displayId }} [{{ calculateNeighborAngle(renderer, wall, leftNeighborWall, 'LEFT') }}°]-->
        <!--                    </li>-->
        <!--                </ul>-->
        <!--            </v-col>-->
        <!--        </v-row>-->
        <!--        <v-row>-->
        <!--            <v-col>-->
        <!--                RECHTS-->
        <!--            </v-col>-->
        <!--            <v-col cols="auto">-->
        <!--                <ul>-->
        <!--                    <li v-for="rightNeighborWall in neighborWallsRight"-->
        <!--                        :key="rightNeighborWall.id">-->
        <!--                        #{{ rightNeighborWall.displayId }} [{{ calculateNeighborAngle(renderer, wall, rightNeighborWall, 'RIGHT') }}°]-->
        <!--                    </li>-->
        <!--                </ul>-->
        <!--            </v-col>-->
        <!--        </v-row>-->
        <!--        <v-row>-->
        <!--            <v-col>-->
        <!--                RECHTS INNEN VALID-->
        <!--            </v-col>-->
        <!--            <v-col cols="auto">-->
        <!--                <ul>-->
        <!--                    <li v-for="rightNeighborWall in neighborWallsRightInteriorValid"-->
        <!--                        :key="rightNeighborWall.id">-->
        <!--                        #{{ rightNeighborWall.displayId }} [{{ calculateNeighborAngle(renderer, wall, rightNeighborWall, 'RIGHT') }}°]-->
        <!--                    </li>-->
        <!--                </ul>-->
        <!--            </v-col>-->
        <!--        </v-row>-->
        <!--        <v-row>-->
        <!--            <v-col>-->
        <!--                RECHTS AUßEN VALID (-)-->
        <!--            </v-col>-->
        <!--            <v-col cols="auto">-->
        <!--                <ul>-->
        <!--                    <li v-for="rightNeighborWall in neighborWallsRightExteriorMinusValid"-->
        <!--                        :key="rightNeighborWall.id">-->
        <!--                        #{{ rightNeighborWall.displayId }} [{{ calculateNeighborAngle(renderer, wall, rightNeighborWall, 'RIGHT') }}°]-->
        <!--                    </li>-->
        <!--                </ul>-->
        <!--            </v-col>-->
        <!--        </v-row>-->
        <!--        <v-row>-->
        <!--            <v-col>-->
        <!--                RECHTS AUßEN VALID (+)-->
        <!--            </v-col>-->
        <!--            <v-col cols="auto">-->
        <!--                <ul>-->
        <!--                    <li v-for="rightNeighborWall in neighborWallsRightExteriorPlusValid"-->
        <!--                        :key="rightNeighborWall.id">-->
        <!--                        #{{ rightNeighborWall.displayId }} [{{ calculateNeighborAngle(renderer, wall, rightNeighborWall, 'RIGHT') }}°]-->
        <!--                    </li>-->
        <!--                </ul>-->
        <!--            </v-col>-->
        <!--        </v-row>-->
        <!--        <v-row>-->
        <!--            <v-col>-->
        <!--                ORTHOGONALE INNEN ({{ orthogonalInteriorWalls.length }})-->
        <!--            </v-col>-->
        <!--            <v-col cols="auto">-->
        <!--                <ul>-->
        <!--                    <li v-for="otherWall in orthogonalInteriorWalls"-->
        <!--                        :key="otherWall.id">-->
        <!--                        #{{ otherWall.displayId }} [{{ calculateNeighborAngle(renderer, wall, otherWall, 'LEFT') }}°]-->
        <!--                    </li>-->
        <!--                </ul>-->
        <!--            </v-col>-->
        <!--        </v-row>-->

        <!--        <v-row>-->
        <!--            <v-col>-->
        <!--                VALID ORTHOGONALE INNEN ({{ possibleSizeAdjustmentXRelatedWalls.length }})-->
        <!--            </v-col>-->
        <!--            <v-col cols="auto">-->
        <!--                <ul>-->
        <!--                    <li v-for="otherWall in possibleSizeAdjustmentXRelatedWalls"-->
        <!--                        :key="otherWall.id">-->
        <!--                        #{{ otherWall.displayId }} [{{ calculateNeighborAngle(renderer, wall, otherWall, 'LEFT') }}°]-->
        <!--                    </li>-->
        <!--                </ul>-->
        <!--            </v-col>-->
        <!--        </v-row>-->

        <!--        <v-row>-->
        <!--            <v-col>-->
        <!--                VERBLEIB ORTHOGONALE INNEN ({{ remainingPossibleSizeAdjustmentXRelatedWalls.length }})-->
        <!--            </v-col>-->
        <!--            <v-col cols="auto">-->
        <!--                <ul>-->
        <!--                    <li v-for="otherWall in remainingPossibleSizeAdjustmentXRelatedWalls"-->
        <!--                        :key="otherWall.id">-->
        <!--                        #{{ otherWall.displayId }} [{{ calculateNeighborAngle(renderer, wall, otherWall, 'LEFT') }}°]-->
        <!--                    </li>-->
        <!--                </ul>-->
        <!--            </v-col>-->
        <!--        </v-row>-->

        <!--        <v-row>-->
        <!--            <v-col>-->
        <!--                GEWÄHLTE ORTHOGONALE INNEN ({{ sizeAdjustmentUserXRelatedWalls.length }})-->
        <!--            </v-col>-->
        <!--            <v-col cols="auto">-->
        <!--                <ul>-->
        <!--                    <li v-for="otherWall in sizeAdjustmentUserXRelatedWalls"-->
        <!--                        :key="otherWall.id">-->
        <!--                        #{{ otherWall.displayId }} [{{ calculateNeighborAngle(renderer, wall, otherWall, 'LEFT') }}°]-->
        <!--                    </li>-->
        <!--                </ul>-->
        <!--            </v-col>-->
        <!--        </v-row>-->

        <v-slide-y-transition>
            <v-row v-if="renderer.isEvebiModeEnabled.value && wall.shapeRepresentation.shape.__typename === 'Ring'"
                   dense>
                <v-col>
                    <d-alert class="text-caption"
                             type="warning"><p class="ms-2">{{ t('listing.building.cad.selection.wall.evebi.ringWallWarning') }}</p>
                    </d-alert>
                </v-col>
            </v-row>
        </v-slide-y-transition>

        <v-slide-y-transition>
            <v-row v-if="renderer.isEvebiModeEnabled.value">
                <v-col class="text-caption pe-0">{{ t('listing.building.cad.selection.wall.evebi.description') }}</v-col>
                <v-col class="ps-0"
                       cols="auto">
                    <d-tooltip :width="400"
                               open-on-click
                               type="info">
                        <template #activator="{props: tooltipProps}">
                            <d-btn :icon="mdiInformation"
                                   size="x-small"
                                   type="default"
                                   v-bind="tooltipProps"
                                   variant="text"/>
                        </template>

                        {{ t('listing.building.cad.selection.wall.evebi.info') }}
                    </d-tooltip>
                </v-col>
            </v-row>
        </v-slide-y-transition>

        <v-slide-y-transition>
            <v-row v-if="renderer.isEvebiModeEnabled.value && wall.isExterior === true">
                <v-col class="pb-2">
                    <d-h3>
                        <v-layout class="align-center">
                            <d-icon :icon="mdiArrowExpand"
                                    :size="24"
                                    class="me-2"
                                    type="default"/>
                            <div>{{ t('listing.building.cad.selection.wall.evebi.exteriorWallThickness') }}</div>
                        </v-layout>
                    </d-h3>
                </v-col>
            </v-row>
        </v-slide-y-transition>

        <v-slide-y-transition>
            <v-row v-if="renderer.isEvebiModeEnabled.value && wall.isExterior === true"
                   dense>
                <v-col>
                    <d-text-field v-model="wallEvebiThicknessCm"
                                  :bg-color="isEvebiThicknessOverwriteMenuVisible ? themeColorBackgroundInfo : undefined"
                                  :class="{highlighted: isEvebiThicknessOverwriteMenuVisible}"
                                  :value-max="10*100"
                                  :value-min="0"
                                  end-input-text
                                  type="number">
                        <template #appendInner>
                            <d-input-unit unit="CENTIMETER"/>
                        </template>
                    </d-text-field>
                </v-col>
            </v-row>
        </v-slide-y-transition>

        <v-slide-y-transition>
            <v-row v-if="renderer.isEvebiModeEnabled.value && wall.isExterior === true"
                   dense>
                <v-col class="pe-0 text-end">
                    <d-listing-building-selection-overwrite-menu v-model="isEvebiThicknessOverwriteMenuVisible"
                                                                 :config="evebiThicknessOverwriteMenuConfig"/>
                </v-col>
            </v-row>
        </v-slide-y-transition>

        <v-slide-y-transition>
            <v-row v-if="renderer.isEvebiModeEnabled.value">
                <v-col class="pb-2">
                    <d-h3>
                        <v-layout class="align-center">
                            <d-icon :icon="mdiArrowExpandHorizontal"
                                    :size="24"
                                    class="me-2"
                                    type="default"/>
                            <div>{{ t('listing.building.cad.selection.wall.width') }}</div>
                        </v-layout>
                    </d-h3>
                </v-col>
            </v-row>
        </v-slide-y-transition>

        <v-slide-y-transition>
            <v-row v-if="renderer.isEvebiModeEnabled.value"
                   dense>
                <v-col>
                    <v-table :class="themeLayerClass"
                             class="bg-transparent border rounded-lg"
                             density="compact">
                        <tbody>
                            <tr>
                                <td colspan="2">{{ t('listing.building.cad.selection.wall.evebi.interiorWidth') }}</td>
                                <td class="text-end">{{ n(wallWidthCm ?? 0, 'integer') }}</td>
                                <td class="text-end"
                                    style="width: 34px;">
                                    <d-input-unit no-margin
                                                  unit="CENTIMETER"/>
                                </td>
                            </tr>

                            <template v-if="sizeAdjustmentXResult">
                                <tr class="special">
                                    <th class="text-caption font-weight-regular"
                                        colspan="4"
                                        style="height: auto;">
                                        <v-layout class="align-center">
                                            <span>{{ t('listing.building.cad.selection.wall.evebi.neighborWallThicknessShares') }}</span>
                                            <d-icon :icon="mdiCreation"
                                                    :size="12"
                                                    class="ms-1"
                                                    type="magic"/>
                                        </v-layout>
                                    </th>
                                </tr>

                                <template v-for="(side, sideIndex) in sizeAdjustmentXResult.sides"
                                          :key="sideIndex">
                                    <tr v-for="(term, termIndex) in side!!.terms"
                                        :key="termIndex">
                                        <td class="ps-8 text-center">
                                            <div v-if="term.wall.displayId"
                                                 :style="{'background-color': doorbitColors.lightGreen}"
                                                 class="displayId">#{{ term.wall.displayId }}
                                            </div>
                                        </td>
                                        <!--                                        <td class="text-center">-->
                                        <!--                                            <d-icon :icon="mdiCreation"-->
                                        <!--                                                    :size="12"-->
                                        <!--                                                    type="magic"/>-->
                                        <!--                                        </td>-->
                                        <td class="subinfo text-caption text-center">({{ typeNameOfWall(term.wall, t) }})</td>
                                        <td class="text-end">{{ n(term.value * 100, 'integer') }}</td>
                                        <td class="text-end">
                                            <d-input-unit no-margin
                                                          unit="CENTIMETER"/>
                                        </td>
                                    </tr>
                                </template>
                            </template>

                            <!--                            <tr v-for="relatedWall in sizeAdjustmentUserXRelatedWalls"-->
                            <!--                                :key="relatedWall.id">-->
                            <!--                                <td class="ps-8 text-center">-->
                            <!--                                    <div v-if="relatedWall.displayId"-->
                            <!--                                         :style="{'background-color': doorbitColors.lightGreen}"-->
                            <!--                                         class="displayId">#{{ relatedWall.displayId }}-->
                            <!--                                    </div>-->
                            <!--                                </td>-->
                            <!--                                <td class="text-center">-->
                            <!--                                    <d-btn :icon="mdiDelete"-->
                            <!--                                           :loading="renderer.isLoading.value"-->
                            <!--                                           size="x-small"-->
                            <!--                                           type="default"-->
                            <!--                                           variant="text"-->
                            <!--                                           @click="onRemoveSizeAdjustmentUserXRelatedWall(relatedWall)"/>-->
                            <!--                                </td>-->
                            <!--                                <td class="subinfo text-caption text-center">({{ typeNameOfWall(relatedWall, t) }})</td>-->
                            <!--                                <td class="text-end">{{ n(adjustedWallThickness(renderer, relatedWall, false) * 100, 'integer') }}</td>-->
                            <!--                                <td class="text-end">-->
                            <!--                                    <d-input-unit no-margin-->
                            <!--                                                  unit="CENTIMETER"/>-->
                            <!--                                </td>-->
                            <!--                            </tr>-->

                            <!--                            <v-slide-y-transition>-->
                            <!--                                <tr v-if="remainingPossibleSizeAdjustmentXRelatedWalls.length > 0"-->
                            <!--                                    class="special">-->
                            <!--                                    <td class="ps-8 pe-2 py-1"-->
                            <!--                                        colspan="3">-->
                            <!--                                        <d-select v-model="sizeAdjustmentUserXRelatedWall"-->
                            <!--                                                  :display-name-supplier="w => `#${w.displayId ?? ''}`"-->
                            <!--                                                  :id-supplier="w => w.id"-->
                            <!--                                                  :items="remainingPossibleSizeAdjustmentXRelatedWalls"-->
                            <!--                                                  :loading="renderer.isLoading.value"-->
                            <!--                                                  :placeholder="t('listing.building.cad.selection.wall.evebi.addRelatedWallPlaceholder')"-->
                            <!--                                                  small-->
                            <!--                                                  @click="onSizeAdjustmentUserXRelatedWallSelectionClicked"/>-->
                            <!--                                    </td>-->
                            <!--                                    <td colspan="2"/>-->
                            <!--                                </tr>-->
                            <!--                            </v-slide-y-transition>-->

                            <!--                            <tr>-->
                            <!--                                <td colspan="3">{{ t('listing.building.cad.selection.wall.evebi.correction') }}</td>-->
                            <!--                                <td class="py-2"-->
                            <!--                                    colspan="1">-->
                            <!--                                    <d-text-field v-model="wallEvebiWidthCorrectionCm"-->
                            <!--                                                  :value-max="10*100"-->
                            <!--                                                  :value-min="-10*100"-->
                            <!--                                                  class="float-end"-->
                            <!--                                                  end-input-text-->
                            <!--                                                  no-append-padding-->
                            <!--                                                  small-->
                            <!--                                                  style="width: 75px;"-->
                            <!--                                                  type="number"/>-->
                            <!--                                </td>-->
                            <!--                                <td class="text-end">-->
                            <!--                                    <d-input-unit no-margin-->
                            <!--                                                  unit="CENTIMETER"/>-->
                            <!--                                </td>-->
                            <!--                            </tr>-->
                        </tbody>
                        <tfoot>
                            <tr>
                                <th colspan="2">{{ t('listing.building.cad.selection.wall.evebi.exteriorWidth') }}</th>
                                <th class="text-end sum">{{ n(adjustedWallWidth(renderer, wall, false) * 100, 'integer') }}</th>
                                <th class="text-end">
                                    <d-input-unit no-margin
                                                  unit="CENTIMETER"/>
                                </th>
                            </tr>
                        </tfoot>
                    </v-table>
                </v-col>
            </v-row>
        </v-slide-y-transition>

        <v-slide-y-transition>
            <v-row v-if="renderer.isEvebiModeEnabled.value">
                <v-col class="pb-2">
                    <d-h3>
                        <v-layout class="align-center">
                            <d-icon :icon="mdiArrowExpandVertical"
                                    :size="24"
                                    class="me-2"
                                    type="default"/>
                            <div>{{ t('listing.building.cad.selection.wall.height') }}</div>
                        </v-layout>
                    </d-h3>
                </v-col>
            </v-row>
        </v-slide-y-transition>

        <v-slide-y-transition>
            <v-row v-if="renderer.isEvebiModeEnabled.value"
                   dense>
                <v-col>
                    <v-table :class="themeLayerClass"
                             class="bg-transparent border rounded-lg"
                             density="compact">
                        <tbody>
                            <tr>
                                <td colspan="2">{{ t('listing.building.cad.selection.wall.evebi.interiorHeight') }}</td>
                                <td class="text-end">{{ n(wallHeightCm ?? 0, 'integer') }}</td>
                                <td class="text-end"
                                    style="width: 34px;">
                                    <d-input-unit no-margin
                                                  unit="CENTIMETER"/>
                                </td>
                            </tr>

                            <template v-if="sizeAdjustmentYResult">
                                <tr class="special">
                                    <th class="text-caption font-weight-regular"
                                        colspan="4"
                                        style="height: auto;">
                                        <v-layout class="align-center">
                                            <span>{{ t('listing.building.cad.selection.wall.evebi.slabHeightShares') }}</span>
                                            <d-icon :icon="mdiCreation"
                                                    :size="12"
                                                    class="ms-1"
                                                    type="magic"/>
                                        </v-layout>
                                    </th>
                                </tr>

                                <template v-for="(side, sideIndex) in sizeAdjustmentYResult.sides"
                                          :key="sideIndex">
                                    <tr>
                                        <td :colspan="side.info === undefined ? 2 : undefined"
                                            class="ps-8">
                                            <v-layout class="align-center">
                                                {{ t(`listing.building.cad.selection.wall.evebi.slabTypes.${side.type}`) }}
                                                <d-listing-building-select-component-button :emitter-id="BuildingRenderer.selectionEmitterIdForFloor(side.floor.id)"/>
                                            </v-layout>
                                        </td>
                                        <td v-if="side.info !== undefined"
                                            class="subinfo text-caption text-center">
                                            <v-layout class="align-center justify-center">
                                                <div>(</div>
                                                <div>{{ t('listing.building.cad.selection.wall.evebi.slabHeightSharesPolygon', {percent: n(side.info.percent, 'percentDecimal'), height: n(side.info.orginalSizeAdjustmentY * 100, 'integer')}) }}</div>
                                                <d-input-unit class="ms-1"
                                                              no-margin
                                                              unit="CENTIMETER"/>
                                                <div>)</div>
                                            </v-layout>
                                        </td>
                                        <td class="text-end">{{ n(side.sizeAdjustmentY * 100, 'integer') }}</td>
                                        <td class="text-end">
                                            <d-input-unit no-margin
                                                          unit="CENTIMETER"/>
                                        </td>
                                    </tr>
                                </template>
                            </template>

                            <!--                            <tr>-->
                            <!--                                <td colspan="2">{{ t('listing.building.cad.selection.wall.evebi.correction') }}</td>-->
                            <!--                                <td class="py-2">-->
                            <!--                                    <d-text-field v-model="wallEvebiHeightCorrectionCm"-->
                            <!--                                                  :value-max="10*100"-->
                            <!--                                                  :value-min="-10*100"-->
                            <!--                                                  class="float-end"-->
                            <!--                                                  end-input-text-->
                            <!--                                                  inline-->
                            <!--                                                  no-append-padding-->
                            <!--                                                  small-->
                            <!--                                                  style="width: 75px;"-->
                            <!--                                                  type="number"/>-->
                            <!--                                </td>-->
                            <!--                                <td class="text-end">-->
                            <!--                                    <d-input-unit no-margin-->
                            <!--                                                  unit="CENTIMETER"/>-->
                            <!--                                </td>-->
                            <!--                            </tr>-->
                        </tbody>
                        <tfoot>
                            <tr>
                                <th colspan="2">{{ t('listing.building.cad.selection.wall.evebi.exteriorHeight') }}</th>
                                <th class="text-end sum">{{ n(adjustedWallHeight(renderer, wall, false) * 100, 'integer') }}</th>
                                <th class="text-end">
                                    <d-input-unit no-margin
                                                  unit="CENTIMETER"/>
                                </th>
                            </tr>
                        </tfoot>
                    </v-table>
                </v-col>
            </v-row>
        </v-slide-y-transition>

        <v-dialog v-if="selectedShapeType === 'Polygon' && !renderer.isEvebiModeEnabled.value"
                  v-model="isShapeDialogVisible"
                  fullscreen>
            <d-card is-in-dialog>
                <template #title>
                    <v-layout class="align-center justify-center position-relative">
                        {{ t('listing.building.cad.selection.wall.wallEditorDialog.title') }}

                        <d-btn :icon="mdiClose"
                               class="position-absolute"
                               style="right: 0;"
                               type="default"
                               variant="text"
                               @click="onCloseShapeDialog"/>
                    </v-layout>
                </template>
                <template #subtitle>
                    <v-layout class="align-center justify-center subtitleArea mt-4">
                        <d-sheet class="pa-2 pe-3"
                                 rounded="pill">
                            <d-icon :icon="IS_TOUCH_DEVICE ? mdiGestureTap : mdiMouseLeftClick"
                                    class="me-1"
                                    type="tertiary"/>
                            <span class="infoText">{{ t('listing.building.cad.selection.wall.wallEditorDialog.leftClickInfo') }}</span>
                        </d-sheet>

                        <d-sheet class="pa-2 pe-3 ms-6"
                                 rounded="pill">
                            <d-icon :icon="IS_TOUCH_DEVICE ? mdiGestureTapHold : mdiMouseRightClick"
                                    class="me-1"
                                    type="tertiary"/>
                            <span class="infoText">{{ t('listing.building.cad.selection.wall.wallEditorDialog.rightClickInfo') }}</span>
                        </d-sheet>
                    </v-layout>
                </template>

                <d-wall-editor ref="wallEditor"
                               v-model:vertices="polygonVertices"
                               :neighbor-heights-left="wallNeighborHeightsLeft"
                               :neighbor-heights-right="wallNeighborHeightsRight"/>

                <template #actions>
                    <v-layout class="align-center justify-center pa-4 dialogActions">
                        <d-btn :text="t('listing.building.cad.selection.wall.wallEditorDialog.resetVerticesButton')"
                               class="me-8"
                               size="x-large"
                               type="info"
                               @click="onResetWallEditorVertices"/>

                        <d-btn :text="t('listing.building.cad.selection.wall.wallEditorDialog.saveVerticesButton')"
                               size="x-large"
                               type="primary"
                               @click="onSaveWallEditorVertices"/>
                    </v-layout>
                </template>
            </d-card>
        </v-dialog>
    </v-container>
</template>

<script lang="ts"
        setup>
    import DSelect from "@/adapter/vuetify/theme/components/input/d-select.vue";
    import {useI18n} from "vue-i18n";
    import {computed, inject, onUnmounted, ref, shallowRef, toRef, watch, watchEffect} from "vue";
    import {adjustedWallHeight, adjustedWallWidth, BUILDING_EPSILON, BUILDING_WALL_MIN_HEIGHT, BUILDING_WALL_MIN_WIDTH, changeRingWallAngles, changeRingWallInnerRadius, changeVerticesOfPolygonWall, changeWallHeight, changeWallShape, changeWallThickness, changeWallWidth, createCmToMField, DBuildingRendererInjection, heightOfShape, thicknessOfShape, typeNameOfWall, wallIdToSnapPointId} from "@/components/listing/building/building";
    import {Box, Wall} from "@/adapter/graphql/generated/graphql";
    import {ShapeType, ShapeTypeIcon, ShapeTypeValues} from "@/model/building/ShapeType";
    import DTextField from "@/adapter/vuetify/theme/components/input/d-text-field.vue";
    import DInputUnit from "@/adapter/vuetify/theme/components/input/d-input-unit.vue";
    import DWallEditor from "@/components/wall-editor/d-wall-editor.vue";
    import debounce from "debounce";
    import {Vector2} from "three";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import {mdiArrowExpand, mdiArrowExpandHorizontal, mdiArrowExpandVertical, mdiCircleSlice1, mdiCircleSlice5, mdiClose, mdiCreation, mdiGestureTap, mdiGestureTapHold, mdiInformation, mdiMouseLeftClick, mdiMouseRightClick, mdiRadiusOutline, mdiVectorPolygonVariant} from "@mdi/js";
    import {emptySet} from "@/utility/set";
    import {Optional} from "@/model/Optional";
    import {areNumbersEqual} from "@/utility/number";
    import {areArraysEqual} from "@/utility/arrays";
    import {tAreVectors2Equal, tSizeOfVectors2} from "@/adapter/three/three-utility";
    import {IS_TOUCH_DEVICE} from "@/utility/environment";
    import DIcon from "@/adapter/vuetify/theme/components/d-icon.vue";
    import DSheet from "@/adapter/vuetify/theme/components/card/d-sheet.vue";
    import {useDisplay} from "vuetify";
    import {BuildingPipelineBuilder} from "@/components/listing/building/pipeline/BuildingPipelineBuilder";
    import DSwitchInput from "@/adapter/vuetify/theme/components/input/d-switch-input.vue";
    import {generateWallsOfTypeItems, OverwriteMenuConfig, OverwriteMenuConfigItem, useItemsForWallOverwriteMenuConfig} from "@/components/listing/building/overwrite-menu-config";
    import {useDoorbitTheme} from "@/adapter/vuetify/theme/doorbit-theme-provider";
    import {calculateSizeAdjustmentXFor, calculateSizeAdjustmentYFor, WallSizeAdjustmentXResult, WallSizeAdjustmentYResult} from "@/components/listing/building/pipeline/stages/RecalculateAllWallSizeAdjustmentsBPipelineStage";
    import DH3 from "@/adapter/vuetify/theme/components/text/headline/d-h3.vue";
    import {doorbitColors} from "@/adapter/vuetify/theme/doorbit-colors";
    import DAlert from "@/adapter/vuetify/theme/components/alert/d-alert.vue";
    import {WallCreator} from "@/components/listing/building/renderer/WallCreator";
    import DTooltip from "@/adapter/vuetify/theme/components/d-tooltip.vue";
    import DListingBuildingSelectionOverwriteMenu from "@/components/listing/building/d-listing-building-selection-overwrite-menu.vue";
    import {WallTypeIcon, WallTypeValues} from "@/model/building/WallType";
    import DListingBuildingSelectComponentButton from "@/components/listing/building/d-listing-building-select-component-button.vue";
    import {BuildingRenderer} from "@/components/listing/building/renderer/BuildingRenderer";

    const props = defineProps<{
        wall: Wall
    }>()

    const debounceDelay = 1_500

    const isShapeDialogVisible = shallowRef<boolean>(false)
    const wallEditor = ref<InstanceType<typeof DWallEditor>>()

    const {smAndDown} = useDisplay()
    const renderer = inject(DBuildingRendererInjection)!

    // const polygonEdges = computed<PolygonEdge[]>(() => {
    //     const shape = props.wall.shapeRepresentation.shape
    //     if (shape.__typename !== "Polygon") {
    //         return []
    //     }
    //     return calculateEdgesOfPolygon(shape)
    // })
    // const polygonPerimeter = computed<number>(() => {
    //     const shape = props.wall.shapeRepresentation.shape
    //     if (shape.__typename !== "Polygon") {
    //         return -1
    //     }
    //     return calculatePermimeterOfPolygon(shape, polygonEdges.value)
    // })
    // const polygonTopPerimeter = computed<number>(() => {
    //     const shape = props.wall.shapeRepresentation.shape
    //     if (shape.__typename !== "Polygon") {
    //         return -1
    //     }
    //     return calculateTopPerimeterOfPolygon(shape, polygonEdges.value)
    // })
    // const polygonTopAndCenterPerimeter = computed<number>(() => {
    //     const shape = props.wall.shapeRepresentation.shape
    //     if (shape.__typename !== "Polygon") {
    //         return -1
    //     }
    //     return calculateTopAndCenterPerimeterOfPolygon(shape, polygonEdges.value)
    // })

    const {
        themeLayerClass,
        themeColorBackgroundInfo,
    } = useDoorbitTheme()

    watch(isShapeDialogVisible, isVisible => {
        renderer.isEscapeButtonEnabled = !isVisible
    })

    onUnmounted(() => {
        renderer.isEscapeButtonEnabled = true
    })

    // function onSizeAdjustmentUserXRelatedWallSelectionClicked() {
    //     renderer.showDisplayIds.value = true
    // }

    function snapPointIdToHeights(snapPointId: string, floorLevel: number): ReadonlySet<number> {
        const wallSnapPoints = renderer.snappingManager.snapPointIdToSnappedSnapPoints(snapPointId, floorLevel)

        if (wallSnapPoints.length <= 0) {
            return emptySet()
        }

        const wallSnapPointComponents = renderer
            .wallSnapPointComponentsOfSnapPoints(wallSnapPoints)
            .filter(c => c.component.value.id !== WallCreator.TEMP_WALL_ID)
        if (wallSnapPointComponents.length <= 0) {
            return emptySet()
        }

        const heights = wallSnapPointComponents.map(c => heightOfShape(c.wallWithHolesComponent.component.value.shapeRepresentation.shape))
        return new Set(heights)
    }

    const wallNeighborHeightsLeft = computed<ReadonlySet<number>>(() => {
        const snapPointId = wallIdToSnapPointId(props.wall.id, "LEFT")
        const floor = renderer.traversableBuilding.floorOf(props.wall)
        if (floor === null) {
            return emptySet()
        }
        return snapPointIdToHeights(snapPointId, floor.level)
    })

    const wallNeighborHeightsRight = computed<ReadonlySet<number>>(() => {
        const snapPointId = wallIdToSnapPointId(props.wall.id, "RIGHT")
        const floor = renderer.traversableBuilding.floorOf(props.wall)
        if (floor === null) {
            return emptySet()
        }
        return snapPointIdToHeights(snapPointId, floor.level)
    })

    // const neighborWallsLeft = computed<readonly Wall[]>(() => renderer.neighborWallsOf(props.wall, "LEFT", true))
    // const neighborWallsRight = computed<readonly Wall[]>(() => renderer.neighborWallsOf(props.wall, "RIGHT", true))
    // const neighborWallsLeftInteriorValid = computed<readonly Wall[]>(() => calculateSizeAdjustmentXNeighborInteriorWallsOf(renderer, props.wall, "LEFT"))
    // const neighborWallsLeftExteriorMinusValid = computed<readonly Wall[]>(() => calculateSizeAdjustmentXNeighborExteriorMinusWallsOf(renderer, props.wall, "LEFT"))
    // const neighborWallsLeftExteriorPlusValid = computed<readonly Wall[]>(() => calculateSizeAdjustmentXNeighborExteriorPlusWallsOf(renderer, props.wall, "LEFT"))
    // const neighborWallsRightInteriorValid = computed<readonly Wall[]>(() => calculateSizeAdjustmentXNeighborInteriorWallsOf(renderer, props.wall, "RIGHT"))
    // const neighborWallsRightExteriorMinusValid = computed<readonly Wall[]>(() => calculateSizeAdjustmentXNeighborExteriorMinusWallsOf(renderer, props.wall, "RIGHT"))
    // const neighborWallsRightExteriorPlusValid = computed<readonly Wall[]>(() => calculateSizeAdjustmentXNeighborExteriorPlusWallsOf(renderer, props.wall, "RIGHT"))
    // const orthogonalInteriorWalls = computed<readonly Wall[]>(() => calculateOrthogonalInteriorWallsOf(renderer, props.wall))
    // const possibleSizeAdjustmentXRelatedWalls = computed<readonly Wall[]>(() => calculatePossibleSizeAdjustmentXRelatedWalls(renderer, props.wall))
    // noinspection LocalVariableNamingConventionJS
    // const remainingPossibleSizeAdjustmentXRelatedWalls = computed<readonly Wall[]>(() => calculateRemainingPossibleSizeAdjustmentXRelatedWalls(renderer, props.wall)
    //     .sort((a, b) => (stringToInt(a.displayId ?? "") ?? 0) - (stringToInt(b.displayId ?? "") ?? 0))
    // )
    // const sizeAdjustmentUserXRelatedWalls = computed<readonly Wall[]>(() => calculateSizeAdjustmentXRelatedWalls(renderer, props.wall))

    const sizeAdjustmentXResult = computed<Optional<WallSizeAdjustmentXResult>>(() => calculateSizeAdjustmentXFor(renderer, props.wall))
    const sizeAdjustmentYResult = computed<Optional<WallSizeAdjustmentYResult>>(() => calculateSizeAdjustmentYFor(renderer, props.wall))

    function onResetWallEditorVertices() {
        const editor = wallEditor.value
        if (editor === undefined) {
            console.warn("Wall editor is not defined")
            return
        }
        editor.resetVertices()
    }

    async function onSaveWallEditorVertices() {
        const editor = wallEditor.value
        if (editor === undefined) {
            console.warn("Wall editor is not defined")
            return
        }

        changeVerticesOfPolygonWall(props.wall, editor.getVertices())

        isShapeDialogVisible.value = false

        updateInputs()

        await debouncedSaveBuilding()
    }

    const isIntermediate = shallowRef<boolean>(false)

    const wallEvebiWidthCorrectionCm = shallowRef<Optional<number>>(0)
    const wallEvebiWidthCorrectionM = createCmToMField(wallEvebiWidthCorrectionCm)

    const wallEvebiHeightCorrectionCm = shallowRef<Optional<number>>(0)
    const wallEvebiHeightCorrectionM = createCmToMField(wallEvebiHeightCorrectionCm)

    const wallEvebiThicknessCm = shallowRef<Optional<number>>(0)
    const wallEvebiThicknessM = createCmToMField(wallEvebiThicknessCm)

    const wallSizeAdjustmentZCm = shallowRef<number>(0)
    const wallSizeAdjustmentZM = createCmToMField(wallSizeAdjustmentZCm)

    const wallWidthCm = shallowRef<Optional<number>>(0)
    const wallWidthM = createCmToMField(wallWidthCm)

    const wallHeightCm = shallowRef<Optional<number>>(0)
    const wallHeightM = createCmToMField(wallHeightCm)

    const wallThicknessCm = shallowRef<Optional<number>>(0)
    const wallThicknessM = createCmToMField(wallThicknessCm)

    watchEffect(() => {
        wallEvebiThicknessM.value = (wallThicknessM.value ?? 0) + (wallSizeAdjustmentZM.value ?? 0)
    })
    watchEffect(() => {
        wallSizeAdjustmentZM.value = (wallEvebiThicknessM.value ?? 0) - (wallThicknessM.value ?? 0)
    })

    const angleRange = ref<number[]>([0, 360])

    const innerRadiusCm = shallowRef<Optional<number>>(0)
    const innerRadiusM = createCmToMField(innerRadiusCm)

    const polygonVertices = shallowRef<readonly Vector2[]>([])
    const polygonSize = computed<Vector2>(() => tSizeOfVectors2(polygonVertices.value))
    const polygonVerticesAspectRatio = computed<number>(() => {
        const size = polygonSize.value
        return size.x / size.y
    })

    const {t, n} = useI18n()
    const selectedShapeType = shallowRef<Optional<ShapeType>>(props.wall.shapeRepresentation.shape.__typename ?? null)

    const itemsForWallOverwriteMenuConfig = useItemsForWallOverwriteMenuConfig(renderer, toRef(() => props.wall), t)

    const isEvebiThicknessOverwriteMenuVisible = shallowRef<boolean>(false)
    const evebiThicknessOverwriteMenuConfig: OverwriteMenuConfig<Wall> = {
        buttonText: t('listing.building.cad.selection.wall.overwriteMenu.variant.evebi.thickness.button'),
        description: t('listing.building.cad.selection.wall.overwriteMenu.variant.evebi.thickness.description'),
        processor: async (walls) => {
            if (walls.length <= 0) {
                return
            }
            const newEvebiThickness = wallEvebiThicknessM.value ?? 0;
            for (const wall of walls) {
                const thickness = thicknessOfShape(wall.shapeRepresentation.shape)
                wall.sizeAdjustmentZ = newEvebiThickness - thickness
            }

            await BuildingPipelineBuilder
                .create("ChangeEvebiThicknessOfMultipleWalls", renderer)
                .recalculateAllWallSizeAdjustments()
                .save()
                .build()
                .execute()
        },
        displayValue: computed<string>(() => n(wallEvebiThicknessCm.value ?? 0, 'integer')),
        unit: "CENTIMETER",
        source: toRef(() => props.wall),
        items: computed<readonly (readonly OverwriteMenuConfigItem<Wall>[])[]>(() => {
            const floor = renderer.traversableBuilding.floorOf(props.wall)
            const walls = renderer.building.value.floors.flatMap(f => f.walls)
            const wallsOnFloor = floor?.walls ?? []
            const wallTypes = WallTypeValues.filter(t => t !== "INTERIOR")

            return [
                [
                    ...generateWallsOfTypeItems(wallsOnFloor, wallType => t(`listing.building.cad.selection.wall.overwriteMenu.menuItems.allOfTypeOnFloor.${wallType}`), wallTypes)
                ],
                [
                    ...generateWallsOfTypeItems(walls, wallType => t(`listing.building.cad.selection.wall.overwriteMenu.menuItems.allOfType.${wallType}`), wallTypes),
                ]
            ]
        })
    }

    const isThicknessOverwriteMenuVisible = shallowRef<boolean>(false)
    const thicknessOverwriteMenuConfig: OverwriteMenuConfig<Wall> = {
        buttonText: t('listing.building.cad.selection.wall.overwriteMenu.variant.thickness.button'),
        description: t('listing.building.cad.selection.wall.overwriteMenu.variant.thickness.description'),
        processor: async (walls) => {
            if (walls.length <= 0) {
                return
            }
            for (const wall of walls) {
                changeWallThickness(renderer, wall, wallThicknessM.value ?? 0)
            }

            ++renderer.buildingShapeComputationCounter.value

            await BuildingPipelineBuilder
                .create("ChangeThicknessOfMultipleWalls", renderer)
                .splitWallsOnFloorsOfBuildingComponents(...walls)
                .mergeWallsOnFloorsOfBuildingComponents(walls)
                .removeZeroWallsOnFloorsOfBuildingComponents(...walls)
                .removeDuplicateWallsOnFloorsOfBuildingComponents(...walls)
                .renewRoomsOnFloorsOfBuildingComponents(walls)
                .flipExteriorWallsOnFloorsOfBuildingComponents(...walls)
                .renewFloorsOfBuildingComponents(...walls)
                .renewBuilding(false)
                .recalculateAllWallSizeAdjustments()
                .renewDisplayIdsAndRoomNumbers()
                .removeInvalidRelations()
                .save()
                .build()
                .execute()
        },
        displayValue: computed<string>(() => n(wallThicknessCm.value ?? 0, 'integer')),
        unit: "CENTIMETER",
        source: toRef(() => props.wall),
        items: itemsForWallOverwriteMenuConfig
    }

    const isHeightOverwriteMenuVisible = shallowRef<boolean>(false)
    const heightOverwriteMenuConfig: OverwriteMenuConfig<Wall> = {
        buttonText: t('listing.building.cad.selection.wall.overwriteMenu.variant.height.button'),
        description: t('listing.building.cad.selection.wall.overwriteMenu.variant.height.description'),
        processor: async (walls) => {
            if (walls.length <= 0) {
                return
            }
            for (const wall of walls) {
                changeWallHeight(wall, wallHeightM.value ?? 0)
            }

            ++renderer.buildingShapeComputationCounter.value

            await BuildingPipelineBuilder
                .create("ChangeHeightOfMultipleWalls", renderer)
                .splitWallsOnFloorsOfBuildingComponents(...walls)
                .mergeWallsOnFloorsOfBuildingComponents(walls)
                .removeZeroWallsOnFloorsOfBuildingComponents(...walls)
                .removeDuplicateWallsOnFloorsOfBuildingComponents(...walls)
                .renewRoomsOnFloorsOfBuildingComponents(walls)
                .flipExteriorWallsOnFloorsOfBuildingComponents(...walls)
                .renewFloorsOfBuildingComponents(...walls)
                .renewBuilding(false)
                .recalculateAllWallSizeAdjustments()
                .renewDisplayIdsAndRoomNumbers()
                .removeInvalidRelations()
                .save()
                .build()
                .execute()
        },
        displayValue: computed<string>(() => n(wallHeightCm.value ?? 0, 'integer')),
        unit: "CENTIMETER",
        source: toRef(() => props.wall),
        items: itemsForWallOverwriteMenuConfig
    }

    watch(() => props.wall.shapeRepresentation.shape.__typename, shapeType => {
        selectedShapeType.value = shapeType ?? null
    })

    // noinspection OverlyComplexFunctionJS,FunctionTooLongJS
    function updateInputs() {
        const wall = props.wall
        const shapeRepresentation = wall.shapeRepresentation

        isIntermediate.value = wall.isIntermediate === true

        if (shapeRepresentation.shape.__typename === "Box") {
            if (!areNumbersEqual(wallWidthM.value ?? Number.MIN_VALUE, shapeRepresentation.shape.width, BUILDING_EPSILON)) {
                wallWidthM.value = shapeRepresentation.shape.width
            }
            if (!areNumbersEqual(wallHeightM.value ?? Number.MIN_VALUE, shapeRepresentation.shape.height, BUILDING_EPSILON)) {
                wallHeightM.value = shapeRepresentation.shape.height
            }
            if (!areNumbersEqual(wallThicknessM.value ?? Number.MIN_VALUE, shapeRepresentation.shape.depth, BUILDING_EPSILON)) {
                wallThicknessM.value = shapeRepresentation.shape.depth
            }
        } else if (shapeRepresentation.shape.__typename === "Polygon") {
            const size = tSizeOfVectors2(shapeRepresentation.shape.vertices.map(v => new Vector2(v.x, v.y)))

            if (!areNumbersEqual(wallWidthM.value ?? Number.MIN_VALUE, size.x, BUILDING_EPSILON)) {
                wallWidthM.value = size.x
            }
            if (!areNumbersEqual(wallHeightM.value ?? Number.MIN_VALUE, size.y, BUILDING_EPSILON)) {
                wallHeightM.value = size.y
            }
            if (!areNumbersEqual(wallThicknessM.value ?? Number.MIN_VALUE, shapeRepresentation.shape.extrusion, BUILDING_EPSILON)) {
                wallThicknessM.value = shapeRepresentation.shape.extrusion
            }
            const newPolygonVertices = shapeRepresentation.shape.vertices.map(v => new Vector2(v.x, v.y))
            if (!areArraysEqual(polygonVertices.value, newPolygonVertices, (a, b) => tAreVectors2Equal(a, b, BUILDING_EPSILON))) {
                polygonVertices.value = newPolygonVertices
            }
        } else if (shapeRepresentation.shape.__typename === "Ring") {
            if (!areNumbersEqual(innerRadiusM.value ?? Number.MIN_VALUE, shapeRepresentation.shape.innerRadius, BUILDING_EPSILON)) {
                innerRadiusM.value = shapeRepresentation.shape.innerRadius
            }
            if (!areNumbersEqual(wallThicknessM.value ?? Number.MIN_VALUE, shapeRepresentation.shape.size, BUILDING_EPSILON)) {
                wallThicknessM.value = shapeRepresentation.shape.size
            }
            if (!areNumbersEqual(wallHeightM.value ?? Number.MIN_VALUE, shapeRepresentation.shape.extrusion, BUILDING_EPSILON)) {
                wallHeightM.value = shapeRepresentation.shape.extrusion
            }
            const newAngleRange = [
                shapeRepresentation.shape.startAngle,
                shapeRepresentation.shape.endAngle
            ];
            if (!areArraysEqual(angleRange.value, newAngleRange, (a, b) => areNumbersEqual(a, b, BUILDING_EPSILON))) {
                angleRange.value = newAngleRange
            }
        }

        if (!areNumbersEqual(wallSizeAdjustmentZM.value ?? Number.MIN_VALUE, wall.sizeAdjustmentZ, BUILDING_EPSILON)) {
            wallSizeAdjustmentZM.value = wall.sizeAdjustmentZ
        }
        if (!areNumbersEqual(wallEvebiWidthCorrectionM.value ?? Number.MIN_VALUE, wall.sizeAdjustmentUserX, BUILDING_EPSILON)) {
            wallEvebiWidthCorrectionM.value = wall.sizeAdjustmentUserX
        }
        if (!areNumbersEqual(wallEvebiHeightCorrectionM.value ?? Number.MIN_VALUE, wall.sizeAdjustmentUserY, BUILDING_EPSILON)) {
            wallEvebiHeightCorrectionM.value = wall.sizeAdjustmentUserY
        }
    }

    watch(() => props.wall, () => {
        updateInputs()
    }, {
        immediate: true
    })

    function onEditShape() {
        isShapeDialogVisible.value = true
    }

    function onCloseShapeDialog() {
        isShapeDialogVisible.value = false
    }

    const debouncedSaveBuilding = debounce(saveBuilding, debounceDelay)

    async function saveBuilding() {
        ++renderer.buildingShapeComputationCounter.value

        await BuildingPipelineBuilder
            .create("SaveSelectedWall", renderer)
            .splitWallsOnFloorsOfBuildingComponents(props.wall)
            .mergeWallsOnFloorsOfBuildingComponents([props.wall])
            .removeZeroWallsOnFloorsOfBuildingComponents(props.wall)
            .removeDuplicateWallsOnFloorsOfBuildingComponents(props.wall)
            .renewRoomsOnFloorsOfBuildingComponents([props.wall])
            .flipExteriorWallsOnFloorsOfBuildingComponents(props.wall)
            .renewFloorsOfBuildingComponents(props.wall)
            .renewBuilding(false)
            .recalculateAllWallSizeAdjustments()
            .renewDisplayIdsAndRoomNumbers()
            .removeInvalidRelations()
            .save()
            .build()
            .execute()
    }

    watch(selectedShapeType, async (newShapeType, oldShapeType) => {
        if (newShapeType === null) {
            return
        }
        if (oldShapeType === newShapeType) {
            return
        }

        changeWallShape(props.wall, newShapeType)

        updateInputs()

        await debouncedSaveBuilding()

        if (newShapeType === "Polygon") {
            isShapeDialogVisible.value = true
        }
    })

    //############################################
    //### SIZE ADJUSTMENT X USER RELATED WALLS ###
    //############################################
    // const sizeAdjustmentUserXRelatedWall = shallowRef<Optional<Wall>>(null)
    // watch(sizeAdjustmentUserXRelatedWall, relatedWall => {
    //     if (relatedWall === null) {
    //         return
    //     }
    //     sizeAdjustmentUserXRelatedWall.value = null
    //
    //     const wall = props.wall
    //     wall.sizeAdjustmentUserXRelatedWallIds = [...wall.sizeAdjustmentUserXRelatedWallIds, relatedWall.id]
    //     saveBuilding()
    // })

    // function onRemoveSizeAdjustmentUserXRelatedWall(relatedWall: Wall) {
    //     const wall = props.wall
    //     wall.sizeAdjustmentUserXRelatedWallIds = wall.sizeAdjustmentUserXRelatedWallIds.filter(id => id !== relatedWall.id)
    //     saveBuilding()
    // }

    //###############################
    //### WIDTH ADJUSTMENT X USER ###
    //###############################
    function updateWidthAdjustmentXUser(value: Optional<number>) {
        const wall = props.wall
        wall.sizeAdjustmentUserX = value ?? 0
        debouncedSaveBuilding()
    }

    const debouncedUpdateWidthAdjustmentXUser = debounce(updateWidthAdjustmentXUser, debounceDelay)
    watch(wallEvebiWidthCorrectionM, debouncedUpdateWidthAdjustmentXUser)

    //################################
    //### HEIGHT ADJUSTMENT Y USER ###
    //################################
    function updateHeightAdjustmentYUser(value: Optional<number>) {
        const wall = props.wall
        wall.sizeAdjustmentUserY = value ?? 0
        debouncedSaveBuilding()
    }

    const debouncedUpdateHeightAdjustmentYUser = debounce(updateHeightAdjustmentYUser, debounceDelay)
    watch(wallEvebiHeightCorrectionM, debouncedUpdateHeightAdjustmentYUser)

    //#########################
    //### SIZE ADJUSTMENT Z ###
    //#########################
    function updateSizeAdjustmentZ(value: Optional<number>) {
        const wall = props.wall
        wall.sizeAdjustmentZ = value ?? 0
        debouncedSaveBuilding()
    }

    const debouncedUpdateSizeAdjustmentZ = debounce(updateSizeAdjustmentZ, debounceDelay)
    watch(wallSizeAdjustmentZM, debouncedUpdateSizeAdjustmentZ)

    //#######################
    //### IS INTERMEDIATE ###
    //#######################

    function updateIsIntermediate(isIntermediate: boolean) {
        const wall = props.wall
        wall.isIntermediate = isIntermediate
        debouncedSaveBuilding()
    }

    const debouncedUpdateIsIntermediate = debounce(updateIsIntermediate, debounceDelay)
    watch(isIntermediate, debouncedUpdateIsIntermediate)

    //#############
    //### ANGLE ###
    //#############
    function updateAngles(angleRangesInDegree: number[]) {
        changeRingWallAngles(props.wall, angleRangesInDegree[0], angleRangesInDegree[1])
        debouncedSaveBuilding()
    }

    const debouncedUpdateAngles = debounce(updateAngles, debounceDelay)
    watch(angleRange, debouncedUpdateAngles, {
        deep: true
    })

    //#############
    //### WIDTH ###
    //#############
    function updateWidth(width: Optional<number>) {
        const wall = props.wall

        changeWallWidth(renderer, wall, width ?? 0)

        if (wall.shapeRepresentation.shape.__typename === "Polygon") {
            updateInputs()
        }

        debouncedSaveBuilding()
    }

    const debouncedUpdateWidth = debounce(updateWidth, debounceDelay)
    watch(wallWidthM, debouncedUpdateWidth)

    //##############
    //### HEIGHT ###
    //##############
    function updateHeight(height: Optional<number>) {
        const wall = props.wall

        changeWallHeight(wall, height ?? 0)

        if (wall.shapeRepresentation.shape.__typename === "Polygon") {
            updateInputs()
        }

        debouncedSaveBuilding()
    }

    const debouncedUpdateHeight = debounce(updateHeight, debounceDelay)
    watch(wallHeightM, debouncedUpdateHeight)

    //#################
    //### THICKNESS ###
    //#################
    function updateThickness(thickness: Optional<number>) {
        changeWallThickness(renderer, props.wall, thickness ?? 0)
        debouncedSaveBuilding()
    }

    const debouncedUpdateThickness = debounce(updateThickness, debounceDelay)
    watch(wallThicknessM, debouncedUpdateThickness)

    //####################
    //### INNER RADIUS ###
    //####################
    function updateInnerRadius(innerRadius: Optional<number>) {
        changeRingWallInnerRadius(props.wall, innerRadius ?? 0)
        debouncedSaveBuilding()
    }

    const debouncedUpdateInnerRadius = debounce(updateInnerRadius, debounceDelay)
    watch(innerRadiusM, debouncedUpdateInnerRadius)
</script>

<style scoped>
    .subtitleArea {
        color: rgb(var(--v-theme-d-text-default)) !important;
    }

    .infoText {
        color: rgb(var(--v-theme-d-text-tertiary)) !important;
    }

    .dialogActions {
        padding-bottom: calc(16px + env(safe-area-inset-bottom)) !important;
    }

    .highlighted {
        color: rgb(var(--v-theme-d-text-info)) !important;
        font-weight: bold;
    }

    .v-table tr {
        color: rgb(var(--v-theme-d-text-default));
    }

    .v-table.onBackground tbody tr:nth-child(even) {
        background-color: rgb(var(--v-theme-d-background-default));
    }

    .v-table.onBackground tbody tr:nth-child(odd) {
        background-color: rgb(var(--v-theme-d-background-surface));
    }

    .v-table.onBackground thead tr,
    .v-table.onBackground tfoot tr,
    .v-table.onBackground tbody tr.special {
        background-color: rgb(var(--v-theme-d-background-highlight));
    }

    .v-table.onSurface tbody tr:nth-child(even) {
        background-color: rgb(var(--v-theme-d-background-surface));
    }

    .v-table.onSurface tbody tr:nth-child(odd) {
        background-color: rgb(var(--v-theme-d-background-default));
    }

    .v-table.onSurface thead tr,
    .v-table.onSurface tfoot tr,
    .v-table.onSurface tbody tr.special {
        background-color: rgb(var(--v-theme-d-background-highlight));
    }

    .v-table tbody tr.special th {
        color: rgb(var(--v-theme-d-icon-default));
    }

    .v-table tbody tr td,
    .v-table tbody tr th {
        border-bottom: 0 !important;
    }

    .v-table tr td:not(:first-child),
    .v-table tr th:not(:first-child) {
        padding-left: 2px;
    }

    .v-table tr td:not(:last-child),
    .v-table tr th:not(:last-child) {
        padding-right: 2px;
    }

    .sum {
        text-decoration: underline;
    }

    :deep(.text-h3) {
        font-size: 1.2rem !important;
        margin-top: 4px !important;
        margin-bottom: 4px !important;
    }

    .displayId {
        color: black;
        padding: 2px;
        display: inline-block;
    }

    .subinfo {
        color: rgb(var(--v-theme-d-icon-default))
    }

    .v-table :deep(input) {
        padding-inline-end: 0.3rem;
    }

    .v-table :deep(input),
    .v-table :deep(.v-field .unit) {
        font-size: 0.875rem !important;
    }

    .v-table :deep(th input),
    .v-table :deep(th .v-field .unit) {
        font-weight: bold;
    }
</style>