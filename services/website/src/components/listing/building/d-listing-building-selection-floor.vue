<template>
    <v-container class="pb-0 mb-0"
                 fluid>
        <v-row dense
               justify="end">
            <v-col cols="9">
                <d-text-field v-model="rotationAngle"
                              :label="t('listing.building.cad.selection.floor.rotation')"
                              :prepend-inner-icon="smAndDown ? undefined : mdiAxisZRotateCounterclockwise"
                              :value-max="360"
                              :value-min="0"
                              center-input-text
                              small-prepend-inner-icon
                              type="number"
                              @focusin="onFocus(true)"
                              @focusout="onFocus(false)">
                    <template #appendInner>
                        <d-input-unit unit="DEGREE_OF_ARC"/>
                    </template>
                </d-text-field>
            </v-col>
        </v-row>

        <v-row dense
               justify="end">
            <v-col class="pe-0"
                   cols="auto">
                <d-btn :disabled="renderer.isLoading.value"
                       :prepend-icon="mdiRotateRight"
                       density="compact"
                       type="default"
                       variant="text"
                       @click="onAutoRotate">
                    {{ t('listing.building.cad.selection.floor.autoRotateButton') }}
                </d-btn>
            </v-col>
            <v-col class="px-0"
                   cols="auto">
                <d-btn :disabled="renderer.isLoading.value"
                       :prepend-icon="mdiRotateRight"
                       class="pe-0"
                       density="compact"
                       type="default"
                       variant="text"
                       @click="onRotate90">
                    90
                    <d-input-unit unit="DEGREE_OF_ARC"/>
                </d-btn>
            </v-col>
            <v-col class="px-0"
                   cols="auto">
                <d-btn :disabled="renderer.isLoading.value"
                       :prepend-icon="mdiRotateRight"
                       class="pe-0"
                       density="compact"
                       type="default"
                       variant="text"
                       @click="onRotate180">
                    180
                    <d-input-unit unit="DEGREE_OF_ARC"/>
                </d-btn>
            </v-col>
        </v-row>

        <v-row>
            <v-slide-x-transition>
                <v-col v-if="ceilingSlabThicknessCm !== null"
                       cols="6">
                    <d-text-field v-model="ceilingSlabThicknessCm"
                                  :label="t('listing.building.cad.selection.floor.ceilingSlabHeight')"
                                  :prepend-inner-icon="smAndDown ? undefined : mdiDockTop"
                                  :value-max="Math.round(BUILDING_FLOOR_SLAB_THICKNESS_MAX * 100)"
                                  :value-min="Math.round(BUILDING_FLOOR_SLAB_THICKNESS_MIN * 100)"
                                  center-input-text
                                  no-append-padding
                                  small-prepend-inner-icon
                                  type="number">
                        <template #appendInner>
                            <d-input-unit unit="CENTIMETER"/>
                        </template>
                    </d-text-field>
                </v-col>
            </v-slide-x-transition>

            <v-col :offset="ceilingSlabThicknessCm === null ? 6 : undefined"
                   cols="6">
                <d-text-field v-model="floorSlabThicknessCm"
                              :label="t('listing.building.cad.selection.floor.floorSlabHeight')"
                              :prepend-inner-icon="smAndDown ? undefined : mdiDockBottom"
                              :value-max="Math.round(BUILDING_FLOOR_SLAB_THICKNESS_MAX * 100)"
                              :value-min="Math.round(BUILDING_FLOOR_SLAB_THICKNESS_MIN * 100)"
                              center-input-text
                              no-append-padding
                              small-prepend-inner-icon
                              type="number">
                    <template #appendInner>
                        <d-input-unit unit="CENTIMETER"/>
                    </template>
                </d-text-field>
            </v-col>
        </v-row>

        <v-row>
            <v-col>
                <d-text-field v-model="floorXCm"
                              :prepend-inner-icon="smAndDown ? undefined : mdiAxisXArrow"
                              center-input-text
                              label="X"
                              no-append-padding
                              small-prepend-inner-icon
                              type="number">
                    <template #appendInner>
                        <d-input-unit unit="CENTIMETER"/>
                    </template>
                </d-text-field>
            </v-col>

            <v-col>
                <d-text-field v-model="floorZCm"
                              :prepend-inner-icon="smAndDown ? undefined : mdiAxisYArrow"
                              center-input-text
                              label="Z"
                              no-append-padding
                              small-prepend-inner-icon
                              type="number">
                    <template #appendInner>
                        <d-input-unit unit="CENTIMETER"/>
                    </template>
                </d-text-field>
            </v-col>
        </v-row>
    </v-container>
</template>

<script lang="ts"
        setup>
    import {computed, inject, shallowRef, watch} from "vue";
    import {BUILDING_EPSILON, BUILDING_FLOOR_SLAB_THICKNESS_DEFAULT, BUILDING_FLOOR_SLAB_THICKNESS_MAX, BUILDING_FLOOR_SLAB_THICKNESS_MIN, changeFloorSlabCeilingThickness, changeFloorSlabFloorThickness, createCmToMField, DBuildingRendererInjection, heightOfShape, transformationOfShapeRepresentation} from "@/components/listing/building/building";
    import {Floor} from "@/adapter/graphql/generated/graphql";
    import DInputUnit from "@/adapter/vuetify/theme/components/input/d-input-unit.vue";
    import debounce from "debounce";
    import {Euler, MathUtils, Matrix4} from "three";
    import {BuildingPipelineBuilder} from "@/components/listing/building/pipeline/BuildingPipelineBuilder";
    import {matrix4ToTransformationMatrixArray} from "@/components/listing/building-creation/pipeline/building-creation-pipeline";
    import DTextField from "@/adapter/vuetify/theme/components/input/d-text-field.vue";
    import {useDisplay} from "vuetify";
    import {areNumbersEqual} from "@/utility/number";
    import {tDecomposeMatrix, tDeskewAngle2D} from "@/adapter/three/three-utility";
    import {mdiAxisXArrow, mdiAxisYArrow, mdiAxisZRotateCounterclockwise, mdiDockBottom, mdiDockTop, mdiRotateRight} from "@mdi/js";
    import {useI18n} from "vue-i18n";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import {wallOrOpeningToLineSegments} from "@/components/listing/building/wall-and-opening-lines";
    import {Optional} from "@/model/Optional";

    const props = defineProps<{
        floor: Floor
    }>()

    const renderer = inject(DBuildingRendererInjection)!

    const {t} = useI18n()
    const {smAndDown} = useDisplay()

    const debounceDelay = 1_500
    const debouncedSaveBuilding = debounce(saveBuilding, debounceDelay)

    const rotationAngle = shallowRef<Optional<number>>(0)

    function updateRotationAngle(rotationAngleInDegree: Optional<number>) {
        onRotate(rotationAngleInDegree ?? 0, true)
    }

    async function saveBuilding() {
        ++renderer.buildingShapeComputationCounter.value

        const floor = props.floor
        const floors = renderer.building.value.floors
        const floorIndex = floors.findIndex(f => f.id === floor.id)
        const bottomFloor = floorIndex <= 0 ? null : floors[floorIndex - 1]
        const topFloor = floorIndex >= floors.length - 1 ? null : floors[floorIndex + 1]

        const floorsToUpdate = [floor]
        if (bottomFloor !== null) {
            floorsToUpdate.push(bottomFloor)
        }
        if (topFloor !== null) {
            floorsToUpdate.push(topFloor)
        }

        await BuildingPipelineBuilder
            .create("SaveSelectedFloor", renderer)
            .renewFloors(...floorsToUpdate)
            .renewBuilding(false)
            .recalculateAllWallSizeAdjustments()
            .renewDisplayIdsAndRoomNumbers()
            .save()
            .build()
            .execute()
    }

    const floorSlabThicknessCm = shallowRef<Optional<number>>(0)
    const floorSlabThicknessM = createCmToMField(floorSlabThicknessCm)

    const floorXCm = shallowRef<Optional<number>>(0)
    const floorXM = createCmToMField(floorXCm)

    const floorZCm = shallowRef<Optional<number>>(0)
    const floorZM = createCmToMField(floorZCm)

    const ceilingSlabThicknessCm = shallowRef<Optional<number>>(null)
    const ceilingSlabThicknessM = computed<Optional<number>>({
        get: () => ceilingSlabThicknessCm.value === null
            ? null
            : ceilingSlabThicknessCm.value / 100,
        set: valueM => {
            if (valueM === null) {
                return
            }
            ceilingSlabThicknessCm.value = Math.round(valueM * 100)
        }
    })

    function updateInputs() {
        const floor = props.floor
        const shapeRepresentation = floor.shapeRepresentation

        const transformation = transformationOfShapeRepresentation(shapeRepresentation)
        const [translation, rotation] = tDecomposeMatrix(transformation)
        const euler = new Euler().setFromQuaternion(rotation, 'YXZ')
        let rotationY = MathUtils.radToDeg(euler.y)
        if (rotationY < 0) {
            rotationY += 360
        } else if (rotationY >= 360) {
            rotationY -= 360
        }

        if (!areNumbersEqual(rotationAngle.value ?? Number.MIN_VALUE, rotationY, BUILDING_EPSILON)) {
            rotationAngle.value = Math.round(rotationY * 100) / 100 //round to 2 decimal places
        }

        const floorSlabThickness = heightOfShape(floor.floorSlab.shapeRepresentation.shape)
        if (!areNumbersEqual((floorSlabThicknessM.value ?? Number.MIN_VALUE), floorSlabThickness, BUILDING_EPSILON)) {
            floorSlabThicknessM.value = floorSlabThickness
        }

        if (floor.ceilingSlab === undefined || floor.ceilingSlab === null) {
            ceilingSlabThicknessM.value = null
        } else {
            const ceilingSlabThickness = heightOfShape(floor.ceilingSlab.shapeRepresentation.shape)
            if (ceilingSlabThicknessM.value === null || !areNumbersEqual(ceilingSlabThicknessM.value, ceilingSlabThickness, BUILDING_EPSILON)) {
                ceilingSlabThicknessM.value = ceilingSlabThickness
            }
        }

        if (!areNumbersEqual(translation.x, floorXM.value ?? Number.MIN_VALUE, BUILDING_EPSILON)) {
            floorXM.value = translation.x
        }
        if (!areNumbersEqual(translation.z, floorZM.value ?? Number.MIN_VALUE, BUILDING_EPSILON)) {
            floorZM.value = translation.z
        }
    }

    watch(() => props.floor, () => {
        updateInputs()
    }, {
        immediate: true
    })

    //dieser block muss nach dem Watcher für updateInputs kommen
    const debouncedUpdateRotationAngle = debounce(updateRotationAngle, debounceDelay)
    watch(rotationAngle, debouncedUpdateRotationAngle)

    function onFocus(isFocused: boolean) {
        renderer.showFloorGrid.value = isFocused
    }

    function onRotate90() {
        onRotate((rotationAngle.value ?? 0) + 90, true)
    }

    function onRotate180() {
        onRotate((rotationAngle.value ?? 0) + 180, true)
    }

    function onAutoRotate() {
        onRotate(0, false)

        const lineSegments = props.floor.walls.flatMap(wall => wallOrOpeningToLineSegments(renderer, wall, wall, true))
        const deskewAngleInRadians = tDeskewAngle2D(lineSegments) ?? 0

        if (deskewAngleInRadians !== 0) {
            const degrees = Math.round(MathUtils.radToDeg(deskewAngleInRadians) * 100) / 100 //round to 2 decimal places
            onRotate(degrees, true)
        }
    }

    //################
    //### ROTATION ###
    //################

    async function onRotate(degrees: number, saveBuilding: boolean) {
        const floor = props.floor

        const transformation = transformationOfShapeRepresentation(floor.shapeRepresentation)

        const [translation, rotation, scale] = tDecomposeMatrix(transformation)
        const euler = new Euler().setFromQuaternion(rotation, 'YXZ')
        euler.y = MathUtils.degToRad(degrees)
        rotation.setFromEuler(euler)

        const newTransformation = new Matrix4().compose(translation, rotation, scale)

        floor.shapeRepresentation.transformationMatrix = matrix4ToTransformationMatrixArray(newTransformation)

        if (saveBuilding) {
            await debouncedSaveBuilding()
        }
    }

    //#########################
    //### FLOOR SLAB HEIGHT ###
    //#########################
    async function updateFloorSlabHeight(floorSlabHeight: Optional<number>) {
        changeFloorSlabFloorThickness(renderer, props.floor, floorSlabHeight ?? BUILDING_FLOOR_SLAB_THICKNESS_DEFAULT)
        await debouncedSaveBuilding()
    }

    const debouncedUpdateFloorSlabHeight = debounce(updateFloorSlabHeight, debounceDelay)
    watch(floorSlabThicknessM, debouncedUpdateFloorSlabHeight)

    //###########################
    //### CEILING SLAB HEIGHT ###
    //###########################
    async function updateCeilingSlabHeight(ceilingSlabHeight: Optional<number>) {
        changeFloorSlabCeilingThickness(renderer, props.floor, ceilingSlabHeight ?? BUILDING_FLOOR_SLAB_THICKNESS_DEFAULT)
        await debouncedSaveBuilding()
    }

    const debouncedUpdateCeilingSlabHeight = debounce(updateCeilingSlabHeight, debounceDelay)
    watch(ceilingSlabThicknessM, debouncedUpdateCeilingSlabHeight)

    //###############
    //### FLOOR X ###
    //###############
    async function updateFloorX(floorX: Optional<number>) {
        const floor = props.floor
        const shapeRepresentation = floor.shapeRepresentation
        const transformation = transformationOfShapeRepresentation(shapeRepresentation)
        const [translation, rotation, scale] = tDecomposeMatrix(transformation)
        translation.x = floorX ?? 0
        const newTransformation = new Matrix4().compose(translation, rotation, scale)
        shapeRepresentation.transformationMatrix = matrix4ToTransformationMatrixArray(newTransformation)

        await debouncedSaveBuilding()
    }

    const debouncedUpdateFloorX = debounce(updateFloorX, debounceDelay)
    watch(floorXM, debouncedUpdateFloorX)

    //###############
    //### FLOOR Z ###
    //###############
    async function updateFloorZ(floorZ: Optional<number>) {
        const floor = props.floor
        const shapeRepresentation = floor.shapeRepresentation
        const transformation = transformationOfShapeRepresentation(shapeRepresentation)
        const [translation, rotation, scale] = tDecomposeMatrix(transformation)
        translation.z = floorZ ?? 0
        const newTransformation = new Matrix4().compose(translation, rotation, scale)
        shapeRepresentation.transformationMatrix = matrix4ToTransformationMatrixArray(newTransformation)

        await debouncedSaveBuilding()
    }

    const debouncedUpdateFloorZ = debounce(updateFloorZ, debounceDelay)
    watch(floorZM, debouncedUpdateFloorZ)
</script>

<style scoped>
</style>