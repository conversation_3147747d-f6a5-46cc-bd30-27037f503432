<template>
    <v-container class="pb-0 mb-0"
                 fluid>
        <v-row dense
               justify="end">
            <v-col cols="auto">
                <d-text-field v-model="rotationAngle"
                              :label="t('listing.building.cad.selection.building.rotation')"
                              :prepend-inner-icon="smAndDown ? undefined : mdiCompass"
                              :value-max="360"
                              :value-min="0"
                              center-input-text
                              small-prepend-inner-icon
                              type="number"
                              @focus="onFocus">
                    <template #appendInner>
                        <d-input-unit unit="DEGREE_OF_ARC"/>
                    </template>
                </d-text-field>
            </v-col>
        </v-row>
    </v-container>
</template>

<script lang="ts"
        setup>
    import {inject, shallowRef, watch} from "vue";
    import {BUILDING_EPSILON, DBuildingRendererInjection} from "@/components/listing/building/building";
    import DInputUnit from "@/adapter/vuetify/theme/components/input/d-input-unit.vue";
    import debounce from "debounce";
    import {BuildingPipelineBuilder} from "@/components/listing/building/pipeline/BuildingPipelineBuilder";
    import DTextField from "@/adapter/vuetify/theme/components/input/d-text-field.vue";
    import {useDisplay} from "vuetify";
    import {areNumbersEqual} from "@/utility/number";
    import {mdiCompass} from "@mdi/js";
    import {useI18n} from "vue-i18n";
    import {Building} from "@/adapter/graphql/generated/graphql";
    import {Optional} from "@/model/Optional";

    const renderer = inject(DBuildingRendererInjection)!

    const {t} = useI18n()
    const {smAndDown} = useDisplay()

    const debounceDelay = 1_500
    const debouncedSaveBuilding = debounce(saveBuilding, debounceDelay)

    const rotationAngle = shallowRef<Optional<number>>(0)

    function updateRotationAngle(rotationAngleInDegree: Optional<number>) {
        onRotate(rotationAngleInDegree ?? 0)
    }

    async function saveBuilding() {
        ++renderer.buildingShapeComputationCounter.value

        await BuildingPipelineBuilder
            .create("SaveSelectedBuilding", renderer)
            .renewBuilding(false)
            .renewDisplayIdsAndRoomNumbers()
            .save()
            .build()
            .execute()
    }

    function onFocus() {
        renderer.showCompass.value = true
    }

    function updateInputs(building: Building) {
        if (!areNumbersEqual(rotationAngle.value ?? Number.MIN_VALUE, building.rotationYCorrectionAngle, BUILDING_EPSILON)) {
            rotationAngle.value = Math.round(building.rotationYCorrectionAngle * 100) / 100 //round to 2 decimal places
        }
    }

    watch(renderer.building, updateInputs, {
        immediate: true
    })

    //dieser block muss nach dem Watcher für updateInputs kommen
    const debouncedUpdateRotationAngle = debounce(updateRotationAngle, debounceDelay)
    watch(rotationAngle, debouncedUpdateRotationAngle)

    //################
    //### ROTATION ###
    //################

    function onRotate(degrees: number) {
        const building = renderer.building.value
        building.rotationYCorrectionAngle = degrees

        debouncedSaveBuilding()
    }
</script>

<style scoped>
</style>