<template>
    <d-card class="mainArea"
            elevation="2"
            rounded="xl"
            variant="elevated">
        <v-slide-y-reverse-transition v-for="item in items"
                                      :key="item.id">
            <div v-if="item.isVisible.value">
                <d-tooltip v-if="item.type === 'BUTTON'"
                           :close-duration="tooltipTimeout"
                           :disabled="item.isActive.value && item.subMenu !== undefined"
                           :offset="item.isActive.value && (item.menu && item.menu.isEnabled.value && item.menu.isVisible.value) ? (smAndDown ? 43 : 52) : undefined"
                           location="end"
                           type="default">
                    <template #activator="{props: tooltipProps, isActive: isTooltipActive}">
                        <d-badge :disabled="!item.isEnabled.value"
                                 :invisible="item.hotkey === undefined"
                                 :location="badgeLocation"
                                 :model-value="isTooltipActive"
                                 :offset-y="badgetOffsetY"
                                 no-pointer
                                 type="warning">
                            <template #badge>
                                <kbd class="text-uppercase">{{ item.hotkey }}</kbd>
                            </template>

                            <d-badge v-if="item.nestedBadgeContent"
                                     :content="item.nestedBadgeContent"
                                     :disabled="!item.isEnabled.value"
                                     :offset-y="15"
                                     :type="item.isActive.value ? 'secondary' : 'info'"
                                     location="top"
                                     nested
                                     no-pointer>
                                <d-btn :class="{smallSizeButton: item.size === 'small' }"
                                       :disabled="!item.isEnabled.value"
                                       :icon="item.icon.value"
                                       :icon-color="item.iconColor?.value ?? undefined"
                                       :rounded="false"
                                       :size="type === 'MAIN' ? (smAndDown ? 'large' : 'x-large') : (smAndDown ? 'small' : 'large')"
                                       :type="item.isActive.value ? 'tertiary' : 'default'"
                                       block
                                       v-bind="tooltipProps"
                                       variant="text"
                                       @click="item.onClick">
                                    <template v-if="item.subIcon"
                                              #default>
                                        <div :class="{
                                                'x-large': type === 'MAIN' && !smAndDown,
                                                'large': type === 'MAIN' && smAndDown || type === 'SUB' && !smAndDown,
                                                'small': type === 'SUB' && smAndDown
                                            }"
                                             class="iconWrapper">
                                            <d-icon :icon="item.icon.value"
                                                    class="mainIcon"
                                                    type="error"/>
                                            <d-icon :icon="item.subIcon"
                                                    class="subIcon"
                                                    type="error"/>
                                        </div>
                                    </template>
                                </d-btn>
                            </d-badge>
                            <d-btn v-else
                                   :class="{smallSizeButton: item.size === 'small' }"
                                   :disabled="!item.isEnabled.value"
                                   :icon="item.icon.value"
                                   :rounded="false"
                                   :size="type === 'MAIN' ? (smAndDown ? 'large' : 'x-large') : (smAndDown ? 'small' : 'large')"
                                   :type="item.isActive.value ? 'tertiary' : 'default'"
                                   block
                                   v-bind="tooltipProps"
                                   variant="text"
                                   @click="item.onClick">
                                <template v-if="item.subIcon"
                                          #default>
                                    <div :class="{
                                            'x-large': type === 'MAIN' && !smAndDown,
                                            'large': type === 'MAIN' && smAndDown || type === 'SUB' && !smAndDown,
                                            'small': type === 'SUB' && smAndDown
                                        }"
                                         class="iconWrapper">
                                        <d-icon :icon="item.icon.value"
                                                class="mainIcon"
                                                type="error"/>
                                        <d-icon :icon="item.subIcon"
                                                class="subIcon"
                                                type="error"/>
                                    </div>
                                </template>
                            </d-btn>
                        </d-badge>
                    </template>

                    {{ item.tooltip }}
                </d-tooltip>

                <d-divider v-else-if="item.type === 'DIVIDER'"
                           :thickness="3"
                           class="my-1"/>
            </div>
        </v-slide-y-reverse-transition>
    </d-card>

    <div v-if="!isEmbedded && !activeItemWithSubMenu"
         class="subArea">
        <div v-for="item in items"
             :key="item.id">
            <div v-if="item.isVisible.value">
                <div v-if="item.type === 'BUTTON'"
                     v-show="item.menu"
                     :class="{small: smAndDown}"
                     class="subItem pa-1 align-content-center">
                    <v-slide-x-transition>
                        <d-card v-if="item.isActive.value && item.isEnabled.value && (item.menu && item.menu.isEnabled.value && item.menu.isVisible.value)"
                                class="subCard"
                                variant="elevated">
                            <d-btn :icon="item.menu.isActive.value ? mdiListBox : mdiListBoxOutline"
                                   :rounded="false"
                                   :size="smAndDown ? 'x-small' : 'small'"
                                   :type="item.menu.isActive.value ? 'warning' : 'default'"
                                   block
                                   variant="text"
                                   @click="item.menu?.onClick"/>
                        </d-card>
                    </v-slide-x-transition>
                </div>

                <d-divider v-else-if="item.type === 'DIVIDER'"
                           :thickness="3"
                           class="my-1"
                           style="opacity: 0;"/>
            </div>
        </div>
    </div>

    <!-- v-if="item.type === 'BUTTON' && (item as CadToolbarButton).isActive.value && item.subMenu" -->
    <!--    <v-slide-x-transition v-for="item in items"-->
    <!--                          :key="item.id">-->

    <v-slide-x-transition>
        <div v-if="!isEmbedded && activeItemWithSubMenu"
             class="subMenuWrapper ma-2">
            <div class="subMenu pa-2">
                <d-listing-building-toolbar-items :is-embedded="isEmbedded"
                                                  :items="activeItemWithSubMenu.subMenu!.items"
                                                  type="SUB"/>
            </div>
        </div>
    </v-slide-x-transition>
    <!--    </v-slide-x-transition>-->
</template>

<script lang="ts"
        setup>
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import {mdiListBox, mdiListBoxOutline} from "@mdi/js";
    import DTooltip from "@/adapter/vuetify/theme/components/d-tooltip.vue";
    import DBadge from "@/adapter/vuetify/theme/components/d-badge.vue";
    import DDivider from "@/adapter/vuetify/theme/components/divider/d-divider.vue";
    import {CadToolbarButton, CadToolbarItem} from "@/components/listing/building/cad-toolbar";
    import {useDisplay} from "vuetify";
    import {computed} from "vue";
    import {Optional} from "@/model/Optional";
    import DIcon from "@/adapter/vuetify/theme/components/d-icon.vue";

    const props = defineProps<{
        items: readonly CadToolbarItem[]
        isEmbedded: boolean
        type: "MAIN" | "SUB"
    }>()

    const {smAndDown, platform} = useDisplay()
    const badgeLocation = "bottom"
    const badgetOffsetY = 12
    const tooltipTimeout = computed<number | undefined>(() => platform.value.touch ? 1000 : undefined)

    const activeItemWithSubMenu = computed<Optional<CadToolbarButton>>(() => props.items.find(item => item.type === "BUTTON" && (item as CadToolbarButton).isActive.value && item.subMenu) as (undefined | CadToolbarButton) ?? null)
</script>

<style scoped>
    .mainArea {
        position: relative;
        z-index: 2;
        height: 100%;
        pointer-events: auto;
    }

    .subArea {
        position: relative;
        z-index: 1;
        left: -12px;
        height: 100%;
    }

    .subMenuWrapper {
        position: relative;
        height: 100%;
        z-index: 3;
        align-self: center;
    }

    .subMenu {
        max-height: calc(100% - 16px * 2);
        overflow-y: auto;
    }

    .subItem {
        height: 56px;
        overflow: hidden;
    }

    .subItem.small {
        height: 48px;
    }

    .subCard {
        padding-inline-start: 8px;
        border-left: 0 !important;
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
        pointer-events: auto;
    }

    .iconWrapper {
        position: relative;
        left: -50%;
    }

    .iconWrapper.x-large {
        width: 41px;
        height: 41px;
    }

    .iconWrapper.large {
        width: 30px;
        height: 30px;
    }

    .iconWrapper.small {
        width: 18px;
        height: 18px;
    }

    .iconWrapper .mainIcon {
        position: absolute;
    }

    .iconWrapper .subIcon {
        position: absolute;
        border-radius: 50%;
        left: 80%;
        top: 25%;
        scale: 55%;
        padding: 2px;
        background: rgb(var(--v-theme-d-background-surface));
        border: 1px solid rgb(var(--v-theme-d-text-default));
    }

    .v-btn.v-btn--size-x-large.v-btn--icon.smallSizeButton :deep(.v-icon) {
        --v-icon-size-multiplier: 0.9;
    }
</style>