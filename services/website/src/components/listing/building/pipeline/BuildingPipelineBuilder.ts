import {BuildingRenderer} from "@/components/listing/building/renderer/BuildingRenderer";
import {BuildingPipelineStage} from "@/components/listing/building/pipeline/BuildingPipelineStage";
import {SaveBuildingBPipelineStage} from "@/components/listing/building/pipeline/stages/SaveBuildingBPipelineStage";
import {Floor} from "@/adapter/graphql/generated/graphql";
import {SplitWallsOnFloorBPipelineStage} from "@/components/listing/building/pipeline/stages/SplitWallsOnFloorBPipelineStage";
import {BuildingPipeline} from "@/components/listing/building/pipeline/BuildingPipeline";
import {RenewFloorBPipelineStage} from "@/components/listing/building/pipeline/stages/RenewFloorBPipelineStage";
import {RenewBuildingBPipelineStage} from "@/components/listing/building/pipeline/stages/RenewBuildingBPipelineStage";
import {floorsOfBuildingComponents} from "@/components/listing/building/building";
import {MergeWallsOnFloorBPipelineStage} from "@/components/listing/building/pipeline/stages/MergeWallsOnFloorBPipelineStage";
import {RenewRoomsOnFloorBPipelineStage} from "@/components/listing/building/pipeline/stages/RenewRoomsOnFloorBPipelineStage";
import {RemoveZeroWallsOnFloorBPipelineStage} from "@/components/listing/building/pipeline/stages/RemoveZeroWallsOnFloorBPipelineStage";
import {RemoveDuplicateWallsOnFloorBPipelineStage} from "@/components/listing/building/pipeline/stages/RemoveDuplicateWallsOnFloorBPipelineStage";
import {RecalculateAllWallSizeAdjustmentsBPipelineStage} from "@/components/listing/building/pipeline/stages/RecalculateAllWallSizeAdjustmentsBPipelineStage";
import {TraversalBuildingComponent} from "@/components/listing/building/renderer/TraversableBuilding";
import {RenewDisplayIdsAndRoomNumbersBPipelineStage} from "@/components/listing/building/pipeline/stages/RenewDisplayIdsAndRoomNumbersBPipelineStage";
import {RemoveInvalidRelationsBPipelineStage} from "@/components/listing/building/pipeline/stages/RemoveInvalidRelationsBPipelineStage";
import {FlipExteriorWallsOnFloorBPipelineStage} from "@/components/listing/building/pipeline/stages/FlipExteriorWallsOnFloorBPipelineStage";

export class BuildingPipelineBuilder {
    private saveBuildingStage?: BuildingPipelineStage
    private recalculateAllWallSizeAdjustmentsStage?: RecalculateAllWallSizeAdjustmentsBPipelineStage
    private readonly splitWallsOnFloorStages: SplitWallsOnFloorBPipelineStage[] = []
    private readonly mergeWallsOnFloorStages: MergeWallsOnFloorBPipelineStage[] = []
    private readonly removeZeroWallsOnFloorStages: RemoveZeroWallsOnFloorBPipelineStage[] = []
    private readonly removeDuplicateWallsOnFloorStages: RemoveDuplicateWallsOnFloorBPipelineStage[] = []
    private readonly renewRoomsOnFloorStages: RenewRoomsOnFloorBPipelineStage[] = []
    private readonly flipExteriorWallsOnFloorStages: FlipExteriorWallsOnFloorBPipelineStage[] = []
    private readonly renewFloorStages: RenewFloorBPipelineStage[] = []
    private renewBuildingStage?: BuildingPipelineStage
    private renewDisplayIdsAndRoomNumbersStage?: BuildingPipelineStage
    private removeInvalidRelationsStage?: BuildingPipelineStage

    private constructor(
        private readonly name: string,
        private readonly renderer: BuildingRenderer
    ) {
    }

    static create(name: string, renderer: BuildingRenderer): BuildingPipelineBuilder {
        return new BuildingPipelineBuilder(name, renderer)
    }

    // noinspection ParameterNamingConventionJS
    doAll(deskewFloors: boolean): this {
        this.splitWallsOnFloors(...this.renderer.building.value.floors)
        this.mergeWallsOnFloors(this.renderer.building.value.floors)
        this.removeZeroWallsOnFloors(...this.renderer.building.value.floors)
        this.removeDuplicateWallsOnFloors(...this.renderer.building.value.floors)
        this.renewRoomsOnFloors(this.renderer.building.value.floors)
        this.flipExteriorWallsOnFloors(...this.renderer.building.value.floors)
        this.renewFloors(...this.renderer.building.value.floors)
        this.renewBuilding(deskewFloors)
        this.recalculateAllWallSizeAdjustments()
        this.renewDisplayIdsAndRoomNumbers()
        this.removeInvalidRelations()
        this.save()
        return this
    }

    save(refreshPageAndRebuildBuilding: boolean = false): this {
        this.saveBuildingStage = new SaveBuildingBPipelineStage(refreshPageAndRebuildBuilding)
        return this
    }

    renewDisplayIdsAndRoomNumbers(): this {
        this.renewDisplayIdsAndRoomNumbersStage = RenewDisplayIdsAndRoomNumbersBPipelineStage
        return this
    }

    removeInvalidRelations(): this {
        this.removeInvalidRelationsStage = RemoveInvalidRelationsBPipelineStage
        return this
    }

    splitWallsOnFloors(...floors: readonly Floor[]): this {
        this.splitWallsOnFloorStages.push(...floors.map(floor => new SplitWallsOnFloorBPipelineStage(floor)))
        return this
    }

    splitWallsOnFloorsOfBuildingComponents(...buildingComponents: readonly TraversalBuildingComponent[]): this {
        const floors = floorsOfBuildingComponents(this.renderer, buildingComponents);
        return this.splitWallsOnFloors(...floors)
    }

    recalculateAllWallSizeAdjustments(forceSnappingManagerUpdates: boolean = false): this {
        this.recalculateAllWallSizeAdjustmentsStage = new RecalculateAllWallSizeAdjustmentsBPipelineStage(forceSnappingManagerUpdates)
        return this
    }

    mergeWallsOnFloors(floors: readonly Floor[], forceSnappingManagerUpdates: boolean = false): this {
        this.mergeWallsOnFloorStages.push(...floors.map(floor => new MergeWallsOnFloorBPipelineStage(floor, forceSnappingManagerUpdates)))
        return this
    }

    mergeWallsOnFloorsOfBuildingComponents(buildingComponents: readonly TraversalBuildingComponent[], forceSnappingManagerUpdates: boolean = false): this {
        const floors = floorsOfBuildingComponents(this.renderer, buildingComponents);
        return this.mergeWallsOnFloors(floors, forceSnappingManagerUpdates)
    }

    removeZeroWallsOnFloors(...floors: readonly Floor[]): this {
        this.removeZeroWallsOnFloorStages.push(...floors.map(floor => new RemoveZeroWallsOnFloorBPipelineStage(floor)))
        return this
    }

    removeZeroWallsOnFloorsOfBuildingComponents(...buildingComponents: readonly TraversalBuildingComponent[]): this {
        const floors = floorsOfBuildingComponents(this.renderer, buildingComponents);
        return this.removeZeroWallsOnFloors(...floors)
    }

    removeDuplicateWallsOnFloors(...floors: readonly Floor[]): this {
        this.removeDuplicateWallsOnFloorStages.push(...floors.map(floor => new RemoveDuplicateWallsOnFloorBPipelineStage(floor)))
        return this
    }

    removeDuplicateWallsOnFloorsOfBuildingComponents(...buildingComponents: readonly TraversalBuildingComponent[]): this {
        const floors = floorsOfBuildingComponents(this.renderer, buildingComponents);
        return this.removeDuplicateWallsOnFloors(...floors)
    }

    renewFloors(...floors: readonly Floor[]): this {
        this.renewFloorStages.push(...floors.map(floor => new RenewFloorBPipelineStage(floor)))
        return this
    }

    renewFloorsOfBuildingComponents(...buildingComponents: readonly TraversalBuildingComponent[]): this {
        const floors = floorsOfBuildingComponents(this.renderer, buildingComponents);
        return this.renewFloors(...floors)
    }

    renewRoomsOnFloors(floors: readonly Floor[], forceSnappingManagerUpdates: boolean = false): this {
        this.renewRoomsOnFloorStages.push(...floors.map(floor => new RenewRoomsOnFloorBPipelineStage(floor, forceSnappingManagerUpdates)))
        return this
    }

    renewRoomsOnFloorsOfBuildingComponents(buildingComponents: readonly TraversalBuildingComponent[], forceSnappingManagerUpdates: boolean = false): this {
        const floors = floorsOfBuildingComponents(this.renderer, buildingComponents);
        return this.renewRoomsOnFloors(floors, forceSnappingManagerUpdates)
    }

    flipExteriorWallsOnFloors(...floors: readonly Floor[]): this {
        this.flipExteriorWallsOnFloorStages.push(...floors.map(floor => new FlipExteriorWallsOnFloorBPipelineStage(floor)))
        return this
    }

    flipExteriorWallsOnFloorsOfBuildingComponents(...buildingComponents: readonly TraversalBuildingComponent[]): this {
        const floors = floorsOfBuildingComponents(this.renderer, buildingComponents);
        return this.flipExteriorWallsOnFloors(...floors)
    }

    renewBuilding(deskewFloors: boolean): this {
        this.renewBuildingStage = new RenewBuildingBPipelineStage(deskewFloors)
        return this
    }

    build(): BuildingPipeline {
        const stages: BuildingPipelineStage[] = []

        stages.push(...this.splitWallsOnFloorStages)
        stages.push(...this.mergeWallsOnFloorStages)
        stages.push(...this.removeZeroWallsOnFloorStages)
        stages.push(...this.removeDuplicateWallsOnFloorStages)
        stages.push(...this.renewRoomsOnFloorStages)
        stages.push(...this.flipExteriorWallsOnFloorStages)
        stages.push(...this.renewFloorStages)

        if (this.renewBuildingStage) {
            stages.push(this.renewBuildingStage)
        }

        if (this.recalculateAllWallSizeAdjustmentsStage) {
            stages.push(this.recalculateAllWallSizeAdjustmentsStage)
        }

        if (this.renewDisplayIdsAndRoomNumbersStage) {
            stages.push(this.renewDisplayIdsAndRoomNumbersStage)
        }

        if (this.removeInvalidRelationsStage) {
            stages.push(this.removeInvalidRelationsStage)
        }

        if (this.saveBuildingStage) {
            stages.push(this.saveBuildingStage)
        }

        return new BuildingPipeline(
            this.name,
            this.renderer,
            stages,
        )
    }
}