import {BuildingPipelineStage} from "@/components/listing/building/pipeline/BuildingPipelineStage";
import {BuildingRenderer} from "@/components/listing/building/renderer/BuildingRenderer";
import {nextTick} from "vue";
import {BUILDING_RENDERER_EMPTY_ID, BuildingRendererEmpty} from "@/components/listing/building/renderer/BuildingRendererEmpty";
import {v4 as uuidv4} from "uuid";
import {createIdentityMatrix} from "@/components/listing/building-creation/pipeline/building-creation-pipeline";
import {EnsureDefined} from "@/adapter/graphql/mapper/graphql-mapper";
import {ConstructionPart, Polygon, ShapeRepresentation, Vector3D} from "@/adapter/graphql/generated/graphql";
import {reportMessageToBff} from "@/adapter/graphql/apollo-links/MonitoringReporter";

export class BuildingPipeline {
    constructor(
        private readonly name: string,
        private readonly renderer: BuildingRenderer,
        private readonly stages: readonly BuildingPipelineStage[]
    ) {
    }

    async execute(): Promise<void> {
        console.log("=================================")
        console.log('Executing building pipeline', this.name)
        reportMessageToBff("Building Pipeline", `Executing building pipeline ${this.name} for listing ${this.renderer.listingId}`)

        if (this.stages.length <= 0) {
            console.log('No stages to execute')
            reportMessageToBff("Building Pipeline", `No stages to execute for pipeline ${this.name} for listing ${this.renderer.listingId}`)
        } else {
            this.renderer.isPipelineRunning.value = true

            const stageNameToExecutionTime = new Map<string, number>()

            //Warum zwei Ticks? Weil sehr oft vor dem Ausführen der Pipeline "++renderer.buildingShapeComputationCounter.value" ausgeführt wird und das die SnapPoints letztlich refreshed.
            //Damit der Watcher im BuildingRenderer "this.watchStopHandles.push(watch(this.buildingShapeComputationCounter …" sein forceUpdate auf dem SnappingManager ausführt (nextTick),
            //müssen wir ein weiteren Tick darauf warten, damit wir schlussendlich die Pipeline starten können, die dann auch neue Versionen der SnapPoints für Folgeberechnungen hat.
            await nextTick(async () => {
                await nextTick(async () => {
                    this.migrateBuildingIfRequired()

                    for (const stage of this.stages) {
                        if (this.renderer.id === BUILDING_RENDERER_EMPTY_ID && this.renderer instanceof BuildingRendererEmpty) {
                            ++this.renderer.snapPointRefreshCounter.value
                        }

                        console.log(`\tProcessing stage ${stage.name}`)
                        reportMessageToBff("Building Pipeline", `Processing stage ${stage.name} for pipeline ${this.name} for listing ${this.renderer.listingId}`)

                        try {
                            const startTime = performance.now()
                            await stage.execute(this.renderer)
                            const endTime = performance.now()
                            const executionTime = endTime - startTime
                            stageNameToExecutionTime.set(stage.name, executionTime)

                            console.log(`\tCompleted stage ${stage.name} in ${Math.round(executionTime)}ms`)
                            reportMessageToBff("Building Pipeline", `Completed stage ${stage.name} for pipeline ${this.name} for listing ${this.renderer.listingId} in ${Math.round(executionTime)}ms`)
                        } catch (e) {
                            console.error(`\tError while executing stage ${stage.name}`, e)
                            reportMessageToBff("Building Pipeline", `Error while executing stage ${stage.name} for pipeline ${this.name} for listing ${this.renderer.listingId}`, JSON.stringify(e))

                            return
                        }
                    }

                    this.renderer.isPipelineRunning.value = false
                })
            })

            console.log("---------------------------------")
            console.log('Building pipeline executed. Stats:')
            reportMessageToBff("Building Pipeline", `Building pipeline ${this.name} for listing ${this.renderer.listingId} executed. Stats to follow.`)

            for (const [stageName, executionTime] of stageNameToExecutionTime.entries().toArray().sort((a, b) => b[1] - a[1])) {
                console.log(`\t${stageName}: ${Math.round(executionTime)}ms`)
                reportMessageToBff("Building Pipeline", `Stage ${stageName} for pipeline ${this.name} for listing ${this.renderer.listingId} took ${Math.round(executionTime)}ms`)
            }
        }

        console.log("=================================")
        reportMessageToBff("Building Pipeline", `Completed building pipeline ${this.name} for listing ${this.renderer.listingId}`)
    }

    private migrateBuildingIfRequired() {
        console.log(`Migrating building if required for listing ${this.renderer.listingId}`)
        reportMessageToBff("Building Pipeline", `Migrating building if required for listing ${this.renderer.listingId}`)

        const floors = this.renderer.building.value.floors
        if (floors.length <= 0) {
            console.log(`No floors to migrate for listing ${this.renderer.listingId}`)
            reportMessageToBff("Building Pipeline", `No floors to migrate for listing ${this.renderer.listingId}`)

            return
        }

        const floorLevels = floors.map(f => f.level)
        const maxFloorLevel = Math.max(...floorLevels)

        console.log(`Migrating ${floors.length} floors for listing ${this.renderer.listingId}, max floor level: ${maxFloorLevel}`)
        reportMessageToBff("Building Pipeline", `Migrating ${floors.length} floors for listing ${this.renderer.listingId}, max floor level: ${maxFloorLevel}`)

        //MIGRATE OLD FLOORS WITHOUT CEILING SLAB
        let migratedFloorCount = 0
        for (const floor of floors) {
            if (maxFloorLevel === floor.level) {
                floor.ceilingSlab = null
                continue
            }
            if (floor.ceilingSlab === null || floor.ceilingSlab === undefined) {
                floor.ceilingSlab = {
                    __typename: "ConstructionPart",
                    id: uuidv4(),
                    displayId: null,
                    type: "CEILING",
                    customData: [],
                    foreignRoomId: null,
                    shapeRepresentation: {
                        __typename: "ShapeRepresentation",
                        transformationMatrix: createIdentityMatrix(),
                        shape: {
                            __typename: "Polygon",
                            vertices: [],
                            holes: [],
                            extrusion: 0,
                            extrusionDirection: {
                                __typename: "Vector3D",
                                x: 0,
                                y: 1,
                                z: 0
                            } satisfies EnsureDefined<Vector3D>
                        } satisfies EnsureDefined<Polygon>
                    } satisfies EnsureDefined<ShapeRepresentation>
                } satisfies EnsureDefined<ConstructionPart>
                migratedFloorCount++
            }
        }

        if (migratedFloorCount > 0) {
            console.log(`Migrated ${migratedFloorCount} floors with missing ceiling slabs for listing ${this.renderer.listingId}`)
            reportMessageToBff("Building Pipeline", `Migrated ${migratedFloorCount} floors with missing ceiling slabs for listing ${this.renderer.listingId}`)
        } else {
            console.log(`No floors needed migration for listing ${this.renderer.listingId}`)
            reportMessageToBff("Building Pipeline", `No floors needed migration for listing ${this.renderer.listingId}`)
        }
    }
}