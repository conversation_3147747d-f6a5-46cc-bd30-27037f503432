import {BuildingPipelineStage} from "@/components/listing/building/pipeline/BuildingPipelineStage";
import {BuildingRenderer} from "@/components/listing/building/renderer/BuildingRenderer";
import {Floor, ShapeRepresentation, Wall} from "@/adapter/graphql/generated/graphql";
import {areNumbersEqual} from "@/utility/number";
import {BUILDING_EPSILON, wallIdToSnapPointIds, widthOfShape} from "@/components/listing/building/building";
import {Vector2} from "three";
import {tAreVectors2Equal} from "@/adapter/three/three-utility";

// noinspection JSClassNamingConvention
export class RemoveDuplicateWallsOnFloorBPipelineStage extends BuildingPipelineStage {
    constructor(private readonly floor: Floor) {
        super(`RemoveDuplicateWallsOnFloor(floorId=${floor.id})`)
    }

    async execute(renderer: BuildingRenderer): Promise<void> {
        const walls = [...this.floor.walls]

        for (let i = 0; i < walls.length; ++i) {
            const wall1 = walls[i]

            for (let j = i + 1; j < walls.length; ++j) {
                const wall2 = walls[j]

                if (this.areShapeRepresentationsAlmostTheSame(wall1.shapeRepresentation, wall2.shapeRepresentation)) {
                    const wallToDelete = this.decideWhichWallToDelete(wall1, wall2)

                    console.log(`Removed wall ${wallToDelete.id} because it is a duplicate of ${wallToDelete.id === wall1.id ? wall2.id : wall1.id}`)

                    this.floor.walls = this.floor.walls.filter(wall => wall.id !== wallToDelete.id)

                    for (const snapPointId of wallIdToSnapPointIds(wallToDelete.id)) {
                        renderer.snappingManager.removeSnapPointById(snapPointId)
                    }

                    renderer.traversableBuilding.removeComponent(wallToDelete)
                }
            }
        }
    }

    private decideWhichWallToDelete(wall1: Wall, wall2: Wall): Wall {
        if (wall1.openings.length < wall2.openings.length) {
            return wall1
        }
        if (wall1.openings.length > wall2.openings.length) {
            return wall2
        }
        const customData1 = wall1.customData.filter(cd => !cd.isComputed);
        const customData2 = wall2.customData.filter(cd => !cd.isComputed);
        if (customData1.length < customData2.length) {
            return wall1
        }
        if (customData1.length > customData2.length) {
            return wall2
        }
        return wall2
    }

    private areShapeRepresentationsAlmostTheSame(shapeRepresentation1: ShapeRepresentation, shapeRepresentation2: ShapeRepresentation): boolean {
        //Transformationen müssen immer fast gleich sein
        if (!this.areTransformationMatricesAlmostTheSame(shapeRepresentation1.transformationMatrix, shapeRepresentation2.transformationMatrix)) {
            return false
        }

        const shape1 = shapeRepresentation1.shape
        const shape2 = shapeRepresentation2.shape

        //Bei zwei Ringen muss sehr viel übereinstimmen, Stärke und Höhe aber ist egal
        if (shape1.__typename === "Ring" && shape2.__typename === "Ring") {
            return areNumbersEqual(shape1.radius, shape2.radius, BUILDING_EPSILON) &&
                areNumbersEqual(shape1.startAngle, shape2.startAngle, BUILDING_EPSILON) &&
                areNumbersEqual(shape1.endAngle, shape2.endAngle, BUILDING_EPSILON) &&
                tAreVectors2Equal(
                    new Vector2(shape1.center.x, shape1.center.y),
                    new Vector2(shape2.center.x, shape2.center.y),
                    BUILDING_EPSILON
                )
        }

        //Iwas im Vergleich mit einem Ring, kann nie gleich sein
        if (shape1.__typename === "Ring" || shape2.__typename === "Ring") {
            return false
        }

        //Boxen und Polygone sind beide ähnlich genug, dann zählt nur die Breite
        const width1 = widthOfShape(shape1)
        const width2 = widthOfShape(shape2)
        return areNumbersEqual(width1, width2, BUILDING_EPSILON)
    }

    private areTransformationMatricesAlmostTheSame(transformationMatrix1: number[][], transformationMatrix2: number[][]): boolean {
        for (let i = 0; i < 4; ++i) {
            for (let j = 0; j < 4; ++j) {
                if (!areNumbersEqual(transformationMatrix1[i][j], transformationMatrix2[i][j], BUILDING_EPSILON)) {
                    return false
                }
            }
        }
        return true
    }
}