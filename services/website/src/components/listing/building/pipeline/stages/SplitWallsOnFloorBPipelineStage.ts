import {BuildingPipelineStage} from "@/components/listing/building/pipeline/BuildingPipelineStage";
import {BuildingRenderer} from "@/components/listing/building/renderer/BuildingRenderer";
import {Floor, Vector3D, Wall} from "@/adapter/graphql/generated/graphql";
import {Matrix4, Vector2, Vector3} from "three";
import {v4 as uuidv4} from "uuid";
import {BUILDING_EPSILON, BUILDING_WALL_MIN_WIDTH, changeWallShape, createWallSnapPoint, deepCopyShapeRepresentation, heightOfShape, updateWallShapeRepresentation, wallIdToSnapPointIds, widthOfShape} from "@/components/listing/building/building";
import {Optional} from "@/model/Optional";
import {tAreVectors2Equal, tDecomposeMatrix, tVectors2ToBox2} from "@/adapter/three/three-utility";
import {intersectionPointXZOfLineSegments, LineSegment, LineSegmentIntersectionPoint, wallOrOpeningToLineSegments} from "@/components/listing/building/wall-and-opening-lines";
import {matrix4ToTransformationMatrixArray, transformationMatrixOfShapeRepresentation} from "@/components/listing/building-creation/pipeline/building-creation-pipeline";
import {toRaw, toRef} from "vue";
import {BUILDING_RENDERER_EMPTY_ID} from "@/components/listing/building/renderer/BuildingRendererEmpty";
import {clipperIntersectPolygons, clipperPathsToVectors2, clipperPolygonToPaths} from "@/adapter/clipper/clipper-utils";
import {convert} from "@/utility/converter";
import {areNumbersEqual, isNumberGreaterThan, isNumberLessThan} from "@/utility/number";
import {Paths64} from "clipper2-js";

// noinspection JSClassNamingConvention
type FilteredWallLineSegmentIntersectionPoint = {
    readonly intersectionPoint: Vector3
    readonly intersectionPointXZ: Vector2
    readonly lineSegment1?: LineSegment<Wall>
    readonly lineSegment2?: LineSegment<Wall>
}

export class SplitWallsOnFloorBPipelineStage extends BuildingPipelineStage {
    constructor(private readonly floor: Floor) {
        super(`SplitWallsOnFloor(floorId=${floor.id})`)
    }

    private static updatePolygonalWallHeight(wall: Wall, oldHeight: number) {
        const newHeight = heightOfShape(wall.shapeRepresentation.shape)
        const heightOffset = newHeight - oldHeight
        const halfHeightOffset = heightOffset / 2

        const transformation = transformationMatrixOfShapeRepresentation(wall.shapeRepresentation)
        const wallHeightDeltaTranslationMatrix = new Matrix4().makeTranslation(0, halfHeightOffset, 0)
        const newWallTransformation = transformation.clone().multiply(wallHeightDeltaTranslationMatrix)

        wall.shapeRepresentation = {
            ...deepCopyShapeRepresentation(wall.shapeRepresentation),
            transformationMatrix: matrix4ToTransformationMatrixArray(newWallTransformation),
        }
    }

    /**
     * Prüfung, ob eine Wand in der Mitte geschnitten wurde.
     * Außerdem muss die Wandmindestbreite erreicht werden.
     */
    private static validLineSegmentOrNull(lineSegment: LineSegment<Wall>, intersectionPoint: LineSegmentIntersectionPoint<Wall>): Optional<LineSegment<Wall>> {
        if (lineSegment.wallOrOpening.shapeRepresentation.shape.__typename !== "Box" && lineSegment.wallOrOpening.shapeRepresentation.shape.__typename !== "Polygon") {
            return null //TODO: runde Wände supporten
        }
        const distanceFromStart = lineSegment.start.distanceTo(intersectionPoint.intersectionPointXZ)
        if (isNumberLessThan(distanceFromStart, BUILDING_WALL_MIN_WIDTH, BUILDING_EPSILON)) {
            return null //würden wir das erlauben, würde die neue Wand direkt gelöscht werden durch die Pipeline-Stage "RemoveZeroWalls"
        }
        const distanceToEnd = lineSegment.end.distanceTo(intersectionPoint.intersectionPointXZ)
        if (isNumberLessThan(distanceToEnd, BUILDING_WALL_MIN_WIDTH, BUILDING_EPSILON)) {
            return null //würden wir das erlauben, würde die neue Wand direkt gelöscht werden durch die Pipeline-Stage "RemoveZeroWalls"
        }
        return lineSegment
    }

    async execute(renderer: BuildingRenderer): Promise<void> {
        this.splitWalls(renderer)
    }

    private splitWalls(
        renderer: BuildingRenderer,
        maxRecursionDepth: number = 5000,
        currentRecursionDepth: number = 0
    ) {
        const wallLineSegments = this.floor.walls.flatMap(wall => wallOrOpeningToLineSegments(renderer, wall, wall, false))
        const intersectionPoint = this.calculateNextWallLineIntersectionPoint(wallLineSegments)

        if (intersectionPoint === null) {
            return
        }

        if (currentRecursionDepth >= maxRecursionDepth) {
            console.error(`Max recursion depth of ${maxRecursionDepth} reached. Aborting split walls.`, intersectionPoint)
            return
        }

        if (intersectionPoint.lineSegment1) {
            this.splitWall(renderer, intersectionPoint.lineSegment1, intersectionPoint.intersectionPointXZ)
        }
        if (intersectionPoint.lineSegment2) {
            this.splitWall(renderer, intersectionPoint.lineSegment2, intersectionPoint.intersectionPointXZ)
        }

        this.splitWalls(
            renderer,
            maxRecursionDepth,
            currentRecursionDepth + 1
        ) //tail recursion
    }

    // noinspection FunctionTooLongJS,OverlyComplexFunctionJS
    private splitWall(renderer: BuildingRenderer, wallLineSegment: LineSegment<Wall>, intersectionPointXZ: Vector2) {
        const wall = wallLineSegment.wallOrOpening
        if (wall.shapeRepresentation.shape.__typename !== "Box" && wall.shapeRepresentation.shape.__typename !== "Polygon") {
            //TODO: RUNDUNGEN
            throw new Error("Wall shape type not supported: " + wall.shapeRepresentation.shape.__typename)
        }

        const wallTransformation = transformationMatrixOfShapeRepresentation(wall.shapeRepresentation)

        const newWall1: Wall = JSON.parse(JSON.stringify(toRaw(wall)))
        const newWall2: Wall = JSON.parse(JSON.stringify(toRaw(wall)))
        newWall1.id = uuidv4()
        newWall2.id = uuidv4()
        newWall1.displayId = null
        newWall2.displayId = null
        newWall1.openings = newWall1.openings.map(o => ({
            ...o,
            id: uuidv4(),
            displayId: null
        }))
        newWall2.openings = newWall2.openings.map(o => ({
            ...o,
            id: uuidv4(),
            displayId: null
        }))

        console.log(`Spliting wall ${wall.id} to ${newWall1.id} and ${newWall2.id} …`)

        //erst die wall attachen und dann die wall shape updaten, damit die floor transformation berücktsichtigt wird
        this.floor.walls = this.floor.walls.filter(w => w.id !== wall.id)
        this.floor.walls.push(newWall1, newWall2)

        for (const snapPointId of wallIdToSnapPointIds(wall.id)) {
            renderer.snappingManager.removeSnapPointById(snapPointId)
        }

        renderer.traversableBuilding.addComponent(newWall1)
        renderer.traversableBuilding.addComponent(newWall2)

        //für den fall, dass wir einen empty renderer haben, müssen die snap points manuell entfernt werden
        if (renderer.id === BUILDING_RENDERER_EMPTY_ID) {
            const leftSnapPoint1 = createWallSnapPoint(renderer, toRef(newWall1), "LEFT", () => {
            })
            const rightSnapPoint1 = createWallSnapPoint(renderer, toRef(newWall1), "RIGHT", () => {
            })
            const leftSnapPoint2 = createWallSnapPoint(renderer, toRef(newWall2), "LEFT", () => {
            })
            const rightSnapPoint2 = createWallSnapPoint(renderer, toRef(newWall2), "RIGHT", () => {
            })
            renderer.snappingManager.addSnapPoint(leftSnapPoint1)
            renderer.snappingManager.addSnapPoint(rightSnapPoint1)
            renderer.snappingManager.addSnapPoint(leftSnapPoint2)
            renderer.snappingManager.addSnapPoint(rightSnapPoint2)
        }

        const p1 = wallLineSegment.start
        const p2 = intersectionPointXZ
        const p3 = wallLineSegment.end

        if (tAreVectors2Equal(p1, p2, BUILDING_EPSILON)) {
            console.log("p1", p1)
            console.log("p2", p2)
            throw new Error("p1 and p2 are equal")
        }
        if (tAreVectors2Equal(p2, p3, BUILDING_EPSILON)) {
            console.log("p2", p2)
            console.log("p3", p3)
            throw new Error("p2 and p3 are equal")
        }

        const distance12 = p1.distanceTo(p2)
        const distance13 = p1.distanceTo(p3)

        if (areNumbersEqual(distance12, distance13, BUILDING_EPSILON)) {
            console.log("p1", p1)
            console.log("p2", p2)
            console.log("p3", p3)
            throw new Error("distance12 and distance23 are equal")
        }

        const splitWidthPercentage = distance12 / distance13

        if (areNumbersEqual(splitWidthPercentage, 0, BUILDING_EPSILON)) {
            console.log("p1", p1)
            console.log("p2", p2)
            console.log("p3", p3)
            throw new Error("splitWidthPercentage is 0")
        }
        if (areNumbersEqual(splitWidthPercentage, 1, BUILDING_EPSILON)) {
            console.log("p1", p1)
            console.log("p2", p2)
            console.log("p3", p3)
            throw new Error("splitWidthPercentage is 1")
        }

        updateWallShapeRepresentation(renderer, newWall1, p1, p2)
        updateWallShapeRepresentation(renderer, newWall2, p2, p3)

        this.updateVerticesIfPolygonalWall(wall, newWall1, true, splitWidthPercentage)
        this.updateVerticesIfPolygonalWall(wall, newWall2, false, splitWidthPercentage)

        renderer.traversableBuilding.removeComponent(wall) //erst jetzt entfernen, weil updateVerticesIfPolygonalWall noch die alte Wand braucht

        const newWall1Transformation = transformationMatrixOfShapeRepresentation(newWall1.shapeRepresentation)
        const newWall2Transformation = transformationMatrixOfShapeRepresentation(newWall2.shapeRepresentation)

        const newWall1InverseTransformation = newWall1Transformation.clone().invert()
        const newWall2InverseTransformation = newWall2Transformation.clone().invert()

        for (const opening1 of newWall1.openings) {
            renderer.traversableBuilding.addComponent(opening1)

            const openingTransformation = transformationMatrixOfShapeRepresentation(opening1.shapeRepresentation)
            const newOpeningTransformation = newWall1InverseTransformation.clone().multiply(wallTransformation).multiply(openingTransformation)
            opening1.shapeRepresentation.transformationMatrix = matrix4ToTransformationMatrixArray(newOpeningTransformation)
        }

        for (const opening2 of newWall2.openings) {
            renderer.traversableBuilding.addComponent(opening2)

            const openingTransformation = transformationMatrixOfShapeRepresentation(opening2.shapeRepresentation)
            const newOpeningTransformation = newWall2InverseTransformation.clone().multiply(wallTransformation).multiply(openingTransformation)
            opening2.shapeRepresentation.transformationMatrix = matrix4ToTransformationMatrixArray(newOpeningTransformation)
        }

        this.removeOpeningsOutsideOfWall(renderer, newWall1)
        this.removeOpeningsOutsideOfWall(renderer, newWall2)
    }

    // noinspection FunctionTooLongJS
    private updateVerticesIfPolygonalWall(oldWall: Wall, newWall: Wall, isLeftWall: boolean, widthPercentage: number): void {
        if (oldWall.shapeRepresentation.shape.__typename !== "Polygon" || newWall.shapeRepresentation.shape.__typename !== "Polygon") {
            return
        }
        const oldRawVertices = oldWall.shapeRepresentation.shape.vertices
        if (oldRawVertices.length < 4) { //letzter Punkt ist der erste Punkt und wir brauchen mindestens 3 Punkte
            SplitWallsOnFloorBPipelineStage.updatePolygonalWallHeight(newWall, 0)
            return
        }

        const oldVertices = oldRawVertices.map(v => new Vector2(v.x, v.y))
        const oldPaths = clipperPolygonToPaths(oldVertices, [])
        const oldBbox = tVectors2ToBox2(oldVertices)
        const oldCenter = oldBbox.getCenter(new Vector2())
        const oldSize = oldBbox.getSize(new Vector2())

        const localIntersectionPoint2DX = convert(widthPercentage, 0, 1, oldBbox.min.x, oldBbox.max.x, BUILDING_EPSILON)
        const localIntersectionPoint2D = new Vector2(localIntersectionPoint2DX, 0)

        // console.log("=============")
        // console.log("bbox.min", oldBbox.min)
        // console.log("bbox.max", oldBbox.max)
        // console.log("oldSize", oldSize)
        // console.log("localIntersectionPoint2D", localIntersectionPoint2D)
        // console.log("widthPercentage", widthPercentage)

        if (isNumberLessThan(localIntersectionPoint2D.x, oldBbox.min.x, BUILDING_EPSILON) || isNumberGreaterThan(localIntersectionPoint2D.x, oldBbox.max.x, BUILDING_EPSILON)) {
            console.error("Intersection point not on wall. Recreating wall with default size.", localIntersectionPoint2D, oldBbox)

            //recreate polygon wall with default size
            changeWallShape(newWall, "Box")
            changeWallShape(newWall, "Polygon")

            SplitWallsOnFloorBPipelineStage.updatePolygonalWallHeight(newWall, oldSize.y)
            return
        }

        let newPaths: Paths64

        if (isLeftWall) {
            const leftRect = [
                new Vector2(oldBbox.min.x, oldBbox.min.y),
                new Vector2(oldBbox.min.x, oldBbox.max.y),
                new Vector2(localIntersectionPoint2D.x, oldBbox.max.y),
                new Vector2(localIntersectionPoint2D.x, oldBbox.min.y),
            ]
            const leftRectPaths = clipperPolygonToPaths(leftRect, [])
            newPaths = clipperIntersectPolygons(leftRectPaths, oldPaths)
        } else {
            const rightRect = [
                new Vector2(localIntersectionPoint2D.x, oldBbox.min.y),
                new Vector2(localIntersectionPoint2D.x, oldBbox.max.y),
                new Vector2(oldBbox.max.x, oldBbox.max.y),
                new Vector2(oldBbox.max.x, oldBbox.min.y),
            ]
            const rightRectPaths = clipperPolygonToPaths(rightRect, [])
            newPaths = clipperIntersectPolygons(rightRectPaths, oldPaths)
        }

        const newVertices = clipperPathsToVectors2(newPaths)

        if (newVertices.length !== 1) {
            console.error("Polygons couldn't be intersected. Recreating wall with default size.", localIntersectionPoint2D, oldBbox)

            //recreate polygon wall with default size
            changeWallShape(newWall, "Box")
            changeWallShape(newWall, "Polygon")

            SplitWallsOnFloorBPipelineStage.updatePolygonalWallHeight(newWall, oldSize.y)
            return
        }

        const newBbox = tVectors2ToBox2(newVertices[0])
        const newCenter = newBbox.getCenter(new Vector2())
        const offset = newCenter.clone().sub(oldCenter)
        const transformedVertices = newVertices[0].map(v => v.clone().sub(offset))

        newWall.shapeRepresentation.shape.vertices = transformedVertices.map(v => ({
            x: v.x,
            y: v.y,
            z: 0
        } satisfies Vector3D))

        SplitWallsOnFloorBPipelineStage.updatePolygonalWallHeight(newWall, oldSize.y)
    }

    private removeOpeningsOutsideOfWall(renderer: BuildingRenderer, wall: Wall): void {
        const deletedOpeningIndices: number[] = []

        const wallWidth = widthOfShape(wall.shapeRepresentation.shape)
        const halfWallWidth = wallWidth / 2

        for (let i = 0; i < wall.openings.length; ++i) {
            const opening = wall.openings[i]
            const transformation = transformationMatrixOfShapeRepresentation(opening.shapeRepresentation)
            const [translation] = tDecomposeMatrix(transformation)

            if (isNumberLessThan(translation.x, -halfWallWidth, BUILDING_EPSILON) || isNumberGreaterThan(translation.x, halfWallWidth, BUILDING_EPSILON)) {
                deletedOpeningIndices.push(i)
            }
        }

        for (let i = deletedOpeningIndices.length - 1; i >= 0; --i) {
            const index = deletedOpeningIndices[i]
            const opening = wall.openings[index]
            wall.openings.splice(index, 1)
            renderer.traversableBuilding.removeComponent(opening)
        }
    }

    /**
     * Hier müssen wir aufpassen, dass immer nur ein gültiger Schnittpunkt zurückgegeben wird, der auch aufgelöst werden kann:
     * D.h. im Klartext: Wenn sich zwei Linien treffen, muss mind. eine davon mittig geschnitten werden und diese Linie zu einer Box oder einem Polygon gehören.
     */
    private calculateNextWallLineIntersectionPoint(wallLineSegments: readonly LineSegment<Wall>[]): Optional<FilteredWallLineSegmentIntersectionPoint> {
        for (const lineSegment1 of wallLineSegments) {
            for (const lineSegment2 of wallLineSegments) {
                if (lineSegment1.wallOrOpening.id === lineSegment2.wallOrOpening.id) {
                    continue
                }

                const intersectionPoint = intersectionPointXZOfLineSegments(lineSegment1, lineSegment2, true)
                if (intersectionPoint === null) {
                    continue
                }

                const newLineSegment1 = SplitWallsOnFloorBPipelineStage.validLineSegmentOrNull(lineSegment1, intersectionPoint)
                const newLineSegment2 = SplitWallsOnFloorBPipelineStage.validLineSegmentOrNull(lineSegment2, intersectionPoint)

                if (newLineSegment1 === null && newLineSegment2 === null) {
                    continue
                }

                return {
                    intersectionPoint: intersectionPoint.intersectionPoint,
                    intersectionPointXZ: intersectionPoint.intersectionPointXZ,
                    lineSegment1: newLineSegment1 ?? undefined,
                    lineSegment2: newLineSegment2 ?? undefined,
                }
            }
        }
        return null
    }
}