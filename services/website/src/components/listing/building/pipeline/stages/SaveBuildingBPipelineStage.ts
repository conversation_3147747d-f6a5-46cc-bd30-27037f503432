import {BuildingPipelineStage} from "@/components/listing/building/pipeline/BuildingPipelineStage";
import {BuildingRenderer} from "@/components/listing/building/renderer/BuildingRenderer";

export class SaveBuildingBPipelineStage extends BuildingPipelineStage {
    constructor(private readonly reloadPageAndRebuildBuilding: boolean) {
        super(`SaveBuilding(reloadPageAndRebuildBuilding=${reloadPageAndRebuildBuilding})`)
    }

    async execute(renderer: BuildingRenderer): Promise<void> {
        renderer
            .saveBuilding(undefined, undefined, this.reloadPageAndRebuildBuilding)
            .then()
            .catch(e => {
                console.error(`\tError while executing stage ${this.name}`, e)
            })
    }
}