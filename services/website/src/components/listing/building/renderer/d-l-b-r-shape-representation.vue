<template>
    <d-l-b-r-shape-representation-custom v-if="'customShapeRepresentation' in component"
                                         :component="component"
                                         :holes="holes">
        <slot/>
    </d-l-b-r-shape-representation-custom>

    <d-l-b-r-shape-representation-default v-else
                                          :component="component"
                                          :holes="holes">
        <slot/>
    </d-l-b-r-shape-representation-default>
</template>

<script lang="ts"
        setup>
    import {Representable} from "@/adapter/graphql/generated/graphql";
    import {TMaybeRaycasterRelated} from "@/adapter/three/raycasting/TRaycasterRelated";
    import DLBRShapeRepresentationCustom from "@/components/listing/building/renderer/d-l-b-r-shape-representation-custom.vue";
    import DLBRShapeRepresentationDefault from "@/components/listing/building/renderer/d-l-b-r-shape-representation-default.vue";
    import {BuildingComponent} from "@/components/listing/building/renderer/BuildingComponent";
    import {TraversalBuildingComponent} from "@/components/listing/building/renderer/TraversableBuilding";

    withDefaults(defineProps<{
        component: TMaybeRaycasterRelated<TraversalBuildingComponent, BuildingComponent>
        holes?: readonly (Representable & { id: string })[]
    }>(), {
        holes: () => []
    })
</script>

<style scoped>
</style>