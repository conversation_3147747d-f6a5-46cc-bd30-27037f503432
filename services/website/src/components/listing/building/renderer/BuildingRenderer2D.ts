import {Color, DoubleSide, FrontSide, LinearToneMapping, LineBasicMaterial, MeshBasicMaterial, MeshBasicMaterialParameters, Vector3} from "three";
import {TMaterial, TMaterialDeclaration} from "@/adapter/three/TMaterial";
import {TSelectionRaycasterEmitterOrConsumer, TSelectionRelated} from "@/adapter/three/raycasting/selection/TSelectionRaycasterEmitterOrConsumer";
import {TOcclusionCullingRaycasterEmitterOrConsumer, TOcclusionCullingRelated} from "@/adapter/three/raycasting/occlusion-culling/TOcclusionCullingRaycasterEmitterOrConsumer";
import {THoverRaycasterEmitterOrConsumer, THoverRelated} from "@/adapter/three/raycasting/hover/THoverRaycasterEmitterOrConsumer";
import {BUILDING_EPSILON, BUILDING_WALL_MIN_WIDTH, floorTypeOfFloor, noThicknessOpeningOffsetZ, noThicknessWallOffsetZ, widthOfShape} from "@/components/listing/building/building";
import {RenderVisibility} from "@/components/listing/building/renderer/RenderVisibility";
import {Optional} from "@/model/Optional";
import {TMaybeRaycasterRelated} from "@/adapter/three/raycasting/TRaycasterRelated";
import {LineMaterial, LineMaterialParameters} from "three/addons/lines/LineMaterial.js";
import {BuildingRenderer, OpeningRelocationMode} from "@/components/listing/building/renderer/BuildingRenderer";
import {BuildingComponent, FloorSlabFloorBuildingComponent, RaycastableBuildingComponent, WallOpeningDoorOutlineBuildingComponent, WallRoofPoint, WallSizeAdjustmentBuildingComponent, WallSnapLineBuildingComponent, WallSnapPointBuildingComponent, WallWithHolesBuildingComponent, WallWithoutHolesBuildingComponent} from "@/components/listing/building/renderer/BuildingComponent";
import {Building, ConstructionPart} from "@/adapter/graphql/generated/graphql";
import {TDraggableRaycasterEmitterOrConsumer, TDraggableRelated} from "@/adapter/three/raycasting/draggable/TDraggableRaycasterEmitterOrConsumer";
import {TRaycaster} from "@/adapter/three/raycasting/TRaycaster";
import {WallCreator} from "@/components/listing/building/renderer/WallCreator";
import {Ref, toRaw} from "vue";
import {HistoryManager} from "@/utility/history-manager";
import {wallTypeOfWall} from "@/model/building/WallType";
import {WallOpeningCreator} from "@/components/listing/building/renderer/WallOpeningCreator";
import {ABSPERRBARKE_SHADER_MATERIAL} from "@/components/listing/building/renderer/shader/AbsperrbarkeShaderMaterial";
import {isNumberGreaterThan} from "@/utility/number";
import {TraversalBuildingComponent} from "@/components/listing/building/renderer/TraversableBuilding";
import {doorbitColors} from "@/adapter/vuetify/theme/doorbit-colors";

// #################
// ### MATERIALS ###
// #################
// --- DEBUG ---
const MATERIAL_DEBUG = new MeshBasicMaterial({
    color: 0xFF00FF,
    side: DoubleSide,
    transparent: true,
    opacity: 0.5,
    depthTest: false,
})

// --- BUILDING ---
const MATERIAL_BUILDING = new MeshBasicMaterial({
    color: 0xFF0000,
    side: FrontSide,
    wireframe: true,
    depthTest: false,
    transparent: true,
})

// --- FLOOR ---
const MATERIAL_FLOOR = new MeshBasicMaterial({
    color: 0x00FFFF,
    side: FrontSide,
    wireframe: true,
    depthTest: false,
    transparent: true,
})

// --- FLOOR SLAB FLOOR ---
const MATERIAL_PARAM_FLOOR_SLAB_FLOOR: MeshBasicMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    transparent: true,
}
const MATERIAL_FLOOR_SLAB_FLOOR_DEFAULT = new MeshBasicMaterial({
    ...MATERIAL_PARAM_FLOOR_SLAB_FLOOR,
    color: 0xC4C9D6,
})
const MATERIAL_FLOOR_SLAB_FLOOR_BASEMENT = new MeshBasicMaterial({
    ...MATERIAL_PARAM_FLOOR_SLAB_FLOOR,
    color: 0x343951,
})
// --- FLOOR SLAB CEILING ---
const MATERIAL_FLOOR_SLAB_CEILING = new MeshBasicMaterial({
    side: FrontSide,
    color: 0x00FF00,
    depthTest: false,
    transparent: true,
    wireframe: true,
})

// --- ROOM ---
const MATERIAL_ROOM = new MeshBasicMaterial({
    color: 0x0000FF,
    side: FrontSide,
    wireframe: true,
    depthTest: false,
    transparent: true,
})
const MATERIAL_ROOM_UNRECOGNIZED = new MeshBasicMaterial({
    color: 0x000000,
    side: FrontSide,
    depthTest: false,
    transparent: true,
    opacity: 1,
    wireframe: true,
})
// --- ROOM SLAB FLOOR ---
const MATERIAL_ROOM_FLOOR_SLAB_FLOOR: MeshBasicMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    transparent: true,
}
const MATERIAL_ROOM_SLAB_FLOOR_DEFAULT = new MeshBasicMaterial({
    ...MATERIAL_ROOM_FLOOR_SLAB_FLOOR,
    color: 0xE8E8E8,
})
const MATERIAL_ROOM_SLAB_FLOOR_BASEMENT = new MeshBasicMaterial({
    ...MATERIAL_ROOM_FLOOR_SLAB_FLOOR,
    color: 0x444A69,
})
// --- ROOM SLAB FLOOR SEGMENT ---
const MATERIAL_ROOM_SLAB_FLOOR_SEGMENT = new MeshBasicMaterial({
    side: FrontSide,
    color: 0xFF0000,
    depthTest: false,
    transparent: true,
    opacity: 0.5,
})
// --- ROOM SLAB CEILING ---
const MATERIAL_ROOM_SLAB_CEILING = new MeshBasicMaterial({
    side: FrontSide,
    color: 0xFFFF00,
    depthTest: false,
    transparent: true,
    wireframe: true,
})
// --- ROOM SLAB CEILING SEGMENT ---
const MATERIAL_ROOM_SLAB_CEILING_SEGMENT = new MeshBasicMaterial({
    side: FrontSide,
    color: 0x0000FF,
    depthTest: false,
    transparent: true,
    opacity: 0.5,
})

//--- ROOM TEXT BACKGROUND ---
const MATERIAL_ROOM_TEXT_BACKGROUND = new MeshBasicMaterial({
    color: 0x2A2E3F,
    side: FrontSide,
    transparent: true,
    depthTest: false,
})
// --- ROOM TEXT ---
const MATERIAL_ROOM_TEXT = new MeshBasicMaterial({
    color: 0xFEFEFE,
    side: FrontSide,
    transparent: true,
    depthTest: false,
})

// --- POINT OF INTEREST
const MATERIAL_POINT_OF_INTEREST = new MeshBasicMaterial({
    color: 0x00CCCC,
    transparent: true,
})

// --- POINT OF INTEREST CAMERA
const MATERIAL_POINT_OF_INTEREST_CAMERA = new MeshBasicMaterial({
    color: 0x000000,
    transparent: true,
})

// --- DOOR ---
const MATERIAL_PARAM_DOOR: MeshBasicMaterialParameters = {
    color: 0x009F64,
    side: FrontSide,
    depthTest: false,
    transparent: true,
}
const MATERIAL_DOOR_DEFAULT = new MeshBasicMaterial({
    ...MATERIAL_PARAM_DOOR,
})
const MATERIAL_DOOR_DRAG_AND_DROP = new MeshBasicMaterial({
    ...MATERIAL_PARAM_DOOR,
})
const MATERIAL_PARAM_DOOR_OUTLINE: LineMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    worldUnits: true,
    linewidth: 0.03,
    transparent: true,
    opacity: 0.15
}
const MATERIAL_DOOR_OUTLINE_DEFAULT = new LineMaterial({
    ...MATERIAL_PARAM_DOOR_OUTLINE,
    color: 0x005D3A,
})
const MATERIAL_DOOR_OUTLINE_BASEMENT = new LineMaterial({
    ...MATERIAL_PARAM_DOOR_OUTLINE,
    color: 0x43E77F,
})
export const BUILDING_RENDERER_2D_DOORWAY_LINE_WIDTH = 0.03
const MATERIAL_DOORWAY_LINE = new LineMaterial({
    color: 0x009F64,
    side: FrontSide,
    depthTest: false,
    worldUnits: true,
    linewidth: BUILDING_RENDERER_2D_DOORWAY_LINE_WIDTH,
    transparent: true,
})
const MATERIAL_DOORWAY_ARC = new LineMaterial({
    color: 0x009F64,
    side: FrontSide,
    depthTest: false,
    worldUnits: true,
    linewidth: BUILDING_RENDERER_2D_DOORWAY_LINE_WIDTH,
    dashed: true,
    dashScale: 10,
    dashSize: 1,
    gapSize: 1,
    transparent: true,
})

// --- WINDOW ---
const MATERIAL_WINDOW = new MeshBasicMaterial({
    color: 0x76D8FF,
    depthTest: false,
    transparent: true,
})

// --- OPENING ---
const MATERIAL_OPENING = new MeshBasicMaterial({
    color: 0xAE8A27,
    depthTest: false,
    transparent: true,
})

// --- WALL ---
const MATERIAL_PARAM_WALL: MeshBasicMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    transparent: true,
}
const MATERIAL_WALL_NO_CONNECTION = new MeshBasicMaterial({
    ...MATERIAL_PARAM_WALL,
    color: 0xf45446,
})
const MATERIAL_WALL_INTERIOR = new MeshBasicMaterial({
    ...MATERIAL_PARAM_WALL,
    color: 0x848BA5,
})
const MATERIAL_WALL_EXTERIOR = new MeshBasicMaterial({
    ...MATERIAL_PARAM_WALL,
    color: 0x000000,
})
const MATERIAL_WALL_INTERMEDIATE = new MeshBasicMaterial({
    ...MATERIAL_PARAM_WALL,
    color: 0x9F9F9F,
})

// --- SEMI VISIBLE WALL ---
export const SEMI_VISIBLE_FLOOR_COLORS = [
    doorbitColors.lightGreen,
    doorbitColors.lightYellow,
    doorbitColors.lightBlue,
    doorbitColors.brownLight,
    doorbitColors.lightOrange,
    doorbitColors.magic,
]
const MATERIAL_PARAM_WALL_SEMI_VISIBLE: MeshBasicMaterialParameters = {
    ...MATERIAL_PARAM_WALL,
    opacity: 0.5,
}
const MATERIAL_WALL_SEMI_VISIBLE_1 = new MeshBasicMaterial({
    ...MATERIAL_PARAM_WALL_SEMI_VISIBLE,
    color: 0x43E77F,
})
const MATERIAL_WALL_SEMI_VISIBLE_2 = new MeshBasicMaterial({
    ...MATERIAL_PARAM_WALL_SEMI_VISIBLE,
    color: 0xFFCF2E,
})
const MATERIAL_WALL_SEMI_VISIBLE_3 = new MeshBasicMaterial({
    ...MATERIAL_PARAM_WALL_SEMI_VISIBLE,
    color: 0x76D8FF,
})
const MATERIAL_WALL_SEMI_VISIBLE_4 = new MeshBasicMaterial({
    ...MATERIAL_PARAM_WALL_SEMI_VISIBLE,
    color: 0xD49F6E,
})
const MATERIAL_WALL_SEMI_VISIBLE_5 = new MeshBasicMaterial({
    ...MATERIAL_PARAM_WALL_SEMI_VISIBLE,
    color: 0xFFA56B,
})
const MATERIAL_WALL_SEMI_VISIBLE_6 = new MeshBasicMaterial({
    ...MATERIAL_PARAM_WALL_SEMI_VISIBLE,
    color: 0xFF00FF,
})
const MATERIAL_WALL_SEMI_VISIBLE = [
    MATERIAL_WALL_SEMI_VISIBLE_1,
    MATERIAL_WALL_SEMI_VISIBLE_2,
    MATERIAL_WALL_SEMI_VISIBLE_3,
    MATERIAL_WALL_SEMI_VISIBLE_4,
    MATERIAL_WALL_SEMI_VISIBLE_5,
    MATERIAL_WALL_SEMI_VISIBLE_6,
]

// --- WALL TEMP ---
const MATERIAL_WALL_TEMP = ABSPERRBARKE_SHADER_MATERIAL.clone()
MATERIAL_WALL_TEMP.uniforms.uAlpha.value = 1
MATERIAL_WALL_TEMP.uniforms.uColor.value = new Color(0xFFCF2E)
MATERIAL_WALL_TEMP.uniforms.uBackgroundColor.value = new Color(0x000000)
MATERIAL_WALL_TEMP.side = FrontSide
MATERIAL_WALL_TEMP.depthTest = false
MATERIAL_WALL_TEMP.transparent = true

// --- WALL SIZE ADJUSTMENT ---
const MATERIAL_WALL_SIZE_ADJUSTMENT = ABSPERRBARKE_SHADER_MATERIAL.clone()
MATERIAL_WALL_SIZE_ADJUSTMENT.uniforms.uStripeSpacing.value = 0.5
MATERIAL_WALL_SIZE_ADJUSTMENT.uniforms.uAlpha.value = 0.12
MATERIAL_WALL_SIZE_ADJUSTMENT.uniforms.uColor.value = new Color(0x000000)
MATERIAL_WALL_SIZE_ADJUSTMENT.uniforms.uBackgroundColor.value = new Color(0xffffff)
MATERIAL_WALL_SIZE_ADJUSTMENT.side = FrontSide
MATERIAL_WALL_SIZE_ADJUSTMENT.depthTest = false
MATERIAL_WALL_SIZE_ADJUSTMENT.transparent = true

// --- WALL SNAP POINT ---
const MATERIAL_PARAM_WALL_SNAP_POINT: MeshBasicMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    transparent: true,
    opacity: 0.90
}
const MATERIAL_WALL_SNAP_POINT_DEFAULT = new MeshBasicMaterial({
    ...MATERIAL_PARAM_WALL_SNAP_POINT,
    color: 0x43E77F,
})
const MATERIAL_WALL_SNAP_POINT_FIXED = new MeshBasicMaterial({
    ...MATERIAL_PARAM_WALL_SNAP_POINT,
    color: 0xA600A6,
})
const MATERIAL_WALL_SNAP_POINT_NO_CONNECTION = new MeshBasicMaterial({
    ...MATERIAL_PARAM_WALL_SNAP_POINT,
    color: 0xFF0000,
})
// --- WALL SNAP LINE ---
const MATERIAL_PARAM_WALL_SNAP_LINE: LineMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    worldUnits: true,
    linewidth: 0.02,
    transparent: true,
    opacity: 1,
    dashed: true,
    dashScale: 10,
    dashSize: 0.5,
    gapSize: 0.5,
}
const MATERIAL_WALL_SNAP_LINE_DEFAULT = new LineMaterial({
    ...MATERIAL_PARAM_WALL_SNAP_LINE,
    color: 0xB46251,
})
// const MATERIAL_WALL_SNAP_LINE_FIXED = new LineMaterial({
//     ...MATERIAL_PARAM_WALL_SNAP_LINE,
//     color: 0x952B2B,
// })
// const MATERIAL_WALL_SNAP_LINE_DRAGGED = new LineMaterial({
//     ...MATERIAL_PARAM_WALL_SNAP_LINE,
//     color: 0xFF0000,
// })
//--- METRICS ---
const MATERIAL_PARAM_METRICS_LINE: LineMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    worldUnits: true,
    linewidth: 0.02,
    transparent: true,
    opacity: 1,
}
const MATERIAL_METRICS_LINE = new LineMaterial({
    ...MATERIAL_PARAM_METRICS_LINE,
    color: 0xFF0000,
})
const MATERIAL_METRICS_LINE_NOT_FOCUSED = new LineMaterial({
    ...MATERIAL_PARAM_METRICS_LINE,
    color: 0xFFA56B,
})
const MATERIAL_PARAM_METRICS_TEXT_BACKGROUND: MeshBasicMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    transparent: true,
}
const MATERIAL_METRICS_TEXT_BACKGROUND = new MeshBasicMaterial({
    ...MATERIAL_PARAM_METRICS_TEXT_BACKGROUND,
    color: 0xFF0000,
})
const MATERIAL_METRICS_TEXT_BACKGROUND_NOT_FOCUSED = new MeshBasicMaterial({
    ...MATERIAL_PARAM_METRICS_TEXT_BACKGROUND,
    color: 0xFFA56B,
})
const MATERIAL_PARAM_METRICS_TEXT: MeshBasicMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    transparent: true,
}
const MATERIAL_METRICS_TEXT = new MeshBasicMaterial({
    ...MATERIAL_PARAM_METRICS_TEXT,
    color: 0xFFFFFF,
})
const MATERIAL_METRICS_TEXT_NOT_FOCUSED = new MeshBasicMaterial({
    ...MATERIAL_PARAM_METRICS_TEXT,
    color: 0x000000,
})
const MATERIAL_PARAM_METRICS_TEXT_DISPLAY_ID_BACKGROUND: MeshBasicMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    transparent: true,
}
const MATERIAL_METRICS_TEXT_DISPLAY_ID_BACKGROUND = new MeshBasicMaterial({
    ...MATERIAL_PARAM_METRICS_TEXT_DISPLAY_ID_BACKGROUND,
    color: 0x005D3A,
})
const MATERIAL_METRICS_TEXT_DISPLAY_ID_BACKGROUND_NOT_FOCUSED = new MeshBasicMaterial({
    ...MATERIAL_PARAM_METRICS_TEXT_DISPLAY_ID_BACKGROUND,
    color: 0x43E77F,
})
const MATERIAL_PARAM_METRICS_TEXT_DISPLAY_ID: MeshBasicMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    transparent: true,
}
const MATERIAL_METRICS_TEXT_DISPLAY_ID = new MeshBasicMaterial({
    ...MATERIAL_PARAM_METRICS_TEXT_DISPLAY_ID,
    color: 0xFFFFFF,
})
const MATERIAL_METRICS_TEXT_DISPLAY_ID_NOT_FOCUSED = new MeshBasicMaterial({
    ...MATERIAL_PARAM_METRICS_TEXT_DISPLAY_ID,
    color: 0x000000,
})
const MATERIAL_METRICS_TEXT_THICKNESS_BACKGROUND = new MeshBasicMaterial({
    color: 0xFFFFFF,
    side: FrontSide,
    depthTest: false,
    transparent: true,
})
const MATERIAL_METRICS_TEXT_THICKNESS = new MeshBasicMaterial({
    color: 0x000000,
    side: FrontSide,
    depthTest: false,
    transparent: true,
})

// --- FURNITURE ---
const MATERIAL_FURNITURE = new MeshBasicMaterial({
    color: 0x0095D0,
    side: FrontSide,
    depthTest: false,
    transparent: true,
})

// --- SELECTION ---
const MATERIAL_SELECTION = new MeshBasicMaterial({
    color: 0xFFCF2E,
    side: FrontSide,
    depthTest: false,
    transparent: true,
})

// --- HOVER ---
const MATERIAL_HOVER = new MeshBasicMaterial({
    color: 0xFFFFFF,
    side: FrontSide,
    depthTest: false,
    transparent: true,
})

// --- SELECTION + HOVER ---
const MATERIAL_SELECTION_AND_HOVER = new MeshBasicMaterial({
    color: 0xF9DE74,
    side: FrontSide,
    depthTest: false,
    transparent: true,
})

// --- GRID ---
const MATERIAL_GRID = new LineBasicMaterial({
    color: 0x000000,
    side: FrontSide,
    depthTest: false,
    transparent: true,
    opacity: 0.05,
})

// --- FLOOR GRID ---
const MATERIAL_FLOOR_GRID = new LineBasicMaterial({
    color: 0x000000,
    side: FrontSide,
    depthTest: false,
    transparent: true,
    opacity: 0.25,
})

//--- SNAPPING CROSSHAIR ---
const MATERIAL_SNAPPING_CROSSHAIR = new LineMaterial({
    color: 0xFF0000,
    side: FrontSide,
    depthTest: false,
    worldUnits: true,
    linewidth: 0.02,
    transparent: true,
    opacity: 1,
})

// #######################
// ### RENDERER CONFIG ###
// #######################
export class BuildingRenderer2D extends BuildingRenderer {
    showAmbientLight = false
    showRectAreaWorldLights = false
    showSky = false
    debugScene = false
    showStats = false
    debugHoles = false
    debugSnapping = false
    debugUnrecognizedRooms = false

    /**
     * @param listingId
     * @param building Muss mutable und voll reaktiv sein.
     * @param canEdit
     * @param previousHistoryManager
     */
    constructor(
        listingId: string,
        building: Readonly<Ref<Building>>,
        canEdit: boolean,
        previousHistoryManager: Optional<HistoryManager<Building>>,
    ) {
        super(
            "2D",
            listingId,
            building,
            canEdit,
            "2D",
            LinearToneMapping,
            [],
            0.11, //in meters (11cm), sollte kleiner als der Default-Wert von Apple sein (11,81cm)
            previousHistoryManager,
            "WEBGL"
        );

        this.occlusionCullingRaycaster.destroy()
        this.pointOfInterestCreator?.destroy()
        this.roofAreaCreator?.destroy()
    }

    destroy() {
        super.destroy();

        for (const effect of this.effects) {
            effect.destroy();
        }
    }

    calculateIsCameraFitIgnored(component: BuildingComponent): boolean {
        return component.type !== "BUILDING"
    }

    isSelectable(component: BuildingComponent & TSelectionRelated<TSelectionRaycasterEmitterOrConsumer<RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint>>): boolean {
        const mode = this.mode.value;
        if (mode === "OPENING_CREATION") {
            return false
        }
        if (typeof mode !== 'string' && mode.type === "RELOCATE_OPENING") {
            return false
        }

        const isWallCreationModeEnabled = mode === "WALL_CREATION"
        const isDragAndDropModeEnabled = mode === "DRAG_AND_DROP"

        if (component.type === "FURNITURE") {
            return this.showFurniture.value && !isWallCreationModeEnabled && !isDragAndDropModeEnabled
        }
        if (component.type === "UNRECOGNIZED_ROOM") {
            return false
        }
        if (component.type === "POINT_OF_INTEREST") {
            return this.showPointsOfInterest.value && !isWallCreationModeEnabled && !isDragAndDropModeEnabled
        }
        if (component.type === "WALL_OPENING_DOOR" || component.type === "WALL_OPENING_WINDOW" || component.type === "WALL_OPENING_OPENING") {
            return !this.isEvebiModeEnabled.value && !isDragAndDropModeEnabled && !isWallCreationModeEnabled
        }
        return !isDragAndDropModeEnabled && !isWallCreationModeEnabled
    }

    isOcclusionCullable(component: BuildingComponent & TOcclusionCullingRelated<TOcclusionCullingRaycasterEmitterOrConsumer<RaycastableBuildingComponent<TraversalBuildingComponent>>>): boolean {
        return false;
    }

    isHoverable(component: BuildingComponent & THoverRelated<THoverRaycasterEmitterOrConsumer<RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint>>): boolean {
        const mode = this.mode.value;
        const isOpeningCreationMode = mode === "OPENING_CREATION"
        const isRelocateOpeningMode = typeof mode !== 'string' && mode.type === "RELOCATE_OPENING"
        const isAnyOpeningMode = isOpeningCreationMode || isRelocateOpeningMode

        if (isAnyOpeningMode && component.type !== "WALL_WITHOUT_HOLES") {
            return false
        }
        if (component.type === "UNRECOGNIZED_ROOM") {
            return false
        }
        if (mode === "WALL_CREATION") {
            return false
        }

        const isDragAndDropModeEnabled = mode === "DRAG_AND_DROP"

        switch (component.type) {
            case "WALL_SNAP_POINT":
                return isDragAndDropModeEnabled && !component.snapPoint.isFixed
            case "WALL_WITHOUT_HOLES":
                if (isRelocateOpeningMode) {
                    return (mode as OpeningRelocationMode).wall.id === component.component.value.id
                }
                return isDragAndDropModeEnabled || (isOpeningCreationMode && component.component.value.shapeRepresentation.shape.__typename !== "Ring") //TODO: WallCreation für Ring-Wände ermöglichen
            case "FURNITURE":
                return this.showFurniture.value && !isDragAndDropModeEnabled
            case "POINT_OF_INTEREST":
                return this.showPointsOfInterest.value && !isDragAndDropModeEnabled
            case "WALL_OPENING_DOOR":
            case "WALL_OPENING_WINDOW":
            case "WALL_OPENING_OPENING":
                return !this.isEvebiModeEnabled.value && !isDragAndDropModeEnabled
            default:
                return !isDragAndDropModeEnabled
        }
    }

    calculateDebug(component: BuildingComponent): boolean {
        return false
    }

    calculateHasBillboardEffect(component: BuildingComponent): boolean {
        switch (component.type) {
            case "ROOM_TEXT":
            case "ROOM_TEXT_BACKGROUND":
            case "WALL_SNAP_POINT":
            case "WALL_METRICS_TEXT_WIDTH":
            case "WALL_METRICS_TEXT_WIDTH_BACKGROUND":
            case "WALL_OPENING_METRICS_TEXT":
            case "WALL_OPENING_METRICS_TEXT_BACKGROUND":
            case "WALL_METRICS_TEXT_DISPLAY_ID":
            case "WALL_METRICS_TEXT_DISPLAY_ID_BACKGROUND":
            case "WALL_METRICS_TEXT_THICKNESS":
            case "WALL_METRICS_TEXT_THICKNESS_BACKGROUND":
            case "POINT_OF_INTEREST":
                return true
            default:
                return false
        }
    }

    // noinspection OverlyComplexFunctionJS,FunctionTooLongJS
    calculateVisibility(component: BuildingComponent): RenderVisibility {
        //BUILDING
        if (component.type === "BUILDING") {
            return "SELF_HIDDEN_CHILDREN_VISIBLE"
        }

        //ROOF AREAS
        if (component.type === "ROOF_AREA") {
            return "ALWAYS_HIDDEN"
        }

        //FLOORS
        if (component.type === "FLOOR") {
            if (this.semiVisibleFloorLevels.value.has(component.component.value.level) && !this.visibleFloorLevels.value.has(component.component.value.level)) {
                return "SELF_HIDDEN_CHILDREN_VISIBLE"
            }
            return this.visibleFloorLevels.value.has(component.component.value.level) ? "SELF_HIDDEN_CHILDREN_VISIBLE" : "HIDDEN"
        }

        const floor = this.traversableBuilding.floorOf(component.component.value)
        if (floor !== null && this.semiVisibleFloorLevels.value.has(floor.level) && !this.visibleFloorLevels.value.has(floor.level)) {
            if (component.type === "WALL_WITHOUT_HOLES") {
                return "VISIBLE"
            }
            return "HIDDEN"
        }

        if (component.type === "FLOOR_SLAB_CEILING") {
            return "ALWAYS_HIDDEN"
        }
        if (component.type === "FLOOR_SLAB_FLOOR") {
            return "VISIBLE"
        }

        //ROOMS
        if (component.type === "ROOM") {
            return this.isComponentHovered(component) || this.isComponentSelected(component) ? "VISIBLE" : "SELF_HIDDEN_CHILDREN_VISIBLE"
        }
        if (component.type === "UNRECOGNIZED_ROOM") {
            return "ALWAYS_HIDDEN"
        }
        if (component.type === "ROOM_SLAB_CEILING") {
            return "ALWAYS_HIDDEN"
        }
        if (component.type === "ROOM_SLAB_CEILING_SEGMENT") {
            return "ALWAYS_HIDDEN"
        }
        if (component.type === "ROOM_SLAB_FLOOR") {
            return "VISIBLE"
        }
        if (component.type === "ROOM_SLAB_FLOOR_SEGMENT") {
            return "ALWAYS_HIDDEN"
        }

        //OPENING + DOOR
        const mode = this.mode.value;
        if (component.type === "WALL_OPENING_OPENING") {
            if (mode === "DRAG_AND_DROP" || mode === "WALL_CREATION") {
                return "VISIBLE"
            }
            return this.isComponentHovered(component) || this.isComponentSelected(component) ? "VISIBLE" : "HIDDEN"
        }

        // FLOOR GRID
        if (component.type === "FLOOR_GRID") {
            return this.showFloorGrid.value && this.isComponentSelected(component.floorComponent) ? "VISIBLE" : "HIDDEN"
        }

        //POINT OF INTEREST
        if (component.type === "POINT_OF_INTEREST") {
            if (mode === "WALL_CREATION" || mode === "DRAG_AND_DROP") {
                return "HIDDEN"
            }
            return this.showPointsOfInterest.value ? "VISIBLE" : "HIDDEN"
        }

        //POINT OF INTEREST CAMERA
        if (component.type === "POINT_OF_INTEREST_CAMERA") {
            return "ALWAYS_HIDDEN" //TODO temporary hidden
            // if (mode === "WALL_CREATION" || mode === "DRAG_AND_DROP") {
            //     return "HIDDEN"
            // }
            // if (!this.showPointsOfInterest.value) {
            //     return "HIDDEN"
            // }
            // return this.isComponentHovered(component.pointOfInterestComponent) || this.isComponentSelected(component.pointOfInterestComponent) ? "VISIBLE" : "HIDDEN"
        }

        //FURNITURE
        if (component.type === "FURNITURE") {
            if (mode === "WALL_CREATION" || mode === "DRAG_AND_DROP") {
                return "HIDDEN"
            }
            return this.showFurniture.value ? "VISIBLE" : "HIDDEN"
        }

        //WALL
        if (component.type === "WALL_WITHOUT_HOLES") {
            //TEMP WALL
            if (component.component.value.id === WallCreator.TEMP_WALL_ID) {
                if (mode !== "WALL_CREATION") {
                    return "HIDDEN"
                }
                const wallWidth = widthOfShape(component.component.value.shapeRepresentation.shape);
                return isNumberGreaterThan(wallWidth, BUILDING_WALL_MIN_WIDTH, BUILDING_EPSILON) ? "VISIBLE" : "HIDDEN"
            }
            if (this.isEvebiModeEnabled.value) {
                return "HIDDEN"
            }
            //OTHERS
            return mode === "DRAG_AND_DROP" || this.isRoomOfWallHoveredOrSelected(component) ? "VISIBLE" : "HIDDEN"
        }
        if (component.type === "WALL_WITH_HOLES") {
            if (this.isRoomOfWallHoveredOrSelected(component)) {
                return "HIDDEN"
            }
            if (this.isEvebiModeEnabled.value) {
                return "HIDDEN"
            }
            if (mode === "DRAG_AND_DROP") {
                return "SELF_HIDDEN_CHILDREN_VISIBLE"
            }
            if (mode === "WALL_CREATION" || mode === "OPENING_CREATION" || (typeof mode !== 'string' && mode.type === "RELOCATE_OPENING")) {
                return "VISIBLE"
            }
            // noinspection NonBlockStatementBodyJS
            if (!this.visibleWallTypes.value.has(wallTypeOfWall(component.component.value))) {
                return "HIDDEN"
            }
            return "VISIBLE"
        }
        if (component.type === "WALL_SNAP_POINT") {
            //snap points werden der scene (root) hinzugefügt und können nicht über den floor ausgeblendet werden
            if (!(
                (mode === "DRAG_AND_DROP" || mode === "WALL_CREATION")
                && component.component.value.id !== WallCreator.TEMP_WALL_ID
                && this.isComponentVisibleRelatedToFloor(component)
            )) {
                return "HIDDEN"
            }
            if (component.snapPoint.isFixed) {
                return "VISIBLE"
            }
            if (this.isComponentHovered(component) || this.isComponentSelected(component) || this.isComponentDragged(component)) {
                return "VISIBLE"
            }
            if (!this.snappingManager.isGroupingEnabled.value) {
                return "VISIBLE"
            }

            // eslint-disable-next-line @typescript-eslint/no-unused-expressions
            this.snappingManager.refreshCounter.value //trigger reactivity
            const snapPointsOfSameGroup = this.snappingManager.snapPointIdToSnapPointsOfSameGroup(component.wallWithHolesComponent.component.value, component.snapPoint.id, true)
            if (snapPointsOfSameGroup.length <= 0) {
                return "VISIBLE"
            }

            const firstNonFixedSnapPointOrFirst = snapPointsOfSameGroup.find(snapPoint => !snapPoint.isFixed) ?? snapPointsOfSameGroup[0]

            for (const snapPoint of snapPointsOfSameGroup) {
                if (firstNonFixedSnapPointOrFirst.id === snapPoint.id) {
                    continue
                }
                const snapPointComponent = this.wallSnapPointComponentOfSnapPointId(snapPoint.id)
                if (snapPointComponent === null) {
                    continue
                }
                if (this.isComponentHovered(snapPointComponent) || this.isComponentSelected(snapPointComponent) || this.isComponentDragged(snapPointComponent)) {
                    return "HIDDEN"
                }
            }
            return firstNonFixedSnapPointOrFirst.id === component.snapPoint.id ? "VISIBLE" : "HIDDEN"
        }

        //ROOM TEXT
        if (component.type === "ROOM_TEXT") {
            return this.showRoomTexts.value ? "VISIBLE" : "HIDDEN"
        }
        if (component.type === "ROOM_TEXT_BACKGROUND") {
            return this.visibility(component.textComponent) //recursion
        }

        //GRID
        if (component.type === "GRID") {
            return mode === "DRAG_AND_DROP" || mode === "WALL_CREATION" ? "VISIBLE" : "HIDDEN"
        }

        //CROSSHAIR
        if (component.type === "SNAPPING_CROSSHAIR") {
            return mode === "WALL_CREATION"/* || this.draggableRaycaster?.dragEmitterRaycasterObjects.value?.type === 'WALL_SNAP_POINT'*/ ? "VISIBLE" : "HIDDEN"
        }

        //WALL METRICS
        if (component.type === "WALL_METRICS_TEXT_WIDTH" || component.type === "WALL_METRICS_TEXT_DISPLAY_ID" || component.type === "WALL_METRICS_TEXT_THICKNESS") {
            if (mode === "OPENING_CREATION" || (typeof mode !== 'string' && mode.type === "RELOCATE_OPENING")) {
                return "HIDDEN"
            }
            const text = component.customShapeRepresentation.text.value
            if (text === null || text.trim() === "") {
                return "HIDDEN"
            }
            if (component.component.value.id === WallCreator.TEMP_WALL_ID) {
                return this.visibility(component.wallWithoutHolesComponent) //recursion
            }
            if (component.type === "WALL_METRICS_TEXT_DISPLAY_ID") {
                if (this.showDisplayIds.value) {
                    return "VISIBLE"
                }
                if (this.isEvebiModeEnabled.value && this.displayIdSizeAdjustmentXEffectedWallIds.value.has(component.wallWithHolesComponent.component.value.id)) {
                    return "VISIBLE"
                }
            }
            if (component.type === "WALL_METRICS_TEXT_THICKNESS") {
                if (this.showWallThicknesses.value) {
                    return "VISIBLE"
                }
                if (this.isEvebiModeEnabled.value && this.displayIdSizeAdjustmentXEffectedWallIds.value.has(component.wallWithHolesComponent.component.value.id)) {
                    return "VISIBLE"
                }
            }
            if (component.type === "WALL_METRICS_TEXT_WIDTH" && this.showWallWidths.value) {
                return "VISIBLE"
            }
            if (this.isRoomOfWallHoveredOrSelected(component.wallWithHolesComponent)) {
                return "VISIBLE"
            }
            return this.isEffectedByAnyWallSnapPoint(component.wallWithHolesComponent, snapPointComponent =>
                this.isComponentHovered(snapPointComponent)
                || this.isComponentSelected(snapPointComponent)
                || this.isComponentDragged(snapPointComponent)
            ) ? "VISIBLE" : "HIDDEN"
        }
        if (component.type === "WALL_METRICS_LINE" || component.type === "WALL_METRICS_TEXT_WIDTH_BACKGROUND" || component.type === "WALL_METRICS_TEXT_DISPLAY_ID_BACKGROUND" || component.type === "WALL_METRICS_TEXT_THICKNESS_BACKGROUND") {
            return this.visibility(component.textComponent) //recursion
        }

        //WALL OPENING METRICS
        if (component.type === "WALL_OPENING_METRICS_TEXT") {
            const text = component.customShapeRepresentation.text.value
            if (text === null || text.trim() === "") {
                return "HIDDEN"
            }
            if (typeof mode !== 'string' && mode.type === "RELOCATE_OPENING") {
                return mode.opening.id === component.component.value.id ? "VISIBLE" : "HIDDEN"
            }
            if (mode === "OPENING_CREATION") {
                return component.component.value.id === WallOpeningCreator.TEMP_OPENING_ID ? "VISIBLE" : "HIDDEN"
            }
            return this.isComponentHovered(component.openingComponent) || this.isComponentSelected(component.openingComponent) ? "VISIBLE" : "HIDDEN"
        }
        if (component.type === "WALL_OPENING_METRICS_LINE" || component.type === "WALL_OPENING_METRICS_TEXT_BACKGROUND") {
            return this.visibility(component.textComponent) //recursion
        }

        //WALL SNAP LINE
        if (component.type === "WALL_SNAP_LINE") {
            return this.isWallSnapLineVisible(component) ? "VISIBLE" : "HIDDEN"
        }

        return "VISIBLE"
    }

    // noinspection OverlyComplexFunctionJS,FunctionTooLongJS
    calculateMaterialOf(component: TMaybeRaycasterRelated<TraversalBuildingComponent, BuildingComponent>): TMaterialDeclaration<TMaterial> {
        const isSelected = this.isComponentSelected(component) || (component.type === "WALL_SIZE_ADJUSTMENT" && this.isComponentSelected(component.wallComponent))
        const isHovered = this.isComponentHovered(component) || (component.type === "WALL_SIZE_ADJUSTMENT" && this.isComponentHovered(component.wallComponent))

        if (component.type === "WALL_SNAP_POINT" && this.isComponentDragged(component)) {
            return this.snapPointMaterial(component)
        }

        if (isSelected && isHovered) {
            return MATERIAL_SELECTION_AND_HOVER
        }
        if (isSelected) {
            return MATERIAL_SELECTION
        }
        if (isHovered) {
            return MATERIAL_HOVER
        }

        switch (component.type) {
            case "BUILDING":
                return MATERIAL_BUILDING
            case "FLOOR":
                return MATERIAL_FLOOR
            case "FLOOR_SLAB_CEILING":
                return MATERIAL_FLOOR_SLAB_CEILING
            case "FLOOR_SLAB_FLOOR":
                return this.floorSlabMaterial(component)
            case "FURNITURE":
                return MATERIAL_FURNITURE
            case "ROOM":
                return MATERIAL_ROOM
            case "UNRECOGNIZED_ROOM":
                return MATERIAL_ROOM_UNRECOGNIZED
            case "ROOM_SLAB_CEILING":
                return MATERIAL_ROOM_SLAB_CEILING
            case "ROOM_SLAB_CEILING_SEGMENT":
                return MATERIAL_ROOM_SLAB_CEILING_SEGMENT
            case "ROOM_SLAB_FLOOR":
                return this.roomSlabFloorMaterial(component)
            case "ROOM_SLAB_FLOOR_SEGMENT":
                return MATERIAL_ROOM_SLAB_FLOOR_SEGMENT
            case "ROOM_TEXT_BACKGROUND":
                return MATERIAL_ROOM_TEXT_BACKGROUND
            case "ROOM_TEXT":
                return MATERIAL_ROOM_TEXT
            case "WALL_OPENING_DOOR":
                return this.mode.value === "DRAG_AND_DROP" || this.mode.value === "WALL_CREATION" ? MATERIAL_DOOR_DRAG_AND_DROP : MATERIAL_DOOR_DEFAULT
            case "WALL_OPENING_DOOR_OUTLINE":
                return this.doorOutlineMaterial(component)
            case "WALL_OPENING_DOOR_DOORWAY_ARC":
                return MATERIAL_DOORWAY_ARC
            case "WALL_OPENING_DOOR_DOORWAY_LINE":
                return MATERIAL_DOORWAY_LINE
            case "WALL_OPENING_WINDOW":
                return MATERIAL_WINDOW
            case "WALL_WITH_HOLES":
                return this.wallMaterial(component)
            case "WALL_WITHOUT_HOLES":
                return this.wallMaterial(component)
            case "WALL_SNAP_POINT":
                return this.snapPointMaterial(component)
            case "WALL_OPENING_OPENING":
                return MATERIAL_OPENING
            case "WALL_SIZE_ADJUSTMENT":
                return this.wallMaterial(component)
            case "GRID":
                return MATERIAL_GRID
            case "FLOOR_GRID":
                return MATERIAL_FLOOR_GRID
            case "SNAPPING_CROSSHAIR":
                return MATERIAL_SNAPPING_CROSSHAIR
            case "WALL_METRICS_LINE":
                return this.isEffectedByAnyWallSnapPoint(component.textComponent.wallWithHolesComponent, snapPointComponent =>
                        snapPointComponent.component.value.id !== component.component.value.id && (
                            this.isComponentHovered(snapPointComponent)
                            || this.isComponentSelected(snapPointComponent)
                            || this.isComponentDragged(snapPointComponent)
                        )
                )
                    ? MATERIAL_METRICS_LINE_NOT_FOCUSED
                    : MATERIAL_METRICS_LINE
            case "WALL_OPENING_METRICS_LINE":
                return MATERIAL_METRICS_LINE
            case "WALL_METRICS_TEXT_WIDTH_BACKGROUND":
                return this.isEffectedByAnyWallSnapPoint(component.textComponent.wallWithHolesComponent, snapPointComponent =>
                        snapPointComponent.component.value.id !== component.component.value.id && (
                            this.isComponentHovered(snapPointComponent)
                            || this.isComponentSelected(snapPointComponent)
                            || this.isComponentDragged(snapPointComponent)
                        )
                )
                    ? MATERIAL_METRICS_TEXT_BACKGROUND_NOT_FOCUSED
                    : MATERIAL_METRICS_TEXT_BACKGROUND
            case "WALL_OPENING_METRICS_TEXT_BACKGROUND":
                return MATERIAL_METRICS_TEXT_BACKGROUND
            case "WALL_METRICS_TEXT_WIDTH":
                return this.isEffectedByAnyWallSnapPoint(component.wallWithHolesComponent, snapPointComponent =>
                        snapPointComponent.component.value.id !== component.component.value.id && (
                            this.isComponentHovered(snapPointComponent)
                            || this.isComponentSelected(snapPointComponent)
                            || this.isComponentDragged(snapPointComponent)
                        )
                )
                    ? MATERIAL_METRICS_TEXT_NOT_FOCUSED
                    : MATERIAL_METRICS_TEXT
            case "WALL_OPENING_METRICS_TEXT":
                return MATERIAL_METRICS_TEXT
            case "WALL_METRICS_TEXT_DISPLAY_ID":
                return this.isWallDisplayIdEffectedByOtherHoveredOrSelectedWall(component.wallWithHolesComponent)
                    ? MATERIAL_METRICS_TEXT_DISPLAY_ID_NOT_FOCUSED
                    : MATERIAL_METRICS_TEXT_DISPLAY_ID
            case "WALL_METRICS_TEXT_DISPLAY_ID_BACKGROUND":
                return this.isWallDisplayIdEffectedByOtherHoveredOrSelectedWall(component.textComponent.wallWithHolesComponent)
                    ? MATERIAL_METRICS_TEXT_DISPLAY_ID_BACKGROUND_NOT_FOCUSED
                    : MATERIAL_METRICS_TEXT_DISPLAY_ID_BACKGROUND
            case "WALL_METRICS_TEXT_THICKNESS":
                return MATERIAL_METRICS_TEXT_THICKNESS
            case "WALL_METRICS_TEXT_THICKNESS_BACKGROUND":
                return MATERIAL_METRICS_TEXT_THICKNESS_BACKGROUND
            case "WALL_SNAP_LINE":
                return MATERIAL_WALL_SNAP_LINE_DEFAULT
            case "POINT_OF_INTEREST":
                return MATERIAL_POINT_OF_INTEREST
            case "POINT_OF_INTEREST_CAMERA":
                return MATERIAL_POINT_OF_INTEREST_CAMERA
            case "WALL_SIZE_ADJUSTMENT_OVERLAY":
                return MATERIAL_WALL_SIZE_ADJUSTMENT
            default:
                return MATERIAL_DEBUG
        }
    }

    protected calculateZFightingOffsetY(component: BuildingComponent): Optional<number> {
        return null
    }

    // noinspection OverlyComplexFunctionJS
    protected calculateZFightingOffsetZ(component: BuildingComponent): Optional<number> {
        switch (component.type) {
            case "WALL_WITH_HOLES":
            case "WALL_WITHOUT_HOLES":
                return noThicknessWallOffsetZ(this, component.component.value)
            case "WALL_OPENING_DOOR":
            case "WALL_OPENING_WINDOW":
            case "WALL_OPENING_OPENING":
                return noThicknessOpeningOffsetZ(this, component.component.value)
            default:
                return null
        }
    }

    protected calculateZFightingScale(component: BuildingComponent): Optional<Vector3> {
        return null
    }

    protected initializeRaycasters(edit: boolean): TRaycaster[] {
        if (edit) {
            return [
                this.selectionRaycaster,
                this.hoverRaycaster,
                this.draggableRaycaster!,
                this.trackingRaycaster!,
            ]
        }
        return [
            this.selectionRaycaster,
            this.hoverRaycaster,
        ]
    }

    protected calculateCanBeDeleted(component: RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint): boolean {
        switch (component.type) {
            case "FLOOR":
            case "WALL_WITH_HOLES":
            case "FURNITURE":
            case "WALL_OPENING_OPENING":
            case "WALL_OPENING_DOOR":
            case "WALL_OPENING_WINDOW":
            case "POINT_OF_INTEREST":
                return true
            default:
                return false
        }
    }

    protected isDraggable(component: BuildingComponent & TDraggableRelated<TDraggableRaycasterEmitterOrConsumer<RaycastableBuildingComponent<TraversalBuildingComponent>>>): boolean {
        if (this.mode.value !== "DRAG_AND_DROP") {
            return false
        }
        switch (component.type) {
            case "WALL_SNAP_POINT":
                return !component.snapPoint.isFixed
            case "WALL_WITHOUT_HOLES":
                return true
            case "WALL_WITH_HOLES":
                return true
            default:
                return false
        }
    }

    protected calculateShowSelectionDetails(selection: readonly (RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint)[]): boolean {
        if (this.mode.value !== "DEFAULT") {
            return false
        }
        return !selection.some(c => c.type === "WALL_SNAP_POINT")
    }

    // noinspection OverlyComplexFunctionJS,FunctionTooLongJS
    protected calculateRenderOrder(component: BuildingComponent): Optional<number> {
        if (component.type === "SNAPPING_CROSSHAIR") {
            return 1012
        }
        if (component.type === "WALL_OPENING_METRICS_TEXT") {
            return 1011
        }
        if (component.type === "WALL_OPENING_METRICS_TEXT_BACKGROUND") {
            return 1010
        }
        if (component.type === "WALL_OPENING_METRICS_LINE") {
            return 1009
        }
        if (component.type === "WALL_METRICS_TEXT_DISPLAY_ID") {
            return 1008
        }
        if (component.type === "WALL_METRICS_TEXT_DISPLAY_ID_BACKGROUND") {
            return 1007
        }
        if (component.type === "WALL_METRICS_TEXT_THICKNESS") {
            return 1006
        }
        if (component.type === "WALL_METRICS_TEXT_THICKNESS_BACKGROUND") {
            return 1005
        }
        if (component.type === "WALL_METRICS_TEXT_WIDTH") {
            return 1004
        }
        if (component.type === "WALL_METRICS_TEXT_WIDTH_BACKGROUND") {
            return 1003
        }
        if (component.type === "WALL_METRICS_LINE") {
            return 1002
        }
        if (component.type === "GRID") {
            return 1001
        }
        if (component.component.value.id === WallCreator.TEMP_WALL_ID) {
            return 1000
        }

        const floor = this.traversableBuilding.floorOf(component.component.value)
        if (floor != null && this.semiVisibleFloorLevels.value.has(floor.level) && !this.visibleFloorLevels.value.has(floor.level) && component.type === "WALL_WITHOUT_HOLES") {
            return 999
        }

        const isSelected = this.isComponentSelected(component) || (component.type === "WALL_SIZE_ADJUSTMENT" && this.isComponentSelected(component.wallComponent))
        const isHovered = this.isComponentHovered(component) || (component.type === "WALL_SIZE_ADJUSTMENT" && this.isComponentHovered(component.wallComponent))

        if (component.type === "WALL_SNAP_POINT") {
            if (isHovered || isSelected) {
                return 103
            }
            if (!component.snapPoint.isFixed) {
                return 102
            }
            return 101
        }

        if ((isHovered || isSelected) && component.type !== "FLOOR_SLAB_FLOOR" && component.type !== "ROOM" && component.type !== "ROOM_SLAB_FLOOR") {
            return 100
        }

        switch (component.type) {
            case "ROOM_TEXT":
                return 17

            case "ROOM_TEXT_BACKGROUND":
                return 16

            case "POINT_OF_INTEREST_CAMERA":
                return 15

            case "POINT_OF_INTEREST":
                return 14

            case "WALL_SIZE_ADJUSTMENT_OVERLAY":
                return 13

            case "WALL_SIZE_ADJUSTMENT":
                return 12

            case "WALL_OPENING_DOOR_OUTLINE":
                return 11

            case "WALL_OPENING_DOOR":
                return 10

            case "WALL_OPENING_WINDOW":
                return 9

            case "WALL_OPENING_OPENING":
                return 8

            case "WALL_WITH_HOLES":
            case "WALL_WITHOUT_HOLES":
                return 7

            case "WALL_OPENING_DOOR_DOORWAY_ARC":
            case "WALL_OPENING_DOOR_DOORWAY_LINE":
                return 6

            case "FURNITURE":
                return 5

            case "ROOM":
                return 4

            case "UNRECOGNIZED_ROOM":
                return 3

            case "ROOM_SLAB_FLOOR":
                return 2

            case "FLOOR_SLAB_FLOOR":
                return 1

            default:
                return 9999
        }
    }

    private isWallSnapLineVisible(component: WallSnapLineBuildingComponent): boolean {
        if (!this.isComponentVisibleRelatedToFloor(component)) {
            return false
        }

        if (this.mode.value === "WALL_CREATION") {
            const crosshairResult = this.wallCreator!.snappingResult.value
            if (crosshairResult !== null) {
                return this.matchSnappingResultWallSnappingLineComponent(crosshairResult, component)
            }
        }

        if (this.draggableRaycaster === null) {
            return false
        }

        for (const draggedObject of this.draggableRaycaster.draggedObjects.value) {
            if (draggedObject.type !== "WALL_SNAP_POINT") {
                continue
            }
            const snappingResult = draggedObject.snappingResult.value
            if (snappingResult === null) {
                continue
            }
            if (this.matchSnappingResultWallSnappingLineComponent(snappingResult, component)) {
                return true
            }
        }

        return false
    }

    private snapPointMaterial(component: WallSnapPointBuildingComponent): TMaterialDeclaration<TMaterial> {
        if (component.snapPoint.isFixed) {
            return MATERIAL_WALL_SNAP_POINT_FIXED
        }
        this.snappingManager.refreshCounter.value //trigger reactivity
        return this.snappingManager.snapPointIdToSnapPointsOfSameGroup(component.component.value, component.snapPoint.id, true).length < 2 ? MATERIAL_WALL_SNAP_POINT_NO_CONNECTION : MATERIAL_WALL_SNAP_POINT_DEFAULT
    }

    private doorOutlineMaterial(component: WallOpeningDoorOutlineBuildingComponent): TMaterialDeclaration<TMaterial> {
        const floor = this.traversableBuilding.floorOf(component.component.value)
        return floor != null && floorTypeOfFloor(floor) === "CELLAR"
            ? MATERIAL_DOOR_OUTLINE_BASEMENT
            : MATERIAL_DOOR_OUTLINE_DEFAULT
    }

    private floorSlabMaterial(component: FloorSlabFloorBuildingComponent): TMaterialDeclaration<TMaterial> {
        const floor = this.traversableBuilding.floorOf(component.component.value)
        return floor != null && floorTypeOfFloor(floor) === "CELLAR"
            ? MATERIAL_FLOOR_SLAB_FLOOR_BASEMENT
            : MATERIAL_FLOOR_SLAB_FLOOR_DEFAULT
    }

    private roomSlabFloorMaterial(component: BuildingComponent & { component: Readonly<Ref<ConstructionPart>> }): TMaterialDeclaration<TMaterial> {
        const floor = this.traversableBuilding.floorOf(component.component.value)
        return floor != null && floorTypeOfFloor(floor) === "CELLAR"
            ? MATERIAL_ROOM_SLAB_FLOOR_BASEMENT
            : MATERIAL_ROOM_SLAB_FLOOR_DEFAULT
    }

    // noinspection OverlyComplexFunctionJS
    private wallMaterial(component: WallWithHolesBuildingComponent | WallWithoutHolesBuildingComponent | WallSizeAdjustmentBuildingComponent): TMaterialDeclaration<TMaterial> {
        const floor = this.traversableBuilding.floorOf(component.component.value)
        if (floor != null && this.semiVisibleFloorLevels.value.has(floor.level) && !this.visibleFloorLevels.value.has(floor.level)) {
            return MATERIAL_WALL_SEMI_VISIBLE[floor.level % MATERIAL_WALL_SEMI_VISIBLE.length]
        }

        if (component.component.value.id === WallCreator.TEMP_WALL_ID) {
            return MATERIAL_WALL_TEMP
        }

        const mode = this.mode.value;
        if (typeof mode !== 'string' && mode.type === "RELOCATE_OPENING" && mode.wall.id === component.component.value.id) {
            return MATERIAL_SELECTION
        }

        if (this.isRoomOfWallSelected(component) && !this.isRoomOfWallHovered(component)) {
            return MATERIAL_SELECTION_AND_HOVER
        }

        if (this.isRoomOfWallHoveredOrSelected(component)) {
            return MATERIAL_SELECTION
        }

        if (mode === "DRAG_AND_DROP" && this.isEffectedByAnyWallSnapPoint(component, snapPointComponent =>
            this.isComponentHovered(snapPointComponent)
            || this.isComponentSelected(snapPointComponent)
            || this.isComponentDragged(snapPointComponent)
        )) {
            return MATERIAL_SELECTION
        }

        const rawComponent = toRaw(component)
        const snapPointIds = "snapPointIds" in rawComponent ? rawComponent.snapPointIds : rawComponent.wallComponent.snapPointIds

        this.snappingManager.refreshCounter.value //trigger reactivity
        for (const snapPointId of snapPointIds) {
            if (this.snappingManager.snapPointIdToSnapPointsOfSameGroup(component.component.value, snapPointId, true).length < 2) {
                return MATERIAL_WALL_NO_CONNECTION
            }
        }

        const wallType = wallTypeOfWall(component.component.value)
        switch (wallType) {
            case "INTERIOR":
                return MATERIAL_WALL_INTERIOR
            case "EXTERIOR":
                return MATERIAL_WALL_EXTERIOR
            case "INTERMEDIATE":
                return MATERIAL_WALL_INTERMEDIATE
            default:
                console.warn("Unknown wall type", wallType)
                return MATERIAL_WALL_INTERIOR
        }
    }
}