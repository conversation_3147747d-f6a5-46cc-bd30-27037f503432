<template>
    <t-group v-if="visibility !== 'ALWAYS_HIDDEN'"
             :debug="debug"
             :is-camera-fit-ignored="isCameraFitIgnored"
             :name="`${component.type}-${component.component.value.id}`"
             :transformation="transformationX"
             :visible="isSelfVisible || areChildrenVisible">
        <t-mesh :is-billboard="hasBillboardEffect"
                :raycaster-ids="raycasterIds"
                :raycaster-object="raycasterObject"
                :render-order="renderOrder === null ? undefined : renderOrder"
                :transformation="shapeTransformation"
                :visible="isSelfVisible">
            <t-geometry-raw :geometry="geometryWithHoles_000"/>

            <t-mesh-material-raw :material="material"/>
        </t-mesh>

        <slot/>

        <!--        <t-mesh :is-billboard="hasBillboardEffect"-->
        <!--                :raycaster-ids="raycasterIds"-->
        <!--                :raycaster-object="raycasterObject"-->
        <!--                :render-order="renderOrder === null ? undefined : renderOrder"-->
        <!--                :transformation="shapeTransformation"-->
        <!--                :visible="isSelfVisible">-->
        <!--            <t-geometry-raw :geometry="geometryWithHoles_000"/>-->

        <!--            <t-mesh-basic-material :color="0xFF00FF"/>-->
        <!--        </t-mesh>-->

        <!--        <t-group v-if="component.type === 'WALL_WITH_HOLES'"-->
        <!--                 :transformation="new Matrix4().makeRotationX(-Math.PI / 2)">-->
        <!--            <t-mesh :is-billboard="hasBillboardEffect"-->
        <!--                    :render-order="999999"-->
        <!--                    :visible="isSelfVisible">-->
        <!--                <t-geometry-raw :geometry="shapeGeometry"/>-->

        <!--                <t-mesh-material-raw :material="shapeMaterial"/>-->
        <!--            </t-mesh>-->

        <!--            <t-lines2 :color="0xFF00FF"-->
        <!--                      :depth-test="false"-->
        <!--                      :opacity="1"-->
        <!--                      :render-order="1000000"-->
        <!--                      :size="5"-->
        <!--                      :vertices="shapeLines"-->
        <!--                      :visible="isSelfVisible"-->
        <!--                      transparent/>-->
        <!--        </t-group>-->

        <!--        <template v-if="true && shapeRepresentation.shape.__typename === 'Polygon'">-->
        <!--            <t-mesh v-for="(hole, index) of shapeRepresentation.shape.holes"-->
        <!--                    :key="index"-->
        <!--                    :render-order="1000001"-->
        <!--                    :transformation="new Matrix4().makeRotationX(-Math.PI / 2)">-->
        <!--                <t-geometry-raw :geometry="new ShapeGeometry(new Shape(hole.map(v => new Vector2(v.x, v.y))))"/>-->

        <!--                <t-mesh-basic-material :color="0xFF00FF"-->
        <!--                                       :depth-test="false"-->
        <!--                                       :opacity="0.75"-->
        <!--                                       transparent/>-->
        <!--            </t-mesh>-->
        <!--        </template>-->
    </t-group>

    <!--    <template v-if="renderer.debugHoles && isSelfVisible">-->
    <!--        <t-mesh-raw :mesh="parentMesh_000_GM">-->
    <!--            <t-mesh-material-raw :material="rawMeshMaterial"/>-->
    <!--        </t-mesh-raw>-->

    <!--        <t-mesh-raw v-for="holeMesh in holeMeshes_000_GM"-->
    <!--                    :key="holeMesh.id"-->
    <!--                    :mesh="holeMesh">-->
    <!--            <t-mesh-material-raw :material="holeMaterial"/>-->
    <!--        </t-mesh-raw>-->
    <!--    </template>-->
</template>

<script lang="ts"
        setup>
    import {computed, inject, onUnmounted, toRaw, toRef} from "vue";
    import {createEmptyShapeRepresentation, DBuildingRendererInjection, useParentChildMeshes, useRaycasterRelatedShapeRepresentation, useShapeRepresentation} from "@/components/listing/building/building";
    import TGroup from "@/adapter/three/components/object/t-group.vue";
    import TMesh from "@/adapter/three/components/t-mesh.vue";
    import {BufferGeometry, Matrix4} from "three";
    import {Representable, ShapeRepresentation} from "@/adapter/graphql/generated/graphql";
    import {TMaterialDeclaration, TMeshMaterial} from "@/adapter/three/TMaterial";
    import TMeshMaterialRaw from "@/adapter/three/components/material/t-mesh-material-raw.vue";
    import TGeometryRaw from "@/adapter/three/components/geometry/t-geometry-raw.vue";
    import {BuildingComponent} from "@/components/listing/building/renderer/BuildingComponent";
    import {csgGenerateGeometryWithHoles_000} from "@/adapter/csg/csg-utils";

    // const rawMeshMaterial2D = new MeshBasicMaterial({
    //     color: 0x00FFFF,
    //     side: 2,
    //     depthTest: true,
    //     wireframe: true,
    // })
    // const rawMeshMaterial3D = new MeshBasicMaterial({
    //     color: 0x00FFFF,
    //     side: 2,
    //     transparent: true,
    //     opacity: 0.5,
    // })
    //
    // const holeMaterial2D = new MeshBasicMaterial({
    //     color: 0xFF00FF,
    //     side: 2,
    //     depthTest: false,
    // })
    // const holeMaterial3D = new MeshBasicMaterial({
    //     color: 0xFF00FF,
    //     side: 2,
    //     transparent: true,
    //     opacity: 0.5,
    // })
    //
    // const otherHoleDebugMaterial2D = new MeshBasicMaterial({
    //     color: 0x000000,
    //     side: 2,
    //     depthTest: false,
    // })
    // const otherHoleDebugMaterial3D = new MeshBasicMaterial({
    //     color: 0x000000,
    //     side: 2,
    //     transparent: true,
    //     opacity: 0.5,
    // })

    onUnmounted(() => {
        // rawMeshMaterial2D.dispose()
        // rawMeshMaterial3D.dispose()
        // holeMaterial2D.dispose()
        // holeMaterial3D.dispose()
        // otherHoleDebugMaterial2D.dispose()
        // otherHoleDebugMaterial3D.dispose()

        geometryWithHoles_000.value.dispose()

        destroyParentChildMeshes()

        // tDestroyMesh(parentMesh_000_GM.value, true, true)

        // for (const holeMesh of holeMeshes_000_GM.value) {
        //     tDestroyMesh(holeMesh, true, true)
        // }
    })

    const props = withDefaults(defineProps<{
        component: BuildingComponent
        holes: readonly (Representable & { id: string })[]
        holePolygonToBox?: boolean | "FORCE_FALSE"
    }>(), {
        holePolygonToBox: false,
    })

    const componentRef = toRef(() => props.component)

    const renderer = inject(DBuildingRendererInjection)!

    const material = computed<TMaterialDeclaration<TMeshMaterial>>(() => {
        // if (renderer.debugHoles) {
        //     return otherHoleDebugMaterial
        // }
        return renderer.materialOf(componentRef.value) as TMaterialDeclaration<TMeshMaterial>
        // const material = renderer.materialOf(componentRef.value) as TMaterialDeclaration<TMeshMaterial>
        // if (Array.isArray(material)) {
        //     return material[0]
        // }
        // const material2 = material.clone()
        // material2.opacity = 0.5
        // material2.transparent = true
        // return material2
    })

    const shapeRepresentation = computed<ShapeRepresentation>(() => {
        const component = componentRef.value.component.value;
        if ("shapeRepresentation" in toRaw(component)) {
            //wir dürfen hier die raw-variant nicht in eine variable speichern und in der nächsten zeile verwenden, das hebt die reaktivität auf und führt zu darstellungs-updatefehlern (z.b. bei wall creation)
            return (component as any).shapeRepresentation as ShapeRepresentation
        }
        return {
            ...createEmptyShapeRepresentation(),
            transformationMatrix: (component as any).transformationMatrix as number[][],
        }
    })
    const holeShapeRepresentations = computed<readonly ShapeRepresentation[]>(() => props.holes.map(hole => hole.shapeRepresentation))

    // const rawMeshMaterial = renderer.renderType === '2D' ? rawMeshMaterial2D : rawMeshMaterial3D
    // const holeMaterial = renderer.renderType === '2D' ? holeMaterial2D : holeMaterial3D
    // const otherHoleDebugMaterial = renderer.renderType === '2D' ? otherHoleDebugMaterial2D : otherHoleDebugMaterial3D

    const zFightingCorrectedTransformation = computed<Matrix4>(() => {
        const baseTransformation = transformation.value
        const offsetY = zFightingOffsetY.value
        const offsetZ = zFightingOffsetZ.value
        if (offsetY === null && offsetZ === null) {
            return baseTransformation
        }
        return baseTransformation.clone().multiply(new Matrix4().makeTranslation(
            0,
            offsetY ?? 0,
            offsetZ ?? 0,
        ))
    })

    const transformationX = computed<Matrix4>(() => {
        const baseTransformation = zFightingCorrectedTransformation.value
        const scale = zFightingScale.value
        if (scale !== null) {
            const scaleMatrix = new Matrix4().makeScale(scale.x, scale.y, scale.z)
            return baseTransformation.clone().multiply(scaleMatrix)
        }
        return baseTransformation
    })

    const {
        parentTransformation: transformation,
        parentShapeTransformation: shapeTransformation,
        parentMesh: rawParentMesh, //TODO: das könnte man oben auch im view bereich mit mesh-raw nutzen
        childMeshes: rawHoleMeshes,
        destroyParentChildMeshes,
    } = useParentChildMeshes(
        toRef(() => componentRef.value.component.value.id),
        shapeRepresentation,
        holeShapeRepresentations,
        false,
        props.holePolygonToBox,
        true,
        renderer,
        true,
        false,
    )

    // noinspection LocalVariableNamingConventionJS
    const geometryWithHoles_000 = computed<BufferGeometry>(oldGeometryWithHoles => {
        oldGeometryWithHoles?.dispose()

        return csgGenerateGeometryWithHoles_000(rawParentMesh.value, rawHoleMeshes.value)
    })

    // noinspection LocalVariableNamingConventionJS
    // const parentMesh_000_GM = computed<Mesh>(oldParentMesh => {
    //     if (oldParentMesh !== undefined) {
    //         tDestroyMesh(oldParentMesh, true, true)
    //     }
    //
    //     const mesh = rawParentMesh.value.clone(true)
    //     mesh.renderOrder = 100
    //     return mesh
    // })

    // noinspection LocalVariableNamingConventionJS
    // const holeMeshes_000_GM = computed<readonly Mesh[]>(oldHoleMeshes => {
    //     if (oldHoleMeshes !== undefined) {
    //         for (const oldHoleMesh of oldHoleMeshes) {
    //             tDestroyMesh(oldHoleMesh, true, true)
    //         }
    //     }
    //
    //     return rawHoleMeshes.value.map(hMesh => {
    //         const mesh = hMesh.clone(true)
    //         mesh.renderOrder = 1000
    //         return mesh
    //     })
    // })

    const {
        visibility,
        isSelfVisible,
        areChildrenVisible,
        debug,
        renderOrder,
        hasBillboardEffect,
        zFightingOffsetY,
        zFightingOffsetZ,
        zFightingScale,
        isCameraFitIgnored,
    } = useShapeRepresentation(renderer, componentRef)

    const {
        raycasterIds,
        raycasterObject,
    } = useRaycasterRelatedShapeRepresentation(renderer, componentRef)

    // const shape = computed<Shape>(() => shapeRepresentationToThreeShape(shapeRepresentation.value))
    // const shapeGeometry = computed<BufferGeometry>(() => new ShapeGeometry(shape.value))
    // const shapeLines = computed<Vector3[]>(() => tShapeToLinesSegments(shape.value, 12).map(v => new Vector3(v.x, v.y, 0)))
    // const shapeMaterial = computed<TMaterialDeclaration<TMeshMaterial>>(() => {
    //     return new MeshBasicMaterial({
    //         color: 0x00FFFF,
    //         side: 2,
    //         transparent: true,
    //         opacity: 1,
    //         depthTest: false
    //     })
    // })
</script>

<style scoped>
</style>