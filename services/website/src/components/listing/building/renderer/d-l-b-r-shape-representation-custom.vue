<template>
    <!-- keine transformation hier anwenden, da custom shapes immer in ihren parents embedded werden -->

    <d-l-b-r-shape-representation-custom-lines v-if="component.customShapeRepresentation.customShapeType === 'LINES'"
                                               :component="component as BuildingComponentWithCustomLineShapeRepresentation">
        <slot/>
    </d-l-b-r-shape-representation-custom-lines>

    <d-l-b-r-shape-representation-custom-geometry v-else-if="component.customShapeRepresentation.customShapeType === 'GEOMETRY'"
                                                  :component="component as BuildingComponentWithCustomGeometryShapeRepresentation">
        <slot/>
    </d-l-b-r-shape-representation-custom-geometry>

    <d-l-b-r-shape-representation-custom-text v-else-if="component.customShapeRepresentation.customShapeType === 'TEXT'"
                                              :component="component as BuildingComponentWithCustomTextShapeRepresentation">
        <slot/>
    </d-l-b-r-shape-representation-custom-text>

    <d-l-b-r-shape-representation-custom-grid v-else-if="component.customShapeRepresentation.customShapeType === 'GRID'"
                                              :component="component as BuildingComponentWithCustomGridShapeRepresentation">
        <slot/>
    </d-l-b-r-shape-representation-custom-grid>

    <d-l-b-r-shape-representation-custom-perspective-camera v-else-if="component.customShapeRepresentation.customShapeType === 'PERSPECTIVE_CAMERA'"
                                                            :component="component as BuildingComponentWithCustomPerspectiveCameraShapeRepresentation">
        <slot/>
    </d-l-b-r-shape-representation-custom-perspective-camera>
</template>

<script lang="ts"
        setup>
    import DLBRShapeRepresentationCustomLines from "@/components/listing/building/renderer/d-l-b-r-shape-representation-custom-lines.vue";
    import DLBRShapeRepresentationCustomGeometry from "@/components/listing/building/renderer/d-l-b-r-shape-representation-custom-geometry.vue";
    import DLBRShapeRepresentationCustomText from "@/components/listing/building/renderer/d-l-b-r-shape-representation-custom-text.vue";
    import {BuildingComponentWithCustomGeometryShapeRepresentation, BuildingComponentWithCustomGridShapeRepresentation, BuildingComponentWithCustomLineShapeRepresentation, BuildingComponentWithCustomPerspectiveCameraShapeRepresentation, BuildingComponentWithCustomShapeRepresentation, BuildingComponentWithCustomTextShapeRepresentation} from "@/components/listing/building/renderer/BuildingComponent";
    import DLBRShapeRepresentationCustomGrid from "@/components/listing/building/renderer/d-l-b-r-shape-representation-custom-grid.vue";
    import DLBRShapeRepresentationCustomPerspectiveCamera from "@/components/listing/building/renderer/d-l-b-r-shape-representation-custom-perspective-camera.vue";
    import {Representable} from "@/adapter/graphql/generated/graphql";

    defineProps<{
        component: BuildingComponentWithCustomShapeRepresentation
        holes: readonly (Representable & { id: string })[]
    }>()
</script>

<style scoped>
</style>