import {TMaterial, TMaterialDeclaration} from "@/adapter/three/TMaterial";
import {Building, ConstructionPart, DBuildingRendererSaveBuildingMutation, DBuildingRendererSaveBuildingMutationVariables, Floor, Room, useDBuildingRendererSaveBuildingMutation, Wall} from "@/adapter/graphql/generated/graphql";
import {computed, ComputedRef, effectScope, nextTick, Ref, shallowRef, toRaw, watch, watchEffect} from "vue";
import {Optional} from "@/model/Optional";
import {TOcclusionCullingRaycaster} from "@/adapter/three/raycasting/occlusion-culling/TOcclusionCullingRaycaster";
import {TSelectionRaycaster, TSelectionRaycasterSelectionAddChecker} from "@/adapter/three/raycasting/selection/TSelectionRaycaster";
import {TOcclusionCullingRaycasterEmitterOrConsumer, TOcclusionCullingRelated} from "@/adapter/three/raycasting/occlusion-culling/TOcclusionCullingRaycasterEmitterOrConsumer";
import {TSelectionRaycasterEmitterOrConsumer, TSelectionRelated} from "@/adapter/three/raycasting/selection/TSelectionRaycasterEmitterOrConsumer";
import {THoverRaycasterAnyConsumer, THoverRaycasterAnyEmitter, THoverRaycasterEmitterOrConsumer, THoverRelated} from "@/adapter/three/raycasting/hover/THoverRaycasterEmitterOrConsumer";
import {THoverRaycaster} from "@/adapter/three/raycasting/hover/THoverRaycaster";
import {TMaybeRaycasterRelated} from "@/adapter/three/raycasting/TRaycasterRelated";
import {RenderVisibility} from "@/components/listing/building/renderer/RenderVisibility";
import {TRenderType} from "@/adapter/three/TRenderType";
import {TEffect} from "@/adapter/three/TEffect";
import {BuildingComponent, DraggableBuildingComponent, RaycastableBuildingComponent, WallRoofPoint, WallSizeAdjustmentBuildingComponent, WallSnapLineBuildingComponent, WallSnapPointBuildingComponent, WallWithHolesBuildingComponent, WallWithoutHolesBuildingComponent} from "@/components/listing/building/renderer/BuildingComponent";
import {TDraggableRaycasterAnyConsumer, TDraggableRaycasterAnyEmitter, TDraggableRaycasterEmitterOrConsumer, TDraggableRelated} from "@/adapter/three/raycasting/draggable/TDraggableRaycasterEmitterOrConsumer";
import {TDraggableRaycaster} from "@/adapter/three/raycasting/draggable/TDraggableRaycaster";
import {TRaycaster} from "@/adapter/three/raycasting/TRaycaster";
import {TTrackingRaycaster} from "@/adapter/three/raycasting/tracking/TTrackingRaycaster";
import {WallCreator} from "@/components/listing/building/renderer/WallCreator";
import {SnappingManager} from "@/components/listing/building/renderer/SnappingManager";
import {TSnappingResult} from "@/adapter/three/snapping/TSnappingManager";
import {BUILDING_FLOOR_SLAB_THICKNESS_DEFAULT, changeFloorSlabCeilingThickness, changeFloorSlabFloorThickness, createMutableBuildingFrom, wallIdToSnapPointId, WallOrOpeningSnapPointType, WallSnapLineTemplate, WallSnapPoint, WallSnapPointTemplate} from "@/components/listing/building/building";
import {mapBuildingToInput} from "@/adapter/graphql/mapper/building-to-buildinginput-mapper";
import {HistoryManager} from "@/utility/history-manager";
import {IS_DEVELOPMENT} from "@/utility/environment";
import {BuildingPipelineBuilder} from "@/components/listing/building/pipeline/BuildingPipelineBuilder";
import {removeDuplicates} from "@/utility/arrays";
import debounce from "debounce";
import {PointOfInterestCreator} from "@/components/listing/building/renderer/PointOfInterestCreator";
import {WallOpeningCreator} from "@/components/listing/building/renderer/WallOpeningCreator";
import {ToneMapping, Vector3} from "three";
import {TraversableBuilding, TraversalBuildingComponent} from "@/components/listing/building/renderer/TraversableBuilding";
import {calculateSizeAdjustmentXFor} from "@/components/listing/building/pipeline/stages/RecalculateAllWallSizeAdjustmentsBPipelineStage";
import {WallType, WallTypeValues} from "@/model/building/WallType";
import {RoofAreaCreator} from "@/components/listing/building/renderer/RoofAreaCreator";
import {UseMutationReturn} from "@vue/apollo-composable";
import {BUILDING_RENDERER_EMPTY_ID} from "@/components/listing/building/renderer/BuildingRendererEmpty";
import {EnsureDefined} from "@/adapter/graphql/mapper/graphql-mapper";
import {mapToBuilding} from "@/adapter/graphql/mapper/building-to-building-mapper";

export type OpeningRelocationMode = {
    readonly type: "RELOCATE_OPENING"
    readonly wall: Wall
    readonly opening: ConstructionPart
}

export abstract class BuildingRenderer {
    public static readonly GRID_ID = "GRID"
    public static readonly SNAPPING_CROSSHAIR_ID = "SNAPPING_CROSSHAIR"
    private static readonly SELECTION_EMITTER_ID_PREFIX = "BRC__"
    private static readonly SELECTION_EMITTER_ID_BUILDING = `${this.SELECTION_EMITTER_ID_PREFIX}BUILDING`
    private static readonly REBUILD_BUILDING_URL_PARAMETER = "rebuildBuilding"

    readonly traversableBuilding: TraversableBuilding
    gridSize = 100
    debugOcclusionCulling = false
    debugHoles = false
    debugScene = false
    debugSnapping = false
    debugUnrecognizedRooms = false
    showInfo = false
    showStats = false
    showPoles = false
    showGrid = false
    showAxis = false
    showSky = true
    showCompass = shallowRef<boolean>(false)
    showFloorGrid = shallowRef<boolean>(false)
    showRectAreaWorldLights = false
    showAmbientLight = true
    /**
     * die aktionen, die hiermit ausgelöst werden, sollten kein refresh des snapping managers auslösen, das wird im watcher geregelt
     */
    readonly buildingShapeComputationCounter = shallowRef<number>(0)
    readonly rerenderCounter = shallowRef<number>(0)
    readonly mode = shallowRef<"DEFAULT" | "DRAG_AND_DROP" | "WALL_CREATION" | "POI_ADDING" | "OPENING_CREATION" | "ROOF_AREA_CREATION" | OpeningRelocationMode>("DEFAULT")
    readonly showRoofAreas = shallowRef<boolean>(true)
    readonly showWallWidths = shallowRef<boolean>(false)
    readonly showWallThicknesses = shallowRef<boolean>(false)
    readonly showSlabs = shallowRef<boolean>(true)
    readonly showRoomTexts = shallowRef<boolean>(true)
    readonly showPointsOfInterest = shallowRef<boolean>(true)
    readonly isEvebiModeEnabled = shallowRef<boolean>(false)
    readonly showFurniture = shallowRef<boolean>(false)
    readonly showDisplayIds = shallowRef<boolean>(false)
    readonly semiVisibleFloorLevels = shallowRef<ReadonlySet<number>>(new Set<number>())
    readonly visibleFloorLevels = shallowRef<ReadonlySet<number>>(new Set<number>())
    readonly visibleWallTypes = shallowRef<ReadonlySet<WallType>>(new Set<WallType>(WallTypeValues))
    readonly occlusionCullingRaycaster = new TOcclusionCullingRaycaster<RaycastableBuildingComponent<TraversalBuildingComponent>>()
    readonly hoverRaycaster = new THoverRaycaster<RaycastableBuildingComponent<TraversalBuildingComponent>, THoverRaycasterAnyEmitter<RaycastableBuildingComponent<TraversalBuildingComponent>>, THoverRaycasterAnyConsumer<RaycastableBuildingComponent<TraversalBuildingComponent>>>()
    readonly draggableRaycaster: Optional<TDraggableRaycaster<DraggableBuildingComponent<TraversalBuildingComponent>, TDraggableRaycasterAnyEmitter<DraggableBuildingComponent<TraversalBuildingComponent>>, TDraggableRaycasterAnyConsumer<DraggableBuildingComponent<TraversalBuildingComponent>>>>
    readonly snappingManager: SnappingManager
    readonly raycasters: readonly TRaycaster[]
    readonly wallCreator: Optional<WallCreator>
    readonly pointOfInterestCreator: Optional<PointOfInterestCreator>
    readonly wallOpeningCreator: Optional<WallOpeningCreator>
    readonly roofAreaCreator: Optional<RoofAreaCreator>
    readonly isCustomDataSaving = shallowRef<boolean>(false)
    readonly isPipelineRunning = shallowRef<boolean>(false)
    readonly isDeleting = shallowRef<boolean>(false)
    isEscapeButtonEnabled = true
    isLoading!: ComputedRef<boolean>
    cursor!: ComputedRef<Optional<string>>
    readonly trackingRaycaster: Optional<TTrackingRaycaster>
    currentFloor!: ComputedRef<Optional<Floor>>
    readonly historyManager = new HistoryManager<Building>()
    /**
     * Achtung! Hier darf nicht zusätzlich ausgewertet werden, ob der selectionRaycaster.enabled ist, da sonst für die Dauer eines Saves die details kurz den selectionRaycaster disabled
     */
    showSelectionDetails!: ComputedRef<boolean>
    canSelectionBeDeleted!: ComputedRef<boolean>
    protected readonly reactivityScope = effectScope(true)
    protected displayIdSizeAdjustmentXEffectedWallIds!: ComputedRef<ReadonlySet<string>>
    private saveBuildingMutation?: UseMutationReturn<DBuildingRendererSaveBuildingMutation, DBuildingRendererSaveBuildingMutationVariables>
    private hoveredOrSelectedWalls!: ComputedRef<readonly Wall[]>
    private hoveredWalls!: ComputedRef<readonly Wall[]>
    private selectedWalls!: ComputedRef<readonly Wall[]>
    private hoveredOrSelectedRooms!: ComputedRef<readonly Room[]>
    private hoveredRooms!: ComputedRef<readonly Room[]>
    private selectedRooms!: ComputedRef<readonly Room[]>
    private snapPointIdToWallSnapPointComponent = new Map<string, WallSnapPointBuildingComponent>()
    // noinspection LocalVariableNamingConventionJS
    private snapPointIdToWallSnapPointComponentComputationCounter = shallowRef<number>(0)
    private readonly debouncedSnappingManagerRefresh = debounce(() => {
        this.snappingManager.forceRefreshAll()
    }, 50)
    private hasRebuildBuilding = false

    /**
     * @param id
     * @param listingId
     * @param building Muss mutable und voll reaktiv sein.
     * @param canEdit
     * @param renderType
     * @param toneMapping
     * @param effects
     * @param fallbackSize
     * @param previousHistoryManager
     */
    protected constructor(
        public readonly id: string,
        public readonly listingId: string,
        public readonly building: Readonly<Ref<Building>>,
        public readonly canEdit: boolean,
        public readonly renderType: TRenderType,
        public readonly toneMapping: ToneMapping,
        public readonly effects: TEffect[],
        public readonly fallbackSize: number,
        public readonly previousHistoryManager: Optional<HistoryManager<Building>>,
        public readonly renderer: "WEBGL" | "SVG"
    ) {
        this.reactivityScope.run(() => {
            this.isLoading = computed<boolean>(() => this.isCustomDataSaving.value || this.saveBuildingMutation?.loading.value === true || this.isDeleting.value || this.isPipelineRunning.value)
            this.cursor = computed<Optional<string>>(() => {
                if (this.isPipelineRunning.value) {
                    return "wait"
                }
                const mode = this.mode.value;
                switch (mode) {
                    case "DEFAULT":
                    case "ROOF_AREA_CREATION":
                        return this.hoverRaycaster.isHovering.value ? "pointer" : null
                    case "WALL_CREATION":
                        return "none"
                    case "DRAG_AND_DROP":
                        if (!this.hoverRaycaster.isHovering.value) {
                            return null
                        }
                        return this.draggableRaycaster!.isDragging.value ? "grabbing" : "grab"
                    case "POI_ADDING":
                    case "OPENING_CREATION":
                        return this.hoverRaycaster.intersectionTransformationMatrix.value === null ? null : "copy"
                    default:
                        // noinspection SuspiciousTypeOfGuard
                        if (typeof mode !== 'string') {
                            // noinspection NestedSwitchStatementJS
                            switch (mode.type) {
                                case "RELOCATE_OPENING":
                                    return this.hoverRaycaster.intersectionTransformationMatrix.value === null ? "no-drop" : "grabbing"
                                default:
                                    return null
                            }
                        }
                        return null
                }
            })
            watch([
                this.semiVisibleFloorLevels,
                this.visibleFloorLevels,
                this.visibleWallTypes,
                this.showRoomTexts,
                this.showCompass,
                this.showPointsOfInterest,
                this.showFurniture,
                this.showWallWidths,
                this.showWallThicknesses,
                this.showSlabs,
                this.showRoofAreas,
                this.showDisplayIds,
                this.isEvebiModeEnabled,
                this.showFloorGrid,
                this.selectionRaycaster.selection,
            ], () => {
                ++this.rerenderCounter.value
            })
            this.currentFloor = computed<Optional<Floor>>(() => {
                const floorLevels = this.visibleFloorLevels.value
                if (floorLevels.size !== 1) {
                    return null
                }
                const selectedFloorLevel = floorLevels.values().next().value as number
                const floor = this.building.value.floors.find(f => f.level === selectedFloorLevel)
                if (floor === undefined) {
                    return null
                }
                return floor
            })
            this.showSelectionDetails = computed<boolean>(() => this.selectionRaycaster.selection.value.length > 0 && this.calculateShowSelectionDetails(this.selectionRaycaster.selection.value))
            this.canSelectionBeDeleted = computed<boolean>(() => {
                for (const selection of this.selectionRaycaster.selection.value) {
                    if (!this.canBeDeleted(selection)) {
                        return false
                    }
                }
                return true
            })
            this.hoveredOrSelectedWalls = computed<readonly Wall[]>(() => {
                const walls = this.hoveredWalls.value.concat(this.selectedWalls.value)
                return removeDuplicates(walls, w => w.id)
            })
            this.hoveredWalls = computed<readonly Wall[]>(() => this.hoverRaycaster.hover.value
                .map(o => o.type === "WALL_WITH_HOLES" ? o.component.value as Wall : null)
                .filter(w => w !== null)
            )
            this.selectedWalls = computed<readonly Wall[]>(() => this.selectionRaycaster.selection.value
                .map(o => o.type === "WALL_WITH_HOLES" ? o.component.value as Wall : null)
                .filter(w => w !== null)
            )
            this.displayIdSizeAdjustmentXEffectedWallIds = computed<ReadonlySet<string>>(() => {
                const wallIds = new Set<string>()
                for (const wall of this.hoveredOrSelectedWalls.value) {
                    wall.sizeAdjustmentUserXRelatedWallIds.forEach(wallId => wallIds.add(wallId))

                    const sizeAdjustmentX = calculateSizeAdjustmentXFor(this, wall)
                    if (sizeAdjustmentX === null) {
                        continue
                    }
                    for (const side of sizeAdjustmentX.sides) {
                        for (const term of side.terms) {
                            wallIds.add(term.wall.id)
                        }
                    }
                }
                return wallIds
            })
            this.hoveredOrSelectedRooms = computed<readonly Room[]>(() => {
                const rooms = this.hoveredRooms.value.concat(this.selectedRooms.value)
                return removeDuplicates(rooms, r => r.id)
            })
            this.hoveredRooms = computed<readonly Room[]>(() => this.hoverRaycaster.hover.value
                .map(o => o.type === "ROOM" ? o.component.value as Room : null)
                .filter(r => r !== null)
            )
            this.selectedRooms = computed<readonly Room[]>(() => this.selectionRaycaster.selection.value
                .map(o => o.type === "ROOM" ? o.component.value as Room : null)
                .filter(r => r !== null)
            )
        })

        this.traversableBuilding = new TraversableBuilding(this)
        this.snappingManager = new SnappingManager(this)
        this.snappingManager.setAutoRefresh(false)

        this.trackingRaycaster = canEdit ? new TTrackingRaycaster() : null //muss vor wallCreator erzeugt werden
        this.wallCreator = canEdit ? new WallCreator(this) : null
        this.pointOfInterestCreator = canEdit ? new PointOfInterestCreator(this) : null
        this.roofAreaCreator = canEdit ? new RoofAreaCreator(this) : null
        this.wallOpeningCreator = canEdit ? new WallOpeningCreator(this) : null
        this.draggableRaycaster = canEdit ? new TDraggableRaycaster() : null
        this.historyManager.initialize(previousHistoryManager ?? createMutableBuildingFrom(this.building.value), building => {
            this.saveBuilding(building, false).then()
        })

        this.raycasters = this.initializeRaycasters(canEdit)

        if (this.canEdit) {
            this.historyManager.areHotkeysEnabled(true)

            this.reactivityScope.run(() => {
                watch(this.building, () => {
                    this.snappingManager.setAutoRefresh(false)
                    ++this.buildingShapeComputationCounter.value //force refresh wird in watch(this.buildingShapeComputationCounter) ausgelöst
                    nextTick(() => {
                        this.snappingManager.setAutoRefresh(this.mode.value !== "WALL_CREATION" && !this.draggableRaycaster!.isDragging.value) //diese zeile sollte alle setAutoRefreshes covern (wiederherstellen)

                        if (!this.hasRebuildBuilding) {
                            const queryString = window.location.search
                            const urlParameters = new URLSearchParams(queryString)
                            const rebuildBuilding = urlParameters.get(BuildingRenderer.REBUILD_BUILDING_URL_PARAMETER) === 'true'

                            if (rebuildBuilding) {
                                this.hasRebuildBuilding = true

                                nextTick(async () => {
                                    await BuildingPipelineBuilder
                                        .create("RebuildBuilding - Phase 1", this)
                                        .doAll(false)
                                        .build()
                                        .execute()

                                    await BuildingPipelineBuilder
                                        .create("RebuildBuilding - Phase 2", this)
                                        .doAll(false)
                                        .build()
                                        .execute()

                                    window.history.replaceState({}, document.title, window.location.pathname)
                                })
                            }
                        }
                    }).then()
                }, {
                    immediate: true,
                })

                watch(this.draggableRaycaster!.isDragging, isDragging => {
                    if (!isDragging) {
                        const buildingPipelineBuilder = BuildingPipelineBuilder.create("PostDragging", this)

                        const currentFloor = this.currentFloor.value
                        if (currentFloor === null) {
                            console.warn("currentFloor is null")
                        } else {
                            buildingPipelineBuilder
                                .splitWallsOnFloors(currentFloor)
                                .mergeWallsOnFloors([currentFloor])
                                .removeZeroWallsOnFloors(currentFloor)
                                .removeDuplicateWallsOnFloors(currentFloor)
                                .renewRoomsOnFloors([currentFloor])
                                .flipExteriorWallsOnFloors(currentFloor)
                                .renewFloors(currentFloor)
                                .renewBuilding(false)
                                .recalculateAllWallSizeAdjustments()
                                .renewDisplayIdsAndRoomNumbers()
                                .removeInvalidRelations()
                        }

                        buildingPipelineBuilder
                            .save()
                            .build()
                            .execute()
                            .then()
                    }

                    this.historyManager.isEnabled.value = !isDragging
                    this.snappingManager.setAutoRefresh(!isDragging) //sonst würden beim ziehen neue gruppen geschaffen werden, dann snappen alle möglichen snap points zusammen
                    this.hoverRaycaster.isEnabled = !isDragging
                    //this.selectionRaycaster.isEnabled = !isDragging //geht eig. nicht, sonst kann man nichts mehr selektieren
                    //this.occlusionCullingRaycaster.isEnabled = !isDragging //eig. nicht notwendig

                    this.snappingManager.forceRefresh() //vor dem dragging ist klar, aber nach dem dragging müssen auch die linien erneuert werden, die sonst nicht ausgelöst werden würden
                })

                watch(this.mode, mode => {
                    this.snappingManager.forceRefreshAll()
                    this.trackingRaycaster!.isEnabled = mode === "WALL_CREATION" || mode === "DRAG_AND_DROP" || mode === "POI_ADDING" || mode === "OPENING_CREATION" || (typeof mode !== 'string' && mode.type === "RELOCATE_OPENING")
                    this.snappingManager.setAutoRefresh(mode !== "WALL_CREATION")
                    this.selectionRaycaster.forceMultiSelection = mode === "ROOF_AREA_CREATION"
                    this.selectionRaycaster.allowDeselectionOnEmptyClick = mode !== "ROOF_AREA_CREATION"
                })

                this.saveBuildingMutation = useDBuildingRendererSaveBuildingMutation()
            })

            this.registerEventListeners()
        }

        this.reactivityScope.run(() => {
            watch(this.buildingShapeComputationCounter, () => {
                console.log("Updated building shape")

                nextTick(() => {
                    //wenn sich die shape ändert, werden die snap points und lines neu positioniert und benötigen danach ein force refresh,
                    //da sie das refreshen aushebeln, da es sonst performance probleme gibt, wegen zu häufigem refreshen des snapping managers
                    this.snappingManager.forceRefreshAll()
                }).then()
            })

            watchEffect(() => {
                this.selectionRaycaster.isEnabled = !this.isCustomDataSaving.value
            })
        })
    }

    static selectionEmitterIdForBuilding(): string {
        return this.SELECTION_EMITTER_ID_BUILDING
    }

    static selectionEmitterIdForFloor(floorId: string): string {
        return `${this.SELECTION_EMITTER_ID_PREFIX}FLOOR_${floorId}`
    }

    static selectionEmitterIdForPoi(poiId: string): string {
        return `${this.SELECTION_EMITTER_ID_PREFIX}POI_${poiId}`
    }

    static selectionEmitterIdForOpening(openingId: string): string {
        return `${this.SELECTION_EMITTER_ID_PREFIX}OPENING_${openingId}`
    }

    saveBuildingStateToHistory(building: Building = this.building.value) {
        if (this.id === BUILDING_RENDERER_EMPTY_ID) {
            return
        }

        if (this.historyManager.add(building)) {
            if (IS_DEVELOPMENT) {
                console.log("Added new history state for building")
            }
        } else {
            if (IS_DEVELOPMENT) {
                console.log("No new history state for building added")
            }
        }
    }

    registerWallSnapPointComponent(snapPointComponent: WallSnapPointBuildingComponent) {
        this.snapPointIdToWallSnapPointComponent.set(snapPointComponent.snapPoint.id, snapPointComponent)
        ++this.snapPointIdToWallSnapPointComponentComputationCounter.value

        this.debouncedSnappingManagerRefresh()
    }

    unregisterWallSnapPointComponent(snapPointComponent: WallSnapPointBuildingComponent) {
        this.snapPointIdToWallSnapPointComponent.delete(snapPointComponent.snapPoint.id)
        ++this.snapPointIdToWallSnapPointComponentComputationCounter.value

        this.debouncedSnappingManagerRefresh()
    }

    destroy() {
        this.reactivityScope.stop()

        this.unregisterEventListeners()

        this.wallCreator?.destroy()
        this.pointOfInterestCreator?.destroy()
        this.roofAreaCreator?.destroy()
        this.wallOpeningCreator?.destroy()
        this.occlusionCullingRaycaster.destroy()
        this.hoverRaycaster.destroy()
        this.draggableRaycaster?.destroy()
        this.trackingRaycaster?.destroy()
        this.selectionRaycaster.destroy()
        this.snapPointIdToWallSnapPointComponent.clear()
        this.snappingManager.destroy()
        this.traversableBuilding.destroy()
        this.historyManager.destroy()
    }

    async deleteSelection() {
        if (!this.canSelectionBeDeleted.value || !this.showSelectionDetails.value) {
            return
        }

        this.isDeleting.value = true
        try {
            const selectedComponents = this.selectionRaycaster.selection.value.filter(s => s.type !== "WALL_ROOF_POINT") as RaycastableBuildingComponent<TraversalBuildingComponent>[]
            await this.deleteBuildingComponents(...selectedComponents)
        } finally {
            this.isDeleting.value = false
        }
    }

    async saveBuilding(
        building: Building = toRaw(this.building.value),
        saveToHistory: boolean = true,
        reloadPageAndRebuildBuilding: boolean = false,
    ) {
        if (!this.canEdit) {
            return
        }

        try {
            await this.saveBuildingMutation!.mutate({
                listingBuildingData: {
                    listingId: this.listingId,
                    building: mapBuildingToInput(building)
                }
            }, {
                update: (cache, result) => {
                    cache.modify({
                        id: cache.identify({__typename: "Listing", id: this.listingId}),
                        fields: {
                            building(): Optional<EnsureDefined<Building>> {
                                return mapToBuilding(building)
                            }
                        }
                    });
                }
            })

            if (saveToHistory) {
                this.saveBuildingStateToHistory(building)
            }
        } finally {
            ++this.rerenderCounter.value

            if (reloadPageAndRebuildBuilding) {
                window.location.href += `?${BuildingRenderer.REBUILD_BUILDING_URL_PARAMETER}=true`
            }
        }
    }

    hasBillboardEffect(component: BuildingComponent): boolean {
        return this.calculateHasBillboardEffect(component)
    }

    materialOf(component: TMaybeRaycasterRelated<TraversalBuildingComponent, BuildingComponent>): TMaterialDeclaration<TMaterial> {
        return this.calculateMaterialOf(component)
    }

    zFightingOffsetYOf(component: BuildingComponent): Optional<number> {
        return this.calculateZFightingOffsetY(component)
    }

    zFightingOffsetZOf(component: BuildingComponent): Optional<number> {
        return this.calculateZFightingOffsetZ(component)
    }

    zFightingScaleOf(component: BuildingComponent): Optional<Vector3> {
        return this.calculateZFightingScale(component)
    }

    isCameraFitIgnored(component: BuildingComponent): boolean {
        return this.calculateIsCameraFitIgnored(component)
    }

    visibility(component: BuildingComponent): RenderVisibility {
        return this.calculateVisibility(component)
    }

    debug(component: BuildingComponent): boolean {
        if (!this.isComponentVisibleForDebug(component)) {
            return false
        }

        return this.calculateDebug(component)
    }

    raycasterIdsOf(component: TMaybeRaycasterRelated<RaycastableBuildingComponent<TraversalBuildingComponent>, BuildingComponent>): ReadonlySet<string> {
        const rawComponent = toRaw(component)
        const raycasterIds = new Set<string>()

        if (rawComponent.component.value.id === WallCreator.TEMP_WALL_ID) {
            return raycasterIds //leeres set
        }

        if (!this.isComponentVisibleForRaycaster(rawComponent)) {
            return raycasterIds //leeres set
        }

        if ("occlusionCulling" in rawComponent && this.isOcclusionCullable(rawComponent)) {
            raycasterIds.add(this.occlusionCullingRaycaster.id)
        }

        if ("selection" in rawComponent && this.isSelectable(rawComponent)) {
            raycasterIds.add(this.selectionRaycaster.id)
        }

        if ("hover" in rawComponent && this.isHoverable(rawComponent)) {
            raycasterIds.add(this.hoverRaycaster.id)
        }

        if (this.canEdit && "draggable" in rawComponent && this.isDraggable(rawComponent)) {
            raycasterIds.add(this.draggableRaycaster!.id)
        }

        return raycasterIds
    }

    renderOrderOf(component: BuildingComponent): Optional<number> {
        return this.calculateRenderOrder(component)
    }

    public isComponentVisibleRelatedToFloor(buildingComponent: TraversalBuildingComponent | BuildingComponent): boolean {
        const rawBuildingComponent = toRaw(buildingComponent)
        const component = "component" in rawBuildingComponent ? rawBuildingComponent.component.value : rawBuildingComponent

        if (component.__typename === undefined || component.__typename === "Building" || component.__typename === "Floor" || component.__typename === "BuildingPointOfInterest") {
            return true
        }

        const floor = this.traversableBuilding.floorOf(component)
        if (floor === null) {
            return true
        }
        // noinspection RedundantIfStatementJS
        if (!this.visibleFloorLevels.value.has(floor.level)) {
            return false
        }
        return true
    }

    /**
     * ACHTUNG: Kann TEMP_WALL zurückliefern
     */
    public wallSnapPointComponentsOfSnapPoints(snapPoints: readonly WallSnapPoint[]): WallSnapPointBuildingComponent[] {
        return snapPoints
            .map(snapPoint => this.snapPointIdToWallSnapPointComponent.get(snapPoint.id))
            .filter(snapPointComponent => snapPointComponent !== undefined)
            .map(comp => comp!) // needed to remove undefined warning
    }

    public wallSnapPointComponentOfSnapPointId(snapPointId: string): Optional<WallSnapPointBuildingComponent> {
        return this.snapPointIdToWallSnapPointComponent.get(snapPointId) ?? null
    }

    public clearSelection() {
        if (!this.showSelectionDetails.value) {
            return
        }
        this.selectionRaycaster.reset()
    }

    /**
     * ACHTUNG: Kann TEMP_WALL zurückliefern
     */
    wallsOfSnapPointId(snapPointId: string, floorLevel: number): Wall[] {
        const wallSnapPoints = this.snappingManager.snapPointIdToSnappedSnapPoints(snapPointId, floorLevel)
        return this.wallSnapPointComponentsOfSnapPoints(wallSnapPoints).map(c => c.wallWithHolesComponent.component.value)
    }

    neighborWallsOf(wall: Wall, type: WallOrOpeningSnapPointType, filterTempWall: boolean): Wall[] {
        const snapPointId = wallIdToSnapPointId(wall.id, type)
        const floor = this.traversableBuilding.floorOf(wall)
        if (floor === null) {
            console.error(`floor is null for wall ${wall.id}`)
            return []
        }
        return this.wallsOfSnapPointId(snapPointId, floor.level).filter(w => w.id !== wall.id && (!filterTempWall || w.id !== WallCreator.TEMP_WALL_ID))
    }

    protected isWallRoofPointValid(wallRoofPoint: WallRoofPoint): boolean {
        if (wallRoofPoint.wall.roomIds === undefined || wallRoofPoint.wall.roomIds === null || wallRoofPoint.wall.roomIds.length <= 0) {
            return false
        }
        const validRoomIds = this.roofAreaCreator!.validRoomIds.value
        if (validRoomIds.size <= 0) {
            return true
        }
        return wallRoofPoint.wall.roomIds?.some(roomId => validRoomIds.has(roomId)) === true
    }

    protected abstract calculateZFightingOffsetY(component: BuildingComponent): Optional<number>

    protected abstract calculateZFightingOffsetZ(component: BuildingComponent): Optional<number>

    protected abstract calculateZFightingScale(component: BuildingComponent): Optional<Vector3>

    protected matchSnappingResultWallSnappingLineComponent(snappingResult: TSnappingResult<WallSnapPointTemplate, WallSnapLineTemplate>, component: WallSnapLineBuildingComponent): boolean {
        switch (snappingResult.type) {
            case "LINE":
                return snappingResult.snapLine.id === component.id
            case "LINE_INTERSECTION_POINT":
                return snappingResult.intersectionPoint.line1.id === component.id
                    || snappingResult.intersectionPoint.line2.id === component.id
            default:
                return false
        }
    }

    protected abstract initializeRaycasters(edit: boolean): TRaycaster[]

    protected abstract calculateCanBeDeleted(component: RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint): boolean

    protected isEffectedByAnyWallSnapPoint(wallComponent: WallWithHolesBuildingComponent | WallWithoutHolesBuildingComponent | WallSizeAdjustmentBuildingComponent, isEffectedCalculator: (snapPointComponent: WallSnapPointBuildingComponent | WallWithHolesBuildingComponent | WallWithoutHolesBuildingComponent) => boolean): boolean {
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        this.snapPointIdToWallSnapPointComponentComputationCounter.value //trigger reactivity
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        this.snappingManager.refreshCounter.value //trigger reactivity

        // console.log("===========================")

        const rawComponent = toRaw(wallComponent)
        const snapPointIds = "snapPointIds" in rawComponent ? rawComponent.snapPointIds : rawComponent.wallComponent.snapPointIds

        for (const snapPointId of snapPointIds) {
            const snapPointsOfSameGroup = this.snappingManager.snapPointIdToSnapPointsOfSameGroup(wallComponent.component.value, snapPointId, true)

            const snapPointComponents = this.wallSnapPointComponentsOfSnapPoints(snapPointsOfSameGroup).filter(c => c.component.value.id !== WallCreator.TEMP_WALL_ID)

            // console.log("snaps", snapPointComponents.map(s => s.snapPoint.id))

            for (const snapPointComponent of snapPointComponents) {
                if (isEffectedCalculator(snapPointComponent)) {
                    // console.log("1")
                    return true
                }
                if (isEffectedCalculator(snapPointComponent.wallWithHolesComponent)) {
                    // console.log("2")
                    return true
                }
                if (isEffectedCalculator(snapPointComponent.wallWithoutHolesComponent)) {
                    // console.log("3")
                    return true
                }
            }
        }
        return false
    }

    protected abstract calculateShowSelectionDetails(selection: readonly (RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint)[]): boolean

    protected abstract calculateHasBillboardEffect(component: BuildingComponent): boolean

    protected abstract calculateRenderOrder(component: BuildingComponent): Optional<number>

    protected abstract calculateDebug(component: BuildingComponent): boolean

    protected isComponentHovered(component: TMaybeRaycasterRelated<TraversalBuildingComponent, BuildingComponent>): boolean {
        const rawComponent = toRaw(component)
        return "hover" in rawComponent && rawComponent.hover.isConsumer && rawComponent.hover.isHovered.value
    }

    protected isComponentSelected(component: TMaybeRaycasterRelated<TraversalBuildingComponent, BuildingComponent>): boolean {
        const rawComponent = toRaw(component)
        return "selection" in rawComponent && rawComponent.selection.isConsumer && rawComponent.selection.isSelected.value
    }

    protected isComponentDragged(component: TMaybeRaycasterRelated<TraversalBuildingComponent, BuildingComponent>): boolean {
        const rawComponent = toRaw(component)
        return "draggable" in rawComponent && rawComponent.draggable.isConsumer && rawComponent.draggable.isDragged.value
    }

    protected isComponentOcclusionCulled(component: TMaybeRaycasterRelated<TraversalBuildingComponent, BuildingComponent>): boolean {
        const rawComponent = toRaw(component)
        return "occlusionCulling" in rawComponent && rawComponent.occlusionCulling.isConsumer && rawComponent.occlusionCulling.isOcclusionCulled.value
    }

    protected abstract isHoverable(component: BuildingComponent & THoverRelated<THoverRaycasterEmitterOrConsumer<RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint>>): boolean

    protected abstract isDraggable(component: BuildingComponent & TDraggableRelated<TDraggableRaycasterEmitterOrConsumer<RaycastableBuildingComponent<TraversalBuildingComponent>>>): boolean

    protected abstract isSelectable(component: BuildingComponent & TSelectionRelated<TSelectionRaycasterEmitterOrConsumer<RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint>>): boolean

    protected abstract calculateVisibility(component: BuildingComponent): RenderVisibility

    protected abstract calculateIsCameraFitIgnored(component: BuildingComponent): boolean

    protected abstract calculateMaterialOf(component: TMaybeRaycasterRelated<TraversalBuildingComponent, BuildingComponent>): TMaterialDeclaration<TMaterial>

    protected abstract isOcclusionCullable(component: BuildingComponent & TOcclusionCullingRelated<TOcclusionCullingRaycasterEmitterOrConsumer<RaycastableBuildingComponent<TraversalBuildingComponent>>>): boolean

    protected isRoomOfWallHoveredOrSelected(wallComponent: WallWithHolesBuildingComponent | WallWithoutHolesBuildingComponent | WallSizeAdjustmentBuildingComponent): boolean {
        for (const room of this.hoveredOrSelectedRooms.value) {
            if (room.wallIds?.includes(wallComponent.component.value.id) === true) {
                return true
            }
        }
        return false
    }

    protected isWallDisplayIdEffectedByOtherHoveredOrSelectedWall(wallComponent: WallWithHolesBuildingComponent | WallWithoutHolesBuildingComponent): boolean {
        if (!this.isEvebiModeEnabled.value) {
            return false
        }
        return this.displayIdSizeAdjustmentXEffectedWallIds.value.has(wallComponent.component.value.id)
    }

    protected isRoomOfWallHovered(wallComponent: WallWithHolesBuildingComponent | WallWithoutHolesBuildingComponent | WallSizeAdjustmentBuildingComponent): boolean {
        for (const room of this.hoveredRooms.value) {
            if (room.wallIds?.includes(wallComponent.component.value.id) === true) {
                return true
            }
        }
        return false
    }

    protected isRoomOfWallSelected(wallComponent: WallWithHolesBuildingComponent | WallWithoutHolesBuildingComponent | WallSizeAdjustmentBuildingComponent): boolean {
        for (const room of this.selectedRooms.value) {
            if (room.wallIds?.includes(wallComponent.component.value.id) === true) {
                return true
            }
        }
        return false
    }

    private readonly onKeyDown = (event: KeyboardEvent) => { //implicit "this" binding
        if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement || event.target instanceof HTMLSelectElement) {
            return
        }

        switch (event.key) {
            case "Delete":
            case "Backspace":
                this.deleteSelection().then() //checks by itself, if selection details are open
                break

            case "Escape":
                if (this.isEscapeButtonEnabled) {
                    this.clearSelection() //checks by itself, if selection details are open
                }
                break

            case " ":
                this.setSnappingSize("SMALL")
                break
        }
    }

    private readonly onKeyUp = (event: KeyboardEvent) => { //implicit "this" binding
        if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement || event.target instanceof HTMLSelectElement) {
            return
        }

        switch (event.key) {
            case " ":
                this.setSnappingSize("BIG")
                break
        }
    }

    private setSnappingSize(size: "SMALL" | "BIG") {
        if (this.mode.value !== "WALL_CREATION" && this.mode.value !== "DRAG_AND_DROP") {
            return
        }
        this.snappingManager.setSnappingSize(size)
    }

    private registerEventListeners() {
        window.addEventListener("keydown", this.onKeyDown)
        window.addEventListener("keyup", this.onKeyUp)
    }

    private unregisterEventListeners() {
        window.removeEventListener("keydown", this.onKeyDown)
        window.removeEventListener("keyup", this.onKeyUp)
    }

    private canBeDeleted(component: RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint): boolean {
        return this.canEdit && this.calculateCanBeDeleted(component)
    }

    // noinspection OverlyComplexFunctionJS,FunctionTooLongJS
    private async deleteBuildingComponents(...components: readonly RaycastableBuildingComponent<TraversalBuildingComponent>[]) {
        console.log("Deleting components", components.map(c => `${c.component.value.__typename} ${c.component.value.id}`))

        const idsOfFloorsToRenew = new Set<string>()
        let renewBuilding = false

        const building = this.building.value;
        let floor: Floor
        for (const childComponentRef of components) {
            const childComponent = childComponentRef.component.value
            renewBuilding = renewBuilding || childComponent.__typename === "Floor" || childComponent.__typename === "Building"

            const parentComponent = this.traversableBuilding.parentOf(childComponent)
            if (parentComponent === null) {
                continue
            }
            switch (parentComponent.__typename) {
                case "Building":
                    if (childComponent.__typename === "Floor") {
                        floor = childComponent
                        this.semiVisibleFloorLevels.value = new Set([...this.semiVisibleFloorLevels.value].filter(level => level !== floor.level))
                        this.visibleFloorLevels.value = new Set([...this.visibleFloorLevels.value].filter(level => level !== floor.level))

                        //Obere und untere Slabhöhen anpassen //TODO: wenn man mehrere stockwerke gleichzeitig löschen (könnte), dann muss das noch angepasst werden
                        const floorIndex = building.floors.findIndex(f => f.id === floor.id)
                        const bottomFloor = floorIndex <= 0 ? null : building.floors[floorIndex - 1]
                        const topFloor = floorIndex >= building.floors.length - 1 ? null : building.floors[floorIndex + 1]

                        if (bottomFloor !== null) {
                            changeFloorSlabCeilingThickness(this, bottomFloor, BUILDING_FLOOR_SLAB_THICKNESS_DEFAULT, false)
                            idsOfFloorsToRenew.add(bottomFloor.id)
                        }
                        if (topFloor !== null) {
                            changeFloorSlabFloorThickness(this, topFloor, BUILDING_FLOOR_SLAB_THICKNESS_DEFAULT, false)
                            idsOfFloorsToRenew.add(topFloor.id)
                        }

                        building.floors = building.floors.filter(f => f.id !== floor.id)
                    }
                    if (childComponent.__typename === "BuildingPointOfInterest") {
                        building.pointsOfInterest = building.pointsOfInterest?.filter(pointOfInterest => pointOfInterest.id !== childComponent.id) ?? []
                    }
                    break
                case "Floor":
                    if (childComponent.__typename === "Room") {
                        parentComponent.rooms = parentComponent.rooms.filter(room => room.id !== childComponent.id)
                    }
                    if (childComponent.__typename === "Wall") {
                        idsOfFloorsToRenew.add(parentComponent.id)
                        parentComponent.walls = parentComponent.walls.filter(wall => wall.id !== childComponent.id)
                    }
                    if (childComponent.__typename === "Furniture") {
                        parentComponent.furniture = parentComponent.furniture?.filter(furniture => furniture.id !== childComponent.id) ?? []
                    }
                    if (childComponent.__typename === "BuildingPointOfInterest") {
                        parentComponent.pointsOfInterest = parentComponent.pointsOfInterest?.filter(pointOfInterest => pointOfInterest.id !== childComponent.id) ?? []
                    }
                    if (childComponent.__typename === "RoofArea") {
                        parentComponent.roofAreas = parentComponent.roofAreas?.filter(roofArea => roofArea.id !== childComponent.id) ?? []
                    }
                    break
                case "Room":
                    if (childComponent.__typename === "Furniture") {
                        parentComponent.furniture = parentComponent.furniture?.filter(furniture => furniture.id !== childComponent.id) ?? []
                    }
                    break
                case "Wall":
                    if (childComponent.__typename === "ConstructionPart") {
                        parentComponent.openings = parentComponent.openings.filter(opening => opening.id !== childComponent.id)
                    }
                    break
                default:
                    console.error(`Unknown parent component type: ${parentComponent.__typename}`)
                    break
            }
        }

        this.traversableBuilding.refresh()

        await nextTick(async () => { //erst im nächsten Vue-Renderschritt sind die Komponenten entfernt worden und wir können "Refreshes" durchführen
            this.snappingManager.forceRefreshAll()

            const buildingPipelineBuilder = BuildingPipelineBuilder.create("DeleteBuildingComponents", this)

            if (idsOfFloorsToRenew.size > 0) {
                for (const floor of building.floors) {
                    if (idsOfFloorsToRenew.has(floor.id)) {
                        buildingPipelineBuilder
                            //kein split notwendig
                            .mergeWallsOnFloors([floor])
                            .removeZeroWallsOnFloors(floor)
                            .removeDuplicateWallsOnFloors(floor)
                            .renewRoomsOnFloors([floor])
                            .flipExteriorWallsOnFloors(floor)
                            .renewFloors(floor)
                            .removeInvalidRelations()
                    }
                }
                renewBuilding = true
            }

            if (renewBuilding) {
                buildingPipelineBuilder
                    .renewBuilding(false)
                    .recalculateAllWallSizeAdjustments()
            }

            await buildingPipelineBuilder
                .renewDisplayIdsAndRoomNumbers()
                .save()
                .build()
                .execute()
        })
    }

    private readonly mayElementBeAddedToSelection: TSelectionRaycasterSelectionAddChecker<RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint> = (selection, selectedElementToAdd) => { //implicit "this" binding
        if (selection.some(s => s.type === "BUILDING" || s.type === "WALL_SNAP_POINT")) {
            return false
        }

        const areTypesEqual = selection.some(s => s.type === selectedElementToAdd.type)

        if (areTypesEqual && selectedElementToAdd.type === "WALL_ROOF_POINT") {
            const wallRoofPoint = selectedElementToAdd as WallRoofPoint

            //WALL_ROOF_POINTS
            const floor = this.traversableBuilding.floorOf(wallRoofPoint.wall)
            if (floor === null) {
                console.warn(`Floor is null for wall ${wallRoofPoint.wall.id}`)
                return false
            }

            return selection.some(s => {
                const sWallRoofPoint = s as WallRoofPoint
                const sFloor = this.traversableBuilding.floorOf(sWallRoofPoint.wall)
                if (sFloor === null) {
                    console.warn(`Floor is null for wall ${sWallRoofPoint.wall.id}`)
                    return false
                }
                return sFloor.id === floor.id
            })
        }

        return areTypesEqual
    }

    readonly selectionRaycaster = new TSelectionRaycaster<RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint>("multi", this.mayElementBeAddedToSelection)

    private isComponentVisibleForDebug(component: BuildingComponent): boolean {
        return this.isComponentVisibleRelatedToFloor(component)
    }

    private isComponentVisibleForRaycaster(component: BuildingComponent): boolean {
        return this.isComponentVisibleRelatedToFloor(component)
    }
}