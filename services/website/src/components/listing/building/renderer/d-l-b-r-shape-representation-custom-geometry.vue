<template>
    <t-group v-if="visibility !== 'ALWAYS_HIDDEN'"
             :visible="isSelfVisible || areChildrenVisible">
        <!-- TODO: eig. muss is-billboard auf group-ebene, aber dann funktioniert das nicht mehr für snap points während man dragged, "warum" klären -->
        <!-- TODO: eig. muss add to scene auf group-ebene -->
        <t-mesh :add-to-scene="shapeRepresentation.addToScene"
                :debug="debug"
                :is-billboard="hasBillboardEffect"
                :raycaster-ids="raycasterIds"
                :raycaster-object="raycasterObject"
                :render-order="renderOrder === null ? undefined : renderOrder"
                :transformation="transformation"
                :visible="isSelfVisible">
            <t-geometry-raw :geometry="shapeRepresentation.geometry.value"/>

            <t-mesh-material-raw :material="material"/>

            <slot/>
        </t-mesh>
    </t-group>
</template>

<script lang="ts"
        setup>
    import TMesh from "@/adapter/three/components/t-mesh.vue";
    import TMeshMaterialRaw from "@/adapter/three/components/material/t-mesh-material-raw.vue";
    import TGeometryRaw from "@/adapter/three/components/geometry/t-geometry-raw.vue";
    import TGroup from "@/adapter/three/components/object/t-group.vue";
    import {BuildingComponentWithCustomGeometryShapeRepresentation, CustomGeometryShapeRepresentation} from "@/components/listing/building/renderer/BuildingComponent";
    import {computed, inject, toRef} from "vue";
    import {DBuildingRendererInjection, useRaycasterRelatedShapeRepresentation, useShapeRepresentation} from "@/components/listing/building/building";
    import {TMaterialDeclaration, TMeshMaterial} from "@/adapter/three/TMaterial";
    import {Matrix4} from "three";

    const props = defineProps<{
        component: BuildingComponentWithCustomGeometryShapeRepresentation
    }>()

    const componentRef = toRef(() => props.component)

    const renderer = inject(DBuildingRendererInjection)!

    const material = computed<TMaterialDeclaration<TMeshMaterial>>(() => renderer.materialOf(componentRef.value) as TMaterialDeclaration<TMeshMaterial>)
    const shapeRepresentation = computed<CustomGeometryShapeRepresentation>(() => componentRef.value.customShapeRepresentation)

    const transformation = computed<Matrix4>(() => {
        const baseTransformation = shapeRepresentation.value.transformationMatrix.value
        const scale = zFightingScale.value
        if (scale !== null) {
            const scaleMatrix = new Matrix4().makeScale(scale.x, scale.y, scale.z)
            return baseTransformation.clone().multiply(scaleMatrix)
        }
        return baseTransformation
    })

    const {
        visibility,
        isSelfVisible,
        areChildrenVisible,
        debug,
        renderOrder,
        hasBillboardEffect,
        zFightingScale,
    } = useShapeRepresentation(renderer, componentRef)

    const {
        raycasterIds,
        raycasterObject,
    } = useRaycasterRelatedShapeRepresentation(renderer, componentRef)
</script>

<style scoped>
</style>