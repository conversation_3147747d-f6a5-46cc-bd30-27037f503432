<template>
    <d-l-b-r-shape-representation :component="component"/>
</template>

<script lang="ts"
        setup>
    import {computed} from "vue";
    import {CustomPerspectiveCameraShapeRepresentation, PointOfInterestBuildingComponent, PointOfInterestCameraBuildingComponent} from "@/components/listing/building/renderer/BuildingComponent";
    import DLBRShapeRepresentation from "@/components/listing/building/renderer/d-l-b-r-shape-representation.vue";
    import {PointOfInterestCamera} from "@/components/listing/building/building";
    import {Matrix4, PerspectiveCamera} from "three";
    import {tExtractParametersFromProjectionMatrixOfPerspectiveCamera} from "@/adapter/three/three-utility";

    const props = defineProps<{
        pointOfInterestComponent: PointOfInterestBuildingComponent
        camera: PointOfInterestCamera
        index: number
        showIPhoneShape: boolean
    }>()

    const clippingPlaneNear = 0.5
    const clippingPlaneFar = 0.25
    // const projectRotationMatrix = new Matrix4().makeRotationX(-Math.PI / 2)

    // const cameraMatrix = new PerspectiveCamera(75, 16 / 9, 0.1, 1000)

    const component: PointOfInterestCameraBuildingComponent = {
        id: `${props.pointOfInterestComponent.id}-camera-${props.index}`,
        type: "POINT_OF_INTEREST_CAMERA",
        pointOfInterestComponent: props.pointOfInterestComponent,
        component: props.pointOfInterestComponent.component,
        camera: props.camera,
        customShapeRepresentation: {
            customShapeType: "PERSPECTIVE_CAMERA",
            transformationMatrix: computed<Matrix4>(() => props.camera.transformationMatrix),
            perspectiveCamera: computed<PerspectiveCamera>(() => {
                const {fov, aspect, near, far} = tExtractParametersFromProjectionMatrixOfPerspectiveCamera(props.camera.projectionMatrix);
                return new PerspectiveCamera(fov, aspect, clippingPlaneNear, clippingPlaneFar)
            }),
            showIPhoneShape: props.showIPhoneShape,
        } satisfies CustomPerspectiveCameraShapeRepresentation,
    }
</script>

<style scoped>
</style>