<template>
    <t-renderer-webgl v-if="renderer.renderer === 'WEBGL'"
                      ref="tRendererWebgl"
                      :cursor="renderer.cursor.value ?? undefined"
                      :effects="renderer.effects"
                      :needs-image-download="needsImageDownload"
                      :raycasters="renderer.raycasters"
                      :redraw-counter="renderer.rerenderCounter.value"
                      :renderer-id="id"
                      :show-info="renderer.showInfo"
                      :show-signature="showSignature"
                      :show-stats="renderer.showStats"
                      :signature-position="signaturePosition"
                      :tone-mapping="renderer.toneMapping"
                      :tone-mapping-exposure="1"
                      shadow-map>
        <t-scene :background-color="0xe0e2e8"
                 :show-axis="renderer.showAxis"
                 :show-grid="renderer.showGrid"
                 :show-poles="renderer.showPoles">
            <!-- ### KAMERA ### -->
            <!-- TODO: culling feintunen -->
            <t-orthographic-camera v-if="renderer.renderType === '2D'"
                                   :far="renderer.debugScene ? 999999 : 100"
                                   :near="0.01"
                                   :position="cameraPosition2D"
                                   :target="cameraTarget2D"
                                   :zoom="30">
                <t-orbit-controls :disable-rotate="!renderer.debugScene"/>
            </t-orthographic-camera>

            <t-perspective-camera v-else-if="renderer.renderType === '3D'"
                                  :far="BUILDING_SCAN_3D_CAMERA_FAR"
                                  :position="cameraPosition3D"
                                  :target="cameraTarget3D">
                <t-orbit-controls/>
            </t-perspective-camera>

            <!-- ### SKY ### -->
            <t-sky v-if="renderer.showSky"
                   :daytime="isDark ? 'NIGHT' : 'DAY'"
                   :offset-degrees="-renderer.building.value.rotationYCorrectionAngle"/>

            <!-- ### GRID ### -->
            <template v-if="renderer.renderType === '2D' && renderer.id !== '2D_SVG' && renderer.snappingManager">
                <d-l-b-r-grid/>
                <d-l-b-r-snapping-crosshair v-if="renderer.wallCreator"/>
            </template>

            <t-group v-if="renderer.id !== '2D_SVG'"
                     :transformation="new Matrix4().makeRotationY(MathUtils.degToRad(-renderer.building.value.rotationYCorrectionAngle))"
                     :visible="renderer.showCompass.value">
                <d-l-b-r-compass :bounding-box="boundingBox"/>
            </t-group>

            <!--            <t-mesh>-->
            <!--                <t-lines :color="0xFF00FF"-->
            <!--                         :vertices="[-->
            <!--                    new Vector3(0,-1,0),-->
            <!--                    new Vector3(trueNorthDirection.x, -1, trueNorthDirection.y),-->
            <!--                ]"-->
            <!--                         type="LINE"/>-->
            <!--            </t-mesh>-->

            <!-- ### BUILDING ### -->
            <!-- :transformation="renderer.alignBuildingToGrid.value ? new Matrix4().identity() : new Matrix4().makeRotationY(MathUtils.degToRad(renderer.building.value.rotationYCorrectionAngle))" -->
            <t-group v-model:bounding-box="boundingBox"
                     :bounding-box-invalidation-counter="renderer.buildingShapeComputationCounter.value"
                     fit-to-camera
                     hide-until-first-fit-to-camera
                     name="buildingWrapper">
                <d-l-b-r-building :building="renderer.building.value"/>
            </t-group>

            <!-- WALL CREATOR -->
            <d-l-b-r-temp-wall v-if="renderer.renderType === '2D' && renderer.id !== '2D_SVG'"/>

            <!-- TEMP ROOF AREA -->
            <d-l-b-r-temp-roof-area v-if="renderer.renderType === '3D'"/>

            <!-- POI MODE -->
            <d-l-b-r-point-of-interest-intersection v-if="renderer.renderType === '3D'"/>

            <!-- ### LIGHTS ### -->
            <d-l-b-r-lights v-if="renderer.showRectAreaWorldLights"
                            :bounding-box="boundingBox"/>
            <t-ambient-light v-if="renderer.showAmbientLight"
                             :intensity="1.25"/>

            <!--            TO NOT DELETE THIS, FOR DEBUGGING-->
            <!-- ### DEBUG ### -->
            <!--            <template v-if="renderer.debugOcclusionCulling">-->
            <!--                <t-points :color="0xFF00FF"-->
            <!--                          :depth-test="false"-->
            <!--                          :render-order="10000"-->
            <!--                          :size="2"-->
            <!--                          :vertices="occlusionCullingRaySourcePositions"-->
            <!--                          transparent/>-->
            <!--            </template>-->

            <!--            <template v-if="renderer.debugSnapping">-->
            <!--                <t-points :color="0xFF00FF"-->
            <!--                          :depth-test="false"-->
            <!--                          :render-order="100000"-->
            <!--                          :size="20"-->
            <!--                          :vertices="intersectionPointVertices"-->
            <!--                          add-to-scene-->
            <!--                          transparent/>-->

            <!--                <template v-if="true">-->
            <!--                    <t-lines2 v-for="(lvs, index) in lineVertices"-->
            <!--                              :key="index"-->
            <!--                              :depth-test="false"-->
            <!--                              :material="material_000!"-->
            <!--                              :render-order="99999"-->
            <!--                              :vertices="lvs"-->
            <!--                              add-to-scene/>-->
            <!--                </template>-->
            <!--            </template>-->

            <!--            <t-lines2 v-for="wallLineSegment in wallLineSegments"-->
            <!--                      :key="wallLineSegment.wallOrOpening.id"-->
            <!--                      :color="tRandomColor(wallLineSegment.wallOrOpening.id)"-->
            <!--                      :depth-test="false"-->
            <!--                      :opacity="0.5"-->
            <!--                      :render-order="10000"-->
            <!--                      :size="10"-->
            <!--                      :vertices="wallLineSegment.vertices"-->
            <!--                      transparent/>-->

            <!--            <t-points :color="0xFF0000"-->
            <!--                      :depth-test="false"-->
            <!--                      :opacity="0.5"-->
            <!--                      :render-order="10001"-->
            <!--                      :size="renderer.renderType === '2D' ? 25 : 0.5"-->
            <!--                      :vertices="wallLineSegmentIntersectionPointVectors"-->
            <!--                      transparent/>-->

            <!--            <template v-if="true">-->
            <!--                <template v-if="true">-->
            <!--                    <t-mesh v-for="(roomShape, index) in roomShapes"-->
            <!--                            :key="index"-->
            <!--                            :render-order="10002000"-->
            <!--                            :transformation="new Matrix4().makeTranslation(0, indexToY(index), 0).multiply(new Matrix4().makeRotationX(Math.PI / 2))"-->
            <!--                            add-to-scene-->
            <!--                            transparent>-->
            <!--                        <t-mesh-basic-material :color="tRandomColor(String(index + 20))"-->
            <!--                                               :depth-test="false"-->
            <!--                                               :opacity="0.1"-->
            <!--                                               :side="2"-->
            <!--                                               transparent/>-->

            <!--                        <t-geometry-raw :geometry="roomShape"/>-->
            <!--                    </t-mesh>-->
            <!--                </template>-->

            <!--                <template v-if="true">-->
            <!--                    <t-lines2 v-for="(roomVertices, index) in roomVertices3D"-->
            <!--                              :key="index"-->
            <!--                              :depth-test="false"-->
            <!--                              :material="roomMaterials[index]"-->
            <!--                              :render-order="10002001"-->
            <!--                              :size="2"-->
            <!--                              :vertices="roomVertices"-->
            <!--                              add-to-scene-->
            <!--                              transparent/>-->
            <!--                </template>-->

            <!--                <template v-if="false">-->
            <!--                    <t-mesh v-for="(wallTransformation, index) in wallTransformations"-->
            <!--                            :key="index"-->
            <!--                            :index="index"-->
            <!--                            :render-order="10002004"-->
            <!--                            :transformation="wallTransformation"-->
            <!--                            is-billboard>-->
            <!--                        <t-text-geometry :size="0.15"-->
            <!--                                         :text="String(index)"/>-->
            <!--                        <t-mesh-basic-material :color="0xFF0000"-->
            <!--                                               :depth-test="false"-->
            <!--                                               :side="2"-->
            <!--                                               transparent/>-->

            <!--                        <t-mesh :render-order="10002003">-->
            <!--                            <t-plane-geometry :height="0.3"-->
            <!--                                              :width="0.3"/>-->
            <!--                            <t-mesh-basic-material :color="0xFFFFFF"-->
            <!--                                                   :depth-test="false"-->
            <!--                                                   :side="2"-->
            <!--                                                   transparent/>-->
            <!--                        </t-mesh>-->
            <!--                    </t-mesh>-->
            <!--                </template>-->

            <!--                <template v-if="false">-->
            <!--                    <t-mesh v-for="(vertexTransformation, index) in verticesWorld[1]"-->
            <!--                            :key="index"-->
            <!--                            :index="index"-->
            <!--                            :render-order="10002004"-->
            <!--                            :transformation="vertexTransformation"-->
            <!--                            is-billboard>-->
            <!--                        <t-text-geometry :size="0.13"-->
            <!--                                         :text="String(verticesWorld[0][index])"/>-->
            <!--                        <t-mesh-basic-material :color="0xFFFFFF"-->
            <!--                                               :depth-test="false"-->
            <!--                                               :side="2"-->
            <!--                                               transparent/>-->

            <!--                        <t-mesh :render-order="10002003">-->
            <!--                            <t-plane-geometry :height="0.22"-->
            <!--                                              :width="0.22"/>-->
            <!--                            <t-mesh-basic-material :color="0xFF0000"-->
            <!--                                                   :depth-test="false"-->
            <!--                                                   :side="2"-->
            <!--                                                   transparent/>-->
            <!--                        </t-mesh>-->
            <!--                    </t-mesh>-->
            <!--                </template>-->

            <!--                <template v-if="true">-->
            <!--                    <t-mesh v-for="(room, roomIndex) in roomCalculator.rooms.value"-->
            <!--                            :key="roomIndex"-->
            <!--                            :index="roomIndex"-->
            <!--                            :render-order="10002006"-->
            <!--                            :transformation="roomTransformations[roomIndex]"-->
            <!--                            is-billboard>-->
            <!--                        <t-text-geometry :size="0.2"-->
            <!--                                         :text="String(roomIndex+1)"/>-->
            <!--                        <t-mesh-basic-material :color="0xFFFFFF"-->
            <!--                                               :depth-test="false"-->
            <!--                                               :side="2"-->
            <!--                                               transparent/>-->

            <!--                        <t-mesh :render-order="10002005">-->
            <!--                            <t-plane-geometry :height="0.4"-->
            <!--                                              :width="0.4"/>-->
            <!--                            <t-mesh-basic-material :color="0x000000"-->
            <!--                                                   :depth-test="false"-->
            <!--                                                   :side="2"-->
            <!--                                                   transparent/>-->
            <!--                        </t-mesh>-->

            <!--                        <template v-if="true">-->
            <!--                            <t-lines2 v-for="(wallLines, wlIndex) in roomWallLines[roomIndex]"-->
            <!--                                      :key="wlIndex"-->
            <!--                                      :color="roomColors[roomIndex]"-->
            <!--                                      :depth-test="false"-->
            <!--                                      :render-order="10002000"-->
            <!--                                      :size="2"-->
            <!--                                      :vertices="wallLines"-->
            <!--                                      add-to-scene-->
            <!--                                      transparent/>-->

            <!--                            <template v-if="false">-->
            <!--                                <t-mesh v-for="(roomWallLineTransformation, index) in roomWallLineTransformations[roomIndex]"-->
            <!--                                        :key="index"-->
            <!--                                        :index="index"-->
            <!--                                        :render-order="10002001"-->
            <!--                                        :transformation="roomWallLineTransformation"-->
            <!--                                        add-to-scene-->
            <!--                                        is-billboard>-->
            <!--                                    <t-text-geometry :size="0.15"-->
            <!--                                                     :text="String(index)"/>-->
            <!--                                    <t-mesh-basic-material :color="roomColors[roomIndex]"-->
            <!--                                                           :depth-test="false"-->
            <!--                                                           :side="2"-->
            <!--                                                           transparent/>-->
            <!--                                </t-mesh>-->
            <!--                            </template>-->
            <!--                        </template>-->
            <!--                    </t-mesh>-->
            <!--                </template>-->
            <!--            </template>-->

            <!--            <template v-if="false">-->
            <!--                <template v-if="true">-->
            <!--                    <t-mesh v-for="roomShapeGeometry in room1ShapeGeometries"-->
            <!--                            :key="roomShapeGeometry.uuid"-->
            <!--                            :render-order="10002000"-->
            <!--                            :transformation="new Matrix4().makeRotationX(-Math.PI/2)"-->
            <!--                            :visible="renderer.visibleFloorLevels.value.has(0)"-->
            <!--                            add-to-scene>-->
            <!--                        <t-geometry-raw :geometry="roomShapeGeometry"/>-->
            <!--                        <t-mesh-basic-material :color="0x00FF00"-->
            <!--                                               :depth-test="false"-->
            <!--                                               :opacity="0.5"-->
            <!--                                               :side="2"-->
            <!--                                               transparent/>-->
            <!--                    </t-mesh>-->
            <!--                </template>-->

            <!--                <t-mesh v-for="roomShapeGeometry in room2ShapeGeometries"-->
            <!--                        :key="roomShapeGeometry.uuid"-->
            <!--                        :render-order="10002001"-->
            <!--                        :transformation="new Matrix4().makeRotationX(-Math.PI/2)"-->
            <!--                        :visible="renderer.visibleFloorLevels.value.has(1)"-->
            <!--                        add-to-scene>-->
            <!--                    <t-geometry-raw :geometry="roomShapeGeometry"/>-->
            <!--                    <t-mesh-basic-material :color="0xFF0000"-->
            <!--                                           :depth-test="false"-->
            <!--                                           :opacity="0.5"-->
            <!--                                           :side="2"-->
            <!--                                           transparent/>-->
            <!--                </t-mesh>-->
            <!--            </template>-->

            <!--            <template v-if="true">-->
            <!--                <template v-for="wallPoint in exteriorWallPoints"-->
            <!--                          :key="wallPoint.wall.id">-->
            <!--                    <t-points :color="exteriorWallIntersections.get(wallPoint.wall.id) === undefined ? 0x0000FF : (exteriorWallIntersections.get(wallPoint.wall.id) === true ? 0xFFFFFF : 0xFF0000)"-->
            <!--                              :depth-test="false"-->
            <!--                              :render-order="99999999"-->
            <!--                              :size="20"-->
            <!--                              :vertices="[wallPoint.vertex3D]"-->
            <!--                              add-to-scene-->
            <!--                              transparent/>-->
            <!--                </template>-->
            <!--            </template>-->

            <!--            <template v-if="true">-->
            <!--                <t-mesh v-for="room in roomVertices"-->
            <!--                        :key="room.room.id"-->
            <!--                        :render-order="9999999"-->
            <!--                        :transformation="new Matrix4().makeRotationX(-Math.PI / 2)"-->
            <!--                        add-to-scene>-->
            <!--                    <t-shape-geometry :points="room.vertices2D"/>-->

            <!--                    <t-mesh-basic-material :color="0xFF00FF"-->
            <!--                                           :depth-test="false"-->
            <!--                                           :opacity="0.75"-->
            <!--                                           :side="2"-->
            <!--                                           transparent/>-->
            <!--                </t-mesh>-->
            <!--            </template>-->
        </t-scene>
    </t-renderer-webgl>
    <t-renderer-svg v-else-if="renderer.renderer === 'SVG'"
                    ref="tRendererSvg"
                    :cursor="renderer.cursor.value ?? undefined"
                    :effects="renderer.effects"
                    :needs-image-download="needsImageDownload"
                    :raycasters="renderer.raycasters"
                    :redraw-counter="renderer.rerenderCounter.value"
                    :renderer-id="id"
                    :show-info="renderer.showInfo"
                    :show-signature="showSignature"
                    :show-stats="renderer.showStats"
                    :signature-position="signaturePosition"
                    :tone-mapping="renderer.toneMapping"
                    :tone-mapping-exposure="1"
                    shadow-map>
        <t-scene :background-color="0xe0e2e8"
                 :scale="2"
                 :show-axis="renderer.showAxis"
                 :show-grid="renderer.showGrid"
                 :show-poles="renderer.showPoles">
            <!-- ### KAMERA ### -->
            <!-- TODO: culling feintunen -->
            <t-orthographic-camera :far="100"
                                   :near="0.01"
                                   :position="cameraPosition2D"
                                   :target="cameraTarget2D"
                                   :zoom="30"/>

            <!-- ### SKY ### -->
            <t-sky v-if="renderer.showSky"
                   :daytime="isDark ? 'NIGHT' : 'DAY'"
                   :offset-degrees="-renderer.building.value.rotationYCorrectionAngle"/>

            <!-- ### GRID ### -->
            <template v-if="renderer.renderType === '2D' && renderer.id !== '2D_SVG' && renderer.snappingManager">
                <d-l-b-r-grid/>
                <d-l-b-r-snapping-crosshair v-if="renderer.wallCreator"/>
            </template>

            <t-group v-if="renderer.id !== '2D_SVG'"
                     :transformation="new Matrix4().makeRotationY(MathUtils.degToRad(-renderer.building.value.rotationYCorrectionAngle))"
                     :visible="renderer.showCompass.value">
                <d-l-b-r-compass :bounding-box="boundingBox"/>
            </t-group>

            <!-- ### BUILDING ### -->
            <t-group v-model:bounding-box="boundingBox"
                     :bounding-box-invalidation-counter="renderer.buildingShapeComputationCounter.value"
                     fit-to-camera
                     hide-until-first-fit-to-camera
                     name="buildingWrapper">
                <d-l-b-r-building :building="renderer.building.value"/>
            </t-group>

            <!-- WALL CREATOR -->
            <d-l-b-r-temp-wall v-if="renderer.renderType === '2D' && renderer.id !== '2D_SVG'"/>

            <!-- TEMP ROOF AREA -->
            <d-l-b-r-temp-roof-area v-if="renderer.renderType === '3D'"/>

            <!-- POI MODE -->
            <d-l-b-r-point-of-interest-intersection v-if="renderer.renderType === '3D'"/>

            <!-- ### LIGHTS ### -->
            <d-l-b-r-lights v-if="renderer.showRectAreaWorldLights"
                            :bounding-box="boundingBox"/>
            <t-ambient-light v-if="renderer.showAmbientLight"
                             :intensity="1.25"/>
        </t-scene>
    </t-renderer-svg>
</template>

<script lang="ts"
        setup>
    import TPerspectiveCamera from "@/adapter/three/components/camera/t-perspective-camera.vue";
    import TScene from "@/adapter/three/components/t-scene.vue";
    import {computed, inject, onUnmounted, ref, shallowRef, toRaw, watch} from "vue";
    import TOrbitControls from "@/adapter/three/components/camera/t-orbit-controls.vue";
    import {Optional} from "@/model/Optional";
    import TSky from "@/adapter/three/components/object/t-sky.vue";
    import {BUILDING_SCAN_3D_CAMERA_FAR} from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-config";
    import TGroup from "@/adapter/three/components/object/t-group.vue";
    import {Box3, BoxGeometry, FrontSide, MathUtils, Matrix4, Mesh, Vector3} from "three";
    import {transformationMatrixOfShapeRepresentation} from "@/components/listing/building-creation/pipeline/building-creation-pipeline";
    import TAmbientLight from "@/adapter/three/components/light/t-ambient-light.vue";
    import DLBRBuilding from "@/components/listing/building/renderer/d-l-b-r-building.vue";
    import DLBRLights from "@/components/listing/building/renderer/d-l-b-r-lights.vue";
    import TOrthographicCamera from "@/adapter/three/components/camera/t-orthographic-camera.vue";
    import DLBRTempWall from "@/components/listing/building/renderer/d-l-b-r-temp-wall.vue";
    import DLBRGrid from "@/components/listing/building/renderer/d-l-b-r-grid.vue";
    import DLBRSnappingCrosshair from "@/components/listing/building/renderer/d-l-b-r-snapping-crosshair.vue";
    import {LineMaterial} from "three/addons/lines/LineMaterial.js";
    import {useColorMode} from "@vueuse/core";
    import {tDecomposeMatrix, tDestroyMesh} from "@/adapter/three/three-utility";
    import DLBRCompass from "@/components/listing/building/renderer/d-l-b-r-compass.vue";
    import DLBRPointOfInterestIntersection from "@/components/listing/building/renderer/d-l-b-r-point-of-interest-intersection.vue";
    import {DBuildingRendererInjection} from "@/components/listing/building/building";
    import DLBRTempRoofArea from "@/components/listing/building/renderer/d-l-b-r-temp-roof-area.vue";
    import TRendererWebgl from "@/adapter/three/components/t-renderer-webgl.vue";
    import TRendererSvg from "@/adapter/three/components/t-renderer-svg.vue";
    // TO NOT DELETE THIS, FOR DEBUGGING
    // import testjson from "@/components/listing/building-model/testdata.json?raw";
    // import rawMeshesFileURL from "@/components/listing/building-model/arMeshAnchors.bin?url";

    const props = defineProps<{
        rendererId: string
        showSignature: boolean
        signaturePosition?: "START" | "END"
        needsImageDownload: boolean
    }>()

    const renderer = inject(DBuildingRendererInjection)!
    const colorMode = useColorMode()
    const isDark = computed<boolean>(() => colorMode.state.value === 'dark')

    const id = `${props.rendererId}-building-${renderer.id}`

    // const roomVertices = computed<readonly {
    //     readonly room: Room,
    //     readonly vertices3D: readonly Vector3[]
    //     readonly vertices2D: readonly Vector2[]
    // }[]>(() => {
    //     const building = renderer.building.value
    //     const buildingTransformation = transformationMatrixOfShapeRepresentation(building.shapeRepresentation)
    //
    //     return building.floors
    //         .filter(f => renderer.visibleFloorLevels.value.has(f.level))
    //         .flatMap(floor => {
    //             const floorTransformation = transformationMatrixOfShapeRepresentation(floor.shapeRepresentation)
    //
    //             return floor.rooms.map(room => {
    //                 const roomTransformation = transformationMatrixOfShapeRepresentation(room.shapeRepresentation)
    //                 const transformation = buildingTransformation.clone().multiply(floorTransformation).multiply(roomTransformation)
    //
    //                 const vertices3D = (room.shapeRepresentation as PolygonShapeRepresentation)
    //                     .shape
    //                     .vertices
    //                     .map(v => new Vector3(v.x, 0, -v.y).applyMatrix4(transformation).applyMatrix4(RoomCalculator.vertexTransformation))
    //                 const vertices2D = vertices3D.map(v => new Vector2(v.x, v.z))
    //
    //                 return {
    //                     room,
    //                     vertices3D,
    //                     vertices2D,
    //                 }
    //             })
    //         })
    // })

    // const forcedOffsetZ = 0.1
    // const exteriorWallPoints = computed<readonly { wall: Wall, vertex3D: Vector3, vertex2D: Vector2 }[]>(() => {
    //     const building = renderer.building.value
    //
    //     return building.floors
    //         .filter(f => renderer.visibleFloorLevels.value.has(f.level))
    //         .flatMap(floor => floor.walls
    //             .filter(w => w.isExterior === true && w.isPartition !== true)
    //             .flatMap(wall => {
    //                 const start = calculateWallOrOpeningSnapPointPositionXZ(renderer, wall, "LEFT", wall, false, forcedOffsetZ)
    //                 const end = calculateWallOrOpeningSnapPointPositionXZ(renderer, wall, "RIGHT", wall, false, forcedOffsetZ)
    //                 const wallCenterWorld = start.clone().add(end).multiplyScalar(0.5)
    //                 return {
    //                     wall,
    //                     vertex2D: wallCenterWorld,
    //                     vertex3D: new Vector3(wallCenterWorld.x, 0, wallCenterWorld.y)
    //                 }
    //             })
    //         )
    // })

    // const exteriorWallIntersections = computed<Map<string, boolean>>(() => {
    //     const intersections = new Map<string, boolean>()
    //     for (const exteriorWallPoint of exteriorWallPoints.value) {
    //         //TODO: ring handling
    //         if (exteriorWallPoint.wall.shapeRepresentation.shape.__typename === "Ring") {
    //             intersections.set(exteriorWallPoint.wall.id, true)
    //             continue
    //         }
    //
    //         if (exteriorWallPoint.wall.roomIds === undefined || exteriorWallPoint.wall.roomIds === null || exteriorWallPoint.wall.roomIds.length <= 0) {
    //             console.warn(`Wall ${exteriorWallPoint.wall.id} has no roomIds`)
    //             intersections.set(exteriorWallPoint.wall.id, false)
    //             continue
    //         }
    //         const roomIds = new Set(exteriorWallPoint.wall.roomIds)
    //         const wallRooms = roomVertices.value.filter(rv => roomIds.has(rv.room.id));
    //         // const isInside = wallRooms.some(wallRoom => d3IsVector2InsidePolygon(exteriorWallPoint.vertex2D, wallRoom.vertices2D.map(v => new Vector2(v.x, -v.y))))
    //         const isInside2 = wallRooms.some(wallRoom => isWorldPointInsideRoom(renderer, wallRoom.room, exteriorWallPoint.vertex2D))
    //
    //         // console.log(`=== WAND ${exteriorWallPoint.wall.id} ===`)
    //         // console.log("Wandzentrum", exteriorWallPoint.vertex2D.toArray())
    //         // console.log("IsInside", isInside, isInside2)
    //         // console.log("Räume:")
    //         // for (const room of wallRooms) {
    //         //     console.log(`\tRaum ${room.room.id}`)
    //         //     console.log("\t- Vertices", room.vertices2D.map(v => v.toArray()))
    //         // }
    //
    //         intersections.set(exteriorWallPoint.wall.id, isInside2)
    //     }
    //     return intersections
    // })

    // const trueNorthDirection = computed<Vector2>(() => ifcCalculateTrueNorthDirectionOfBuilding(renderer.building.value).normalize().multiplyScalar(10))

    // const floor1 = renderer.building.value.floors[0]
    // const floor2 = renderer.building.value.floors[1]

    // const buildingTransformation = transformationMatrixOfShapeRepresentation(renderer.building.value.shapeRepresentation)
    // const floor1Transformation = transformationMatrixOfShapeRepresentation(floor1.shapeRepresentation)
    // const floor2Transformation = transformationMatrixOfShapeRepresentation(floor2.shapeRepresentation)
    //
    // const floor1WorldTransformation = buildingTransformation.clone().multiply(floor1Transformation)
    // const floor2WorldTransformation = buildingTransformation.clone().multiply(floor2Transformation)
    //
    // const vertexTransformation: Matrix4 = (() => {
    //     const rotationMatrix = new Matrix4().makeRotationY(Math.PI)
    //     const scaleMatrix = new Matrix4().makeScale(-1, 1, 1) // "y" is inverted
    //     return rotationMatrix.multiply(scaleMatrix)
    // })()
    //
    // const room1ShapeGeometries = computed<readonly ShapeGeometry[]>(() => floor1.rooms.map(room => {
    //     const roomTransformation = transformationMatrixOfShapeRepresentation(room.shapeRepresentation)
    //     const roomWorldTransformation = floor1WorldTransformation.clone().multiply(roomTransformation)
    //     const worldVertices = (room.shapeRepresentation as PolygonShapeRepresentation).shape.vertices.map(v => new Vector3(v.x, 0, v.y)
    //         .applyMatrix4(vertexTransformation)
    //         .applyMatrix4(roomWorldTransformation)
    //         .applyMatrix4(vertexTransformation.clone().invert()))
    //         .map(v => new Vector2(v.x, v.z))
    //     const shape = new Shape(worldVertices)
    //     shape.closePath()
    //     return new ShapeGeometry(shape)
    // }))
    // const room2ShapeGeometries = computed<readonly ShapeGeometry[]>(() => floor2.rooms.map(room => {
    //     const roomTransformation = transformationMatrixOfShapeRepresentation(room.shapeRepresentation)
    //     const roomWorldTransformation = floor2WorldTransformation.clone().multiply(roomTransformation)
    //     const worldVertices = (room.shapeRepresentation as PolygonShapeRepresentation).shape.vertices.map(v => new Vector3(v.x, 0, v.y)
    //         .applyMatrix4(vertexTransformation)
    //         .applyMatrix4(roomWorldTransformation)
    //         .applyMatrix4(vertexTransformation.clone().invert()))
    //         .map(v => new Vector2(v.x, v.z))
    //     const shape = new Shape(worldVertices)
    //     shape.closePath()
    //     return new ShapeGeometry(shape)
    // }))

    // TO NOT DELETE THIS, FOR DEBUGGING
    //##################################################################

    // const roomCalculator = new RoomCalculator(renderer, renderer.currentFloor, true)
    //
    // function indexToY(index: number) {
    //     return 1.5 + index * 0.1
    // }
    //
    // const roomsVertices2D = computed<readonly (readonly Vector2[])[]>(() => roomCalculator.rooms.value.map(room => room.verticesWorld))
    // const vertexMap = computed<Map<string, number>>(() => {
    //     let vertexIdCounter = 0
    //     const vertexMap = new Map<string, number>()
    //     for (const room of roomCalculator.rooms.value) {
    //         for (const vertex of RoomCalculator.roomIdToVertexSet(room.id)) {
    //             if (!vertexMap.has(vertex)) {
    //                 vertexMap.set(vertex, ++vertexIdCounter)
    //             }
    //         }
    //     }
    //     return vertexMap
    // })
    // const verticesWorld = computed<readonly [readonly number[], readonly Matrix4[]]>(() => ([
    //     Array.from(vertexMap.value.values()),
    //     Array.from(vertexMap.value.keys()).map(v => {
    //         //TODO: das ist noch falsch
    //         // const buildingTransformation = transformationMatrixOfShapeRepresentation(renderer.building.value.shapeRepresentation)
    //         // const floorTransformation = renderer.currentFloor.value ? transformationMatrixOfShapeRepresentation(renderer.currentFloor.value.shapeRepresentation) : new Matrix4().identity()
    //         const [x, y] = v.split(RoomCalculator.ROOM_VERTEX_SEPARATOR).map(Number)
    //         const vertexTransformation = new Matrix4().makeTranslation(x / 100, 0, y / 100)
    //         // return buildingTransformation.clone().multiply(floorTransformation).multiply(vertexTransformation)
    //         return vertexTransformation
    //     })
    // ]))
    // const roomVertices3D = computed<readonly (readonly Vector3[])[]>(() => roomsVertices2D.value.map((cycle, index) => cycle.map(p => new Vector3(p.x, indexToY(index), p.y))))
    // const roomShapes = computed<readonly ShapeGeometry[]>(() => roomsVertices2D.value.map(roomVertices => new ShapeGeometry(new Shape([...roomVertices]))))
    // const roomCentersWorld = computed<readonly Vector3[]>(() => roomCalculator.rooms.value.map((room, index) => new Vector3(
    //     room.centerWorld.x,
    //     indexToY(index),
    //     room.centerWorld.y
    // )))
    // const roomTransformations = computed<readonly Matrix4[]>(() => roomCentersWorld.value.map(center => {
    //     const transformation = new Matrix4()
    //     transformation.makeTranslation(center)
    //     return transformation
    // }))
    // const roomWallLines = computed<readonly (readonly (readonly [Vector3, Vector3])[])[]>(() => roomCalculator.rooms.value.map((room, roomIndex) => {
    //     return room.wallIds
    //         .map(wallId => {
    //             const extendedWall = roomCalculator.extendedWalls.value.find(w => w.wall.id === wallId)
    //             if (extendedWall === undefined) {
    //                 return null
    //             }
    //             const wall = extendedWall.wall
    //             const start = calculateWallOrOpeningSnapPointPositionXZ(renderer, wall, "LEFT", wall)
    //             const end = calculateWallOrOpeningSnapPointPositionXZ(renderer, wall, "RIGHT", wall)
    //             const center = start.clone().add(end).multiplyScalar(0.5)
    //             const roomCenter = roomCentersWorld.value[roomIndex]
    //             return [
    //                 new Vector3(center.x, 0, center.y),
    //                 roomCenter
    //             ] satisfies [Vector3, Vector3]
    //         })
    //         .filter(l => l !== null)
    // }))
    // const roomWallLineCenters = computed<readonly (readonly Vector3[])[]>(() => roomWallLines.value.map(roomWallLines => roomWallLines.map(([start, end]) => start.clone().add(end).multiplyScalar(0.5))))
    // const roomWallLineTransformations = computed<readonly (readonly Matrix4[])[]>(() => roomWallLineCenters.value.map(roomWallLineCenters => roomWallLineCenters.map(center => {
    //     const transformation = new Matrix4()
    //     transformation.makeTranslation(center)
    //     return transformation
    // })))
    // const wallTransformations = computed<readonly Matrix4[]>(() => roomCalculator.extendedWalls.value.map(wall => {
    //     const startVertex = calculateWallOrOpeningSnapPointPositionXZ(renderer, wall.wall, "LEFT", wall.wall)
    //     const endVertex = calculateWallOrOpeningSnapPointPositionXZ(renderer, wall.wall, "RIGHT", wall.wall)
    //     const center = startVertex.clone().add(endVertex).multiplyScalar(0.5)
    //
    //     const transformation = new Matrix4()
    //     transformation.makeTranslation(center.x, 0, center.y)
    //     return transformation
    // }))
    //
    // const roomColors = computed<readonly Color[]>(() => roomCalculator.rooms.value.map((_, index) => tRandomColor(String(index + 20))))
    // const roomMaterials = computed<readonly LineMaterial[]>(() => roomCalculator.rooms.value.map((_, index) => new LineMaterial({
    //     side: FrontSide,
    //     depthTest: false,
    //     worldUnits: true,
    //     linewidth: 0.15,
    //     transparent: true,
    //     opacity: 0.25,
    //     color: roomColors.value[index],
    // })))

    // const wallLineSegments = computed<readonly LineSegment<Wall | ConstructionPart>[]>(() => {
    //     const floor = renderer.currentFloor.value
    //     if (floor === null) {
    //         console.warn("Floor is null")
    //         return []
    //     }
    //     return wallsOrOpeningsToLineSegments(renderer, floor.walls.flatMap(w => w.openings))
    // })
    // //const wallLineSegmentIntersectionPoints = computed<readonly LineSegmentIntersectionPoint<Wall>[]>(() => calculateWallLineIntersectionPoints(wallLineSegments.value))
    // const wallLineSegmentIntersectionPoints = computed<readonly LineSegmentIntersectionPoint<Wall | ConstructionPart>[]>(() => {
    //     const lineSegments = wallLineSegments.value
    //
    //     return lineSegments.flatMap(ls => {
    //         return [
    //             {
    //                 intersectionPoint: new Vector3(ls.start.x, 0, ls.start.y),
    //                 intersectionPointXZ: ls.start,
    //                 lineSegment1: ls,
    //                 lineSegment2: ls,
    //             },
    //             {
    //                 intersectionPoint: new Vector3(ls.end.x, 0, ls.end.y),
    //                 intersectionPointXZ: ls.end,
    //                 lineSegment1: ls,
    //                 lineSegment2: ls,
    //             }
    //         ]
    //     })
    // })
    // const wallLineSegmentIntersectionPointVectors = computed<readonly Vector3[]>(() => wallLineSegmentIntersectionPoints.value.map(p => p.intersectionPoint))

    //##################################################################

    // const intersectionPointVertices = computed<readonly Vector3[]>(() => {
    //     if (!renderer.debugSnapping) {
    //         return []
    //     }
    //
    //     renderer.snappingManager.refreshCounter.value // trigger reactivity
    //
    //     return renderer.snappingManager.snapLineIntersectionPoints().map(p => new Vector3(p.intersectionPointXZ.x, 0, p.intersectionPointXZ.y))
    // })
    //
    // const lineVertices = computed<readonly (readonly Vector3[])[]>(() => {
    //     if (!renderer.debugSnapping) {
    //         return []
    //     }
    //
    //     renderer.snappingManager.refreshCounter.value // trigger reactivity
    //
    //     console.log("ANTIS", renderer.snappingManager.antiProportionalSnapLineGroups().length)
    //
    //     return renderer.snappingManager.antiProportionalSnapLineGroups().map(g => {
    //         const worldTransformation = worldTransformationOfWallOrOpening(renderer, g.snapLines[0].object.wall.value) //TODO: <<<<<<<<<<<<< ON CHANGE BUILDING + FLOOR ===> eig. egal jetzt, weil funktion und keine computed property mehr
    //         const [worldTranslation] = tDecomposeMatrix(worldTransformation)
    //
    //         const translationMatrix = new Matrix4().makeTranslation(
    //             g.positionXZ.x,
    //             worldTranslation.y,
    //             g.positionXZ.y,
    //         )
    //
    //         const normalizedDirectionXZ = g.normalizedDirectionXZ
    //         const theta = Math.atan2(normalizedDirectionXZ.y, normalizedDirectionXZ.x);
    //         const rotationMatrix = new Matrix4().makeRotationY(-theta)
    //         const transformationMatrix = translationMatrix.multiply(rotationMatrix)
    //
    //         const leftPoint = new Vector3(
    //             -100,
    //             0,
    //             0,
    //         ).applyMatrix4(transformationMatrix)
    //         const rightPoint = new Vector3(
    //             100,
    //             0,
    //             0,
    //         ).applyMatrix4(transformationMatrix)
    //
    //         return [
    //             leftPoint,
    //             rightPoint,
    //         ]
    //         // return [
    //         //     new Vector3(g.positionXZ.x, worldTranslation.y, g.positionXZ.y),
    //         // ]
    //     })
    // })

    // noinspection LocalVariableNamingConventionJS
    const material_000 = computed<Optional<LineMaterial>>(oldMaterial => {
        oldMaterial?.dispose()

        return renderer.debugSnapping
            ? new LineMaterial({
                side: FrontSide,
                depthTest: false,
                worldUnits: true,
                linewidth: 0.02,
                transparent: true,
                opacity: 1,
                dashed: true,
                dashScale: 5,
                dashSize: 1,
                gapSize: 1,
                color: 0x00FF00,
            })
            : null
    })

    const boundingBox = ref<Box3>(new Box3()) //ref okay
    const tRendererWebgl = shallowRef<Optional<typeof TRendererWebgl>>(null)
    const tRendererSvg = shallowRef<Optional<typeof TRendererSvg>>(null)

    //don't use new instances as parameters directly, otherwise they will be rerender everytime one of the parent components change
    const cameraPosition2D = new Vector3(0, 10, 0) //10
    const cameraTarget2D = new Vector3(0, -1, 0) //-1
    const cameraPosition3D = new Vector3(0, 7, 7)
    const cameraTarget3D = new Vector3(0, 0, 0)

    defineExpose({
        generateJPEG: () => tRendererWebgl.value?.generateJPEG(),
        generateSVG: () => tRendererSvg.value?.generateSVG(),
    })

    const occlusionCullingRaySourcePositions = computed<readonly Vector3[]>(() => {
        const building = toRaw(renderer.building.value);
        const buildingTransformation = transformationMatrixOfShapeRepresentation(building.shapeRepresentation)

        const floorLevels = renderer.visibleFloorLevels.value
        return building.floors
            .filter(f => floorLevels.has(f.level))
            .flatMap(floor => {
                const floorTransformation = transformationMatrixOfShapeRepresentation(floor.shapeRepresentation)

                return floor.rooms.flatMap(room => {
                    const roomTransformation = transformationMatrixOfShapeRepresentation(room.shapeRepresentation)
                    const transformation = buildingTransformation.clone().multiply(floorTransformation).multiply(roomTransformation)

                    const [translation] = tDecomposeMatrix(transformation)

                    return translation
                })
            })
    })

    function removeOldRaySourceMeshes() {
        for (const raySourceMeshes of renderer.occlusionCullingRaycaster.raySourceMeshes) {
            tDestroyMesh(raySourceMeshes, true, true)
        }
    }

    onUnmounted(() => {
        material_000.value?.dispose()
        // roomCalculator.destroy()

        removeOldRaySourceMeshes()
    })

    watch(occlusionCullingRaySourcePositions, positions => {
        removeOldRaySourceMeshes()

        renderer.occlusionCullingRaycaster.raySourceMeshes = positions.map(position => {
            const mesh = new Mesh(new BoxGeometry())
            const translation = new Matrix4().makeTranslation(position.x, position.y, position.z)
            mesh.applyMatrix4(translation)
            return mesh
        })
    }, {
        immediate: true,
        deep: true
    })
</script>

<style scoped>
</style>