import {BuildingRenderer} from "@/components/listing/building/renderer/BuildingRenderer";
import {TSnapping<PERSON>anager, TSnappingResult} from "@/adapter/three/snapping/TSnappingManager";
import {BUILDING_EPSILON, WallSnapLine, WallSnapLineTemplate, WallSnapPoint, WallSnapPointTemplate} from "@/components/listing/building/building";
import {Optional} from "@/model/Optional";
import {Floor, Wall} from "@/adapter/graphql/generated/graphql";
import {computed, ComputedRef, effectScope, shallowRef, watch} from "vue";
import {Vector2} from "three";
import {TLineIntersectionPoint} from "@/adapter/three/snapping/TLineIntersectionPoint";
import {WallCreator} from "@/components/listing/building/renderer/WallCreator";
import {TSnapLine} from "@/adapter/three/snapping/TSnapLine";

export class SnappingManager {
    /**
     * Durchmesser in m
     */
    public static readonly WALL_SNAP_POINT_SIZE_BIG = 0.25 //25 cm
    private static readonly SNAPPING_MAX_RANGE = 10 //10 m
    private static readonly SNAP_GROUP_MAX_DISTANCE = 0.01 //1 cm
    private static readonly SNAP_GRID_SIZE_BIG = 0.1 //10 cm
    private static readonly SNAP_GRID_SIZE_SMALL = 0.01 //1 cm
    private static readonly WALL_SNAP_LINE_RANGE_BIG = 0.15 //15 cm
    private static readonly WALL_SNAP_LINE_RANGE_SMALL = 0.05 //5 cm
    /**
     * Durchmesser in m
     */
    private static readonly WALL_SNAP_POINT_SIZE_SMALL = 0.1 //10 cm
    public refreshCounter!: ComputedRef<Optional<number>>
    public snapGridSize!: ComputedRef<number>
    public isSnappingEnabled!: ComputedRef<boolean>
    public isGroupingEnabled!: ComputedRef<boolean>
    public readonly floorsChangedCounter = shallowRef<number>(0)
    public isSpecialModeEnabled = false
    private readonly internalIsSnappingEnabled = shallowRef<boolean>(true)
    private readonly internalIsGroupingEnabled = shallowRef<boolean>(true)
    private readonly floorIdToSnappingManager = new Map<string, TSnappingManager<WallSnapPointTemplate, WallSnapLineTemplate>>()
    private readonly floorLevelToFloorId = new Map<number, string>()
    private readonly reactivityScope = effectScope(true)
    private readonly renderer: WeakRef<BuildingRenderer>
    private autoRefresh = true

    constructor(renderer: BuildingRenderer) {
        this.renderer = new WeakRef(renderer)

        this.reactivityScope.run(() => {
            this.refreshCounter = computed<Optional<number>>(() => this.executeOnSnappingManager(null, s => s.refreshCounter.value))
            this.snapGridSize = computed<number>(() => this.executeOnSnappingManager(null, sm => sm.snapGridSize.value) ?? SnappingManager.SNAP_GRID_SIZE_SMALL)
            this.isSnappingEnabled = computed<boolean>(() => this.internalIsSnappingEnabled.value)
            this.isGroupingEnabled = computed<boolean>(() => this.internalIsGroupingEnabled.value)

            watch(() => renderer.building.value.floors, (newFloors, oldFloors) => {
                if (!this.isSpecialModeEnabled) {
                    this.refresh(oldFloors ?? [], newFloors)
                }
            }, {
                immediate: true,
            })
        })
    }

    public isAutoRefreshEnabled(floorLevel: Optional<number>): boolean {
        return this.executeOnSnappingManager(floorLevel, s => s.autoRefresh) ?? false
    }

    public internalSnapPointGroups(floorLevel: Optional<number>, ignoreFixed: boolean): WallSnapPoint[][] {
        return this.executeOnSnappingManager(floorLevel, s => s.internalSnapPointGroups(ignoreFixed)) ?? []
    }

    updatePositionXZAndDirectionXZForLine(
        wall: Wall,
        snapLineId: string,
        positionXZ: Vector2,
        normalizedDirectionXZ: Vector2,
        preventRefresh: boolean,
    ) {
        const floorLevel = this.renderer.deref()?.traversableBuilding.floorOf(wall)?.level ?? null
        if (floorLevel === null) {
            console.warn("Floor level not found for wall", wall)
        }
        return this.executeOnSnappingManager(floorLevel, sm => sm.updatePositionXZAndDirectionXZForLine(
            snapLineId,
            positionXZ,
            normalizedDirectionXZ,
            preventRefresh,
        ))
    }

    addSnapLine(snapLine: WallSnapLine) {
        //die temp wall snap lines müssen in allen snappingManagern hinzugefügt werden
        if (snapLine.object.wall.value.id === WallCreator.TEMP_WALL_ID) {
            for (const snappingManager of this.floorIdToSnappingManager.values()) {
                //Wird ein Stockwerk gelöscht, wird die refresh-Funktion dieser Klasse ausgelöst, was floorsChangedCounter erhöht.
                //Das sorgt dafür, dass die TEMP_WALL neu gezeichnet wird (siehe d-l-b-r-temp-wall.vue).
                //Wird eine Wand neu gezeichnet, wird die SnapLine neu hinzugefügt, was diese Funktion auslöst.
                //Da die SnapLine für die weiterhin existenten Stockwerke bei den dazugehörigen Snapping Manager doppelt hinzugefügt werden würde,
                //müssen wir zunächst die SnapLine entfernen, falls diese bereits existierte (was die Regel sein sollte in diesem Fall).
                snappingManager.removeSnapLineById(snapLine.id)
                snappingManager.addSnapLine(snapLine)
            }
            return
        }

        const floorLevelOfWall = this.renderer.deref()?.traversableBuilding.floorOf(snapLine.object.wall.value)?.level ?? null
        if (floorLevelOfWall === null) {
            console.warn("Floor level not found for snap line", snapLine)
        }
        return this.executeOnSnappingManager(floorLevelOfWall, sm => sm.addSnapLine(snapLine))
    }

    removeSnapLineById(snapLineId: string) {
        //wir müssen alle snappingManager iterieren, weil der floor nicht mehr ermittelt werden kann, wenn eine komponente bereits gelöscht wurde
        for (const snappingManger of this.floorIdToSnappingManager.values()) {
            snappingManger.removeSnapLineById(snapLineId)
        }
    }

    removeSnapPointById(snapPointId: string) {
        //wir müssen alle snappingManager iterieren, weil der floor nicht mehr ermittelt werden kann, wenn eine komponente bereits gelöscht wurde
        for (const snappingManger of this.floorIdToSnappingManager.values()) {
            snappingManger.removeSnapPointById(snapPointId)
        }
    }

    snapLineIntersectionPoints(): readonly TLineIntersectionPoint<TSnapLine<WallSnapLineTemplate>>[] {
        return this.executeOnSnappingManager(null, s => s.snapLineIntersectionPoints) ?? []
    }

    addSnapPoint(snapPoint: WallSnapPoint) {
        //die temp wall snap points müssen in allen snappingManagern hinzugefügt werden
        if (snapPoint.object.wall.value.id === WallCreator.TEMP_WALL_ID) {
            for (const snappingManager of this.floorIdToSnappingManager.values()) {
                snappingManager.addSnapPoint(snapPoint)
            }
            return
        }

        const floorLevelOfWall = this.renderer.deref()?.traversableBuilding.floorOf(snapPoint.object.wall.value)?.level ?? null
        if (floorLevelOfWall === null) {
            console.warn("Floor level not found for snap point", snapPoint)
        }
        return this.executeOnSnappingManager(floorLevelOfWall, sm => sm.addSnapPoint(snapPoint))
    }

    sizeOfSnapPoint(snapPoint: WallSnapPoint): number {
        const floorLevelOfWall = this.renderer.deref()?.traversableBuilding.floorOf(snapPoint.object.wall.value)?.level ?? null
        if (floorLevelOfWall === null) {
            console.warn("Floor level not found for snap point", snapPoint)
        }
        return this.executeOnSnappingManager(floorLevelOfWall, sm => sm.sizeOfSnapPoint(snapPoint)) ?? 0
    }

    destroy() {
        this.reactivityScope.stop()

        for (const snappingManager of this.floorIdToSnappingManager.values()) {
            snappingManager.destroy()
        }

        this.floorLevelToFloorId.clear()
        this.floorIdToSnappingManager.clear()
    }

    setSnappingSize(size: "SMALL" | "BIG") {
        const isBigEnabled = size === "BIG"
        return this.executeOnSnappingManager(null, snappingManager => {
            snappingManager.snapGridSize.value = isBigEnabled ? SnappingManager.SNAP_GRID_SIZE_BIG : SnappingManager.SNAP_GRID_SIZE_SMALL
            snappingManager.snapPointSize.value = isBigEnabled ? SnappingManager.WALL_SNAP_POINT_SIZE_BIG : SnappingManager.WALL_SNAP_POINT_SIZE_SMALL
            snappingManager.snapLineRange.value = isBigEnabled ? SnappingManager.WALL_SNAP_LINE_RANGE_BIG : SnappingManager.WALL_SNAP_LINE_RANGE_SMALL
        })
    }

    setSnappingEnabled(isSnappingEnabled: boolean) {
        this.internalIsSnappingEnabled.value = isSnappingEnabled

        for (const snappingManager of this.floorIdToSnappingManager.values()) {
            if (snappingManager === undefined) {
                continue
            }
            snappingManager.isSnappingEnabled = isSnappingEnabled
        }
    }

    setGroupingEnabled(isGroupingEnabled: boolean) {
        this.internalIsGroupingEnabled.value = isGroupingEnabled

        for (const snappingManager of this.floorIdToSnappingManager.values()) {
            if (snappingManager === undefined) {
                continue
            }
            snappingManager.isGroupingEnabled = isGroupingEnabled
        }
    }

    updatePositionXZ(wall: Wall, snapPointId: string, positionXZ: Vector2, triggerPositionChanged: boolean, preventRefresh: boolean) {
        const floorLevel = this.renderer.deref()?.traversableBuilding.floorOf(wall)?.level ?? null
        if (floorLevel === null) {
            console.warn("Floor level not found for wall", wall)
        }
        this.executeOnSnappingManager(floorLevel, sm => sm.updatePositionXZ(snapPointId, positionXZ, triggerPositionChanged, preventRefresh))
    }

    updatePositionXZForGroup(wall: Wall, snapPointId: string, positionXZ: Vector2): WallSnapPoint[] {
        const floorLevel = this.renderer.deref()?.traversableBuilding.floorOf(wall)?.level ?? null
        if (floorLevel === null) {
            console.warn("Floor level not found for wall", wall)
        }
        return this.executeOnSnappingManager(floorLevel, sm => sm.updatePositionXZForGroup(snapPointId, positionXZ)) ?? []
    }

    setAutoRefresh(autoRefresh: boolean) {
        this.autoRefresh = autoRefresh

        //das muss für alle snappingManager gemacht werden, weil sich snap points fremder stockwerke auch ändern können (z.b. beim invalidieren des building shapes)
        for (const snappingManager of this.floorIdToSnappingManager.values()) {
            if (snappingManager === undefined) {
                continue
            }
            snappingManager.autoRefresh = autoRefresh
        }
    }

    snapPositionXZ(
        positionXZ: Vector2,
        ignoredSnapPointIds: ReadonlySet<string>,
        ignoredSnapLineIds: ReadonlySet<string>,
        snapToGridOnly?: boolean,
        snapGridSize?: number,
    ): Optional<TSnappingResult<WallSnapPointTemplate, WallSnapLineTemplate>> {
        return this.executeOnSnappingManager(null, sm => sm.snapPositionXZ(
            positionXZ,
            ignoredSnapPointIds,
            ignoredSnapLineIds,
            snapToGridOnly,
            snapGridSize,
        ))
    }

    forceRefresh() {
        return this.executeOnSnappingManager(null, sm => sm.forceRefresh())
    }

    forceRefreshAll() {
        for (const snappingManager of this.floorIdToSnappingManager.values()) {
            snappingManager.forceRefresh()
        }
    }

    snapPointIdToSnapPointsOfSameGroup(wall: Wall, snapPointId: string, ignoreFixed: boolean): WallSnapPoint[] {
        const floorLevel = this.renderer.deref()?.traversableBuilding.floorOf(wall)?.level ?? null
        if (floorLevel === null) {
            console.warn("Floor level not found for wall", wall)
        }
        return this.executeOnSnappingManager(floorLevel, sm => sm.snapPointIdToSnapPointsOfSameGroup(snapPointId, ignoreFixed)) ?? []
    }

    matchPositionsXZAnyGroup(positionsXZ: readonly Vector2[], ignoredSnapPointIds: ReadonlySet<string>): boolean {
        return this.executeOnSnappingManager(null, sm => sm.matchPositionsXZAnyGroup(positionsXZ, ignoredSnapPointIds)) ?? false
    }

    positionXZToSnapPoints(positionXZ: Vector2, ignoredSnapPointIds: ReadonlySet<string>): WallSnapPoint[] {
        return this.executeOnSnappingManager(null, sm => sm.positionXZToSnapPoints(positionXZ, ignoredSnapPointIds)) ?? []
    }

    snapPointIdToSnappedSnapPoints(snapPointId: string, floorLevel: number) {
        return this.executeOnSnappingManager(floorLevel, sm => sm.snapPointIdToSnappedSnapPoints(snapPointId)) ?? []
    }

    public removeFloor(floor: Floor) {
        const snappingManager = this.snappingManager(floor.level)
        if (snappingManager !== null) {
            snappingManager.destroy()
        }
        this.floorLevelToFloorId.delete(floor.level)
        this.floorIdToSnappingManager.delete(floor.id)
    }

    public addFloor(floor: Floor) {
        const snappingManager = new TSnappingManager<WallSnapPointTemplate, WallSnapLineTemplate>(
            BUILDING_EPSILON,
            SnappingManager.SNAP_GROUP_MAX_DISTANCE,
            SnappingManager.SNAPPING_MAX_RANGE,
        )
        snappingManager.autoRefresh = this.autoRefresh
        snappingManager.snapGridSize.value = SnappingManager.SNAP_GRID_SIZE_BIG
        snappingManager.snapPointSize.value = SnappingManager.WALL_SNAP_POINT_SIZE_BIG
        snappingManager.snapLineRange.value = SnappingManager.WALL_SNAP_LINE_RANGE_BIG
        snappingManager.isSnappingEnabled = this.internalIsSnappingEnabled.value
        snappingManager.isGroupingEnabled = this.internalIsGroupingEnabled.value

        this.floorLevelToFloorId.set(floor.level, floor.id)
        this.floorIdToSnappingManager.set(floor.id, snappingManager)
    }

    private floorLevelToSnappingManager(floorLevel: number): Optional<TSnappingManager<WallSnapPointTemplate, WallSnapLineTemplate>> {
        const floorId = this.floorLevelToFloorId.get(floorLevel)
        if (floorId === undefined) {
            return null
        }
        const snappingManager = this.floorIdToSnappingManager.get(floorId)
        if (snappingManager === undefined) {
            return null
        }
        return snappingManager
    }

    private refresh(oldFloors: readonly Floor[], newFloors: readonly Floor[]) {
        const addedFloors = [...newFloors].filter(newFloor => oldFloors.find(oldFloor => oldFloor.id === newFloor.id) === undefined)
        //const updatedFloors = [...newFloors].filter(newFloor => oldFloors.find(oldFloor => oldFloor.id === newFloor.id) !== undefined) //noch keine verwendung
        const removedFloors = [...oldFloors].filter(oldFloor => newFloors.find(newFloor => newFloor.id === oldFloor.id) === undefined)

        for (const floor of removedFloors) {
            this.removeFloor(floor)
        }
        for (const floor of addedFloors) {
            this.addFloor(floor)
        }

        if (addedFloors.length > 0 || removedFloors.length > 0) {
            ++this.floorsChangedCounter.value
        }
    }

    private snappingManager(floorLevel: Optional<number>): Optional<TSnappingManager<WallSnapPointTemplate, WallSnapLineTemplate>> {
        const level = floorLevel ?? this.renderer.deref()?.currentFloor.value?.level
        if (level === undefined) {
            return null
        }
        return this.floorLevelToSnappingManager(level)
    }

    private executeOnSnappingManager<T>(floorLevel: Optional<number>, executor: (snappingManager: TSnappingManager<WallSnapPointTemplate, WallSnapLineTemplate>) => T): Optional<T> {
        const sm = this.snappingManager(floorLevel)
        if (sm === null) {
            console.warn("SnappingManager not found for floor level", floorLevel)
            return null
        }
        return executor(sm)
    }
}