import {FrontSide, LinearToneMapping, LineBasicMaterial, LineBasicMaterialParameters, MeshBasicMaterial, MeshBasicMaterialParameters, Vector3} from "three";
import {TMaterial, TMaterialDeclaration} from "@/adapter/three/TMaterial";
import {TSelectionRaycasterEmitterOrConsumer, TSelectionRelated} from "@/adapter/three/raycasting/selection/TSelectionRaycasterEmitterOrConsumer";
import {TOcclusionCullingRaycasterEmitterOrConsumer, TOcclusionCullingRelated} from "@/adapter/three/raycasting/occlusion-culling/TOcclusionCullingRaycasterEmitterOrConsumer";
import {THoverRaycasterEmitterOrConsumer, THoverRelated} from "@/adapter/three/raycasting/hover/THoverRaycasterEmitterOrConsumer";
import {noThicknessOpeningOffsetZ, noThicknessWallOffsetZ} from "@/components/listing/building/building";
import {RenderVisibility} from "@/components/listing/building/renderer/RenderVisibility";
import {Optional} from "@/model/Optional";
import {TMaybeRaycasterRelated} from "@/adapter/three/raycasting/TRaycasterRelated";
import {BuildingRenderer} from "@/components/listing/building/renderer/BuildingRenderer";
import {BuildingComponent, RaycastableBuildingComponent, WallRoofPoint, WallSizeAdjustmentBuildingComponent, WallWithHolesBuildingComponent, WallWithoutHolesBuildingComponent} from "@/components/listing/building/renderer/BuildingComponent";
import {Building} from "@/adapter/graphql/generated/graphql";
import {TDraggableRaycasterEmitterOrConsumer, TDraggableRelated} from "@/adapter/three/raycasting/draggable/TDraggableRaycasterEmitterOrConsumer";
import {TRaycaster} from "@/adapter/three/raycasting/TRaycaster";
import {Ref} from "vue";
import {wallTypeOfWall} from "@/model/building/WallType";
import {TraversalBuildingComponent} from "@/components/listing/building/renderer/TraversableBuilding";

// #################
// ### MATERIALS ###
// #################
const MATERIAL_DOORWAY_LINE = new LineBasicMaterial({
    color: 0x009F64,
    side: FrontSide,
    linewidth: 0.25,
})

// --- DEBUG ---
const MATERIAL_DEBUG = new MeshBasicMaterial({
    color: 0xFF00FF,
    side: FrontSide,
    transparent: true,
    opacity: 0.5,
    depthTest: false,
})

// --- BUILDING ---
const MATERIAL_BUILDING = new MeshBasicMaterial({
    color: 0xFF0000,
    side: FrontSide,
    wireframe: true,
    depthTest: false,
    transparent: true,
})

// --- FLOOR ---
const MATERIAL_FLOOR = new MeshBasicMaterial({
    color: 0x00FFFF,
    side: FrontSide,
    wireframe: true,
    depthTest: false,
    transparent: true,
})

// --- ROOM ---
const MATERIAL_ROOM = new MeshBasicMaterial({
    color: 0x0000FF,
    side: FrontSide,
    wireframe: true,
    depthTest: false,
    transparent: true,
})

//--- ROOM TEXT BACKGROUND ---
const MATERIAL_ROOM_TEXT_BACKGROUND = new MeshBasicMaterial({
    color: 0x2A2E3F,
    side: FrontSide,
    transparent: true,
    depthTest: false,
})
// --- ROOM TEXT ---
const MATERIAL_ROOM_TEXT = new MeshBasicMaterial({
    color: 0xFEFEFE,
    side: FrontSide,
    transparent: true,
    depthTest: false,
})

// --- POINT OF INTEREST
const MATERIAL_POINT_OF_INTEREST = new MeshBasicMaterial({
    color: 0x00CCCC,
    transparent: true,
})

// --- DOOR ---
const MATERIAL_PARAM_DOOR: MeshBasicMaterialParameters = {
    color: 0x009F64,
    side: FrontSide,
    depthTest: false,
    transparent: true,
}
const MATERIAL_DOOR_DEFAULT = new MeshBasicMaterial({
    ...MATERIAL_PARAM_DOOR,
})

// --- WINDOW ---
const MATERIAL_WINDOW = new MeshBasicMaterial({
    color: 0x76D8FF,
    depthTest: false,
    transparent: true,
})

// --- OPENING ---
const MATERIAL_OPENING = new MeshBasicMaterial({
    color: 0xE8E8E8,
    depthTest: false,
    transparent: true,
})

// --- WALL ---
const MATERIAL_PARAM_WALL: MeshBasicMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    transparent: true,
}
const MATERIAL_WALL_INTERIOR = new MeshBasicMaterial({
    ...MATERIAL_PARAM_WALL,
    color: 0x848BA5,
})
const MATERIAL_WALL_EXTERIOR = new MeshBasicMaterial({
    ...MATERIAL_PARAM_WALL,
    color: 0x000000,
})
const MATERIAL_WALL_INTERMEDIATE = new MeshBasicMaterial({
    ...MATERIAL_PARAM_WALL,
    color: 0x9F9F9F,
})

//--- METRICS ---
const MATERIAL_PARAM_METRICS_LINE: LineBasicMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    linewidth: 0.1,
    transparent: true,
    opacity: 1,
}
const MATERIAL_METRICS_LINE = new LineBasicMaterial({
    ...MATERIAL_PARAM_METRICS_LINE,
    color: 0xFF0000,
})
const MATERIAL_PARAM_METRICS_TEXT_BACKGROUND: MeshBasicMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    transparent: true,
}
const MATERIAL_METRICS_TEXT_BACKGROUND = new MeshBasicMaterial({
    ...MATERIAL_PARAM_METRICS_TEXT_BACKGROUND,
    color: 0xFF0000,
})
const MATERIAL_PARAM_METRICS_TEXT: MeshBasicMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    transparent: true,
}
const MATERIAL_METRICS_TEXT = new MeshBasicMaterial({
    ...MATERIAL_PARAM_METRICS_TEXT,
    color: 0xFFFFFF,
})
const MATERIAL_PARAM_METRICS_TEXT_DISPLAY_ID_BACKGROUND: MeshBasicMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    transparent: true,
}
const MATERIAL_METRICS_TEXT_DISPLAY_ID_BACKGROUND = new MeshBasicMaterial({
    ...MATERIAL_PARAM_METRICS_TEXT_DISPLAY_ID_BACKGROUND,
    color: 0x005D3A,
})
const MATERIAL_PARAM_METRICS_TEXT_DISPLAY_ID: MeshBasicMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    transparent: true,
}
const MATERIAL_METRICS_TEXT_DISPLAY_ID = new MeshBasicMaterial({
    ...MATERIAL_PARAM_METRICS_TEXT_DISPLAY_ID,
    color: 0xFFFFFF,
})
const MATERIAL_METRICS_TEXT_THICKNESS_BACKGROUND = new MeshBasicMaterial({
    color: 0xFFFFFF,
    side: FrontSide,
    depthTest: false,
    transparent: true,
})
const MATERIAL_METRICS_TEXT_THICKNESS = new MeshBasicMaterial({
    color: 0x000000,
    side: FrontSide,
    depthTest: false,
    transparent: true,
})

// --- FURNITURE ---
const MATERIAL_FURNITURE = new MeshBasicMaterial({
    color: 0x0095D0,
    side: FrontSide,
    depthTest: false,
    transparent: true,
})

// --- ROOM SLAB FLOOR ---
const MATERIAL_ROOM_FLOOR_SLAB_FLOOR: MeshBasicMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    transparent: true,
}
const MATERIAL_ROOM_SLAB_FLOOR_DEFAULT = new MeshBasicMaterial({
    ...MATERIAL_ROOM_FLOOR_SLAB_FLOOR,
    color: 0xE8E8E8,
})

const MATERIAL_PARAM_FLOOR_SLAB_FLOOR: MeshBasicMaterialParameters = {
    side: FrontSide,
    depthTest: false,
    transparent: true,
}
const MATERIAL_FLOOR_SLAB_FLOOR_DEFAULT = new MeshBasicMaterial({
    ...MATERIAL_PARAM_FLOOR_SLAB_FLOOR,
    color: 0xC4C9D6,
})

// #######################
// ### RENDERER CONFIG ###
// #######################
export class BuildingRenderer2DSvg extends BuildingRenderer {
    showAmbientLight = false
    showRectAreaWorldLights = false
    showSky = false
    debugScene = false
    showStats = false
    debugHoles = false
    debugSnapping = false
    debugUnrecognizedRooms = false

    /**
     * @param listingId
     * @param building Muss mutable und voll reaktiv sein.
     */
    constructor(
        listingId: string,
        building: Readonly<Ref<Building>>,
    ) {
        super(
            "2D_SVG",
            listingId,
            building,
            false,
            "2D",
            LinearToneMapping,
            [],
            0.11, //in meters (11cm), sollte kleiner als der Default-Wert von Apple sein (11,81cm)
            null,
            "SVG"
        );

        this.wallCreator?.destroy()
        this.pointOfInterestCreator?.destroy()
        this.roofAreaCreator?.destroy()
        this.wallOpeningCreator?.destroy()
        this.occlusionCullingRaycaster.destroy()
        this.hoverRaycaster.destroy()
        this.draggableRaycaster?.destroy()
        this.trackingRaycaster?.destroy()
        this.selectionRaycaster.destroy()
        this.historyManager.destroy()
    }

    destroy() {
        super.destroy();

        for (const effect of this.effects) {
            effect.destroy();
        }
    }

    calculateIsCameraFitIgnored(component: BuildingComponent): boolean {
        return component.type !== "BUILDING"
    }

    isSelectable(component: BuildingComponent & TSelectionRelated<TSelectionRaycasterEmitterOrConsumer<RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint>>): boolean {
        return false
    }

    isOcclusionCullable(component: BuildingComponent & TOcclusionCullingRelated<TOcclusionCullingRaycasterEmitterOrConsumer<RaycastableBuildingComponent<TraversalBuildingComponent>>>): boolean {
        return false;
    }

    isHoverable(component: BuildingComponent & THoverRelated<THoverRaycasterEmitterOrConsumer<RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint>>): boolean {
        return false
    }

    calculateDebug(component: BuildingComponent): boolean {
        return false
    }

    calculateHasBillboardEffect(component: BuildingComponent): boolean {
        switch (component.type) {
            case "ROOM_TEXT":
            case "ROOM_TEXT_BACKGROUND":
            case "WALL_METRICS_TEXT_WIDTH":
            case "WALL_METRICS_TEXT_WIDTH_BACKGROUND":
            case "WALL_OPENING_METRICS_TEXT":
            case "WALL_OPENING_METRICS_TEXT_BACKGROUND":
            case "WALL_METRICS_TEXT_DISPLAY_ID":
            case "WALL_METRICS_TEXT_DISPLAY_ID_BACKGROUND":
            case "WALL_METRICS_TEXT_THICKNESS":
            case "WALL_METRICS_TEXT_THICKNESS_BACKGROUND":
            case "POINT_OF_INTEREST":
                return true
            default:
                return false
        }
    }

    // noinspection OverlyComplexFunctionJS,FunctionTooLongJS
    calculateVisibility(component: BuildingComponent): RenderVisibility {
        //BUILDING
        if (component.type === "BUILDING") {
            return "SELF_HIDDEN_CHILDREN_VISIBLE"
        }

        //FLOORS
        if (component.type === "FLOOR") {
            return this.visibleFloorLevels.value.has(component.component.value.level) ? "SELF_HIDDEN_CHILDREN_VISIBLE" : "HIDDEN"
        }
        if (component.type === "FLOOR_SLAB_FLOOR") {
            return "VISIBLE"
        }

        //ROOMS
        if (component.type === "ROOM") {
            return "SELF_HIDDEN_CHILDREN_VISIBLE"
        }
        if (component.type === "ROOM_SLAB_FLOOR") {
            return "VISIBLE"
        }

        //FURNITURE
        if (component.type === "FURNITURE") {
            return this.showFurniture.value ? "VISIBLE" : "HIDDEN"
        }

        //WALL
        if (component.type === "WALL_WITH_HOLES") {
            if (this.isEvebiModeEnabled.value) {
                return "HIDDEN"
            }
            return "VISIBLE"
        }

        //ROOM TEXT
        if (component.type === "ROOM_TEXT") {
            return this.showRoomTexts.value ? "VISIBLE" : "HIDDEN"
        }
        if (component.type === "ROOM_TEXT_BACKGROUND") {
            return this.visibility(component.textComponent) //recursion
        }

        //WALL METRICS
        if (component.type === "WALL_METRICS_TEXT_WIDTH" || component.type === "WALL_METRICS_TEXT_DISPLAY_ID" || component.type === "WALL_METRICS_TEXT_THICKNESS") {
            const text = component.customShapeRepresentation.text.value
            if (text === null || text.trim() === "") {
                return "HIDDEN"
            }
            if (component.type === "WALL_METRICS_TEXT_DISPLAY_ID") {
                if (this.showDisplayIds.value) {
                    return "VISIBLE"
                }
                if (this.isEvebiModeEnabled.value && this.displayIdSizeAdjustmentXEffectedWallIds.value.has(component.wallWithHolesComponent.component.value.id)) {
                    return "VISIBLE"
                }
            }
            if (component.type === "WALL_METRICS_TEXT_THICKNESS") {
                if (this.showWallThicknesses.value) {
                    return "VISIBLE"
                }
                if (this.isEvebiModeEnabled.value && this.displayIdSizeAdjustmentXEffectedWallIds.value.has(component.wallWithHolesComponent.component.value.id)) {
                    return "VISIBLE"
                }
            }
            if (component.type === "WALL_METRICS_TEXT_WIDTH" && this.showWallWidths.value) {
                return "VISIBLE"
            }
            return "HIDDEN"
        }
        if (component.type === "WALL_METRICS_LINE" || component.type === "WALL_METRICS_TEXT_WIDTH_BACKGROUND" || component.type === "WALL_METRICS_TEXT_DISPLAY_ID_BACKGROUND" || component.type === "WALL_METRICS_TEXT_THICKNESS_BACKGROUND") {
            return this.visibility(component.textComponent) //recursion
        }

        if (component.type === "WALL_OPENING_WINDOW") {
            return "VISIBLE"
        }

        if (component.type === "WALL_OPENING_DOOR") {
            return "VISIBLE"
        }

        if (component.type === "WALL_OPENING_OPENING") {
            return "VISIBLE"
        }

        if (component.type === "WALL_OPENING_DOOR_DOORWAY_ARC" || component.type === "WALL_OPENING_DOOR_DOORWAY_LINE") {
            return "VISIBLE"
        }

        if (component.type === "WALL_SIZE_ADJUSTMENT" || component.type === "WALL_SIZE_ADJUSTMENT_OVERLAY") {
            return "VISIBLE"
        }

        return "ALWAYS_HIDDEN"
    }

    // noinspection OverlyComplexFunctionJS,FunctionTooLongJS
    calculateMaterialOf(component: TMaybeRaycasterRelated<TraversalBuildingComponent, BuildingComponent>): TMaterialDeclaration<TMaterial> {
        switch (component.type) {
            case "BUILDING":
                return MATERIAL_BUILDING
            case "FLOOR":
                return MATERIAL_FLOOR
            case "FURNITURE":
                return MATERIAL_FURNITURE
            case "ROOM":
                return MATERIAL_ROOM
            case "ROOM_TEXT_BACKGROUND":
                return MATERIAL_ROOM_TEXT_BACKGROUND
            case "ROOM_TEXT":
                return MATERIAL_ROOM_TEXT
            case "ROOM_SLAB_FLOOR":
                return MATERIAL_ROOM_SLAB_FLOOR_DEFAULT
            case "WALL_OPENING_DOOR":
                return MATERIAL_DOOR_DEFAULT
            case "WALL_OPENING_DOOR_DOORWAY_ARC":
            case "WALL_OPENING_DOOR_DOORWAY_LINE":
                return MATERIAL_DOORWAY_LINE
            case "WALL_OPENING_WINDOW":
                return MATERIAL_WINDOW
            case "WALL_WITH_HOLES":
                return this.wallMaterial(component)
            case "WALL_OPENING_OPENING":
                return MATERIAL_OPENING
            case "WALL_SIZE_ADJUSTMENT":
                return this.wallMaterial(component)
            case "WALL_METRICS_LINE":
                return MATERIAL_METRICS_LINE
            case "WALL_OPENING_METRICS_LINE":
                return MATERIAL_METRICS_LINE
            case "WALL_METRICS_TEXT_WIDTH_BACKGROUND":
                return MATERIAL_METRICS_TEXT_BACKGROUND
            case "WALL_OPENING_METRICS_TEXT_BACKGROUND":
                return MATERIAL_METRICS_TEXT_BACKGROUND
            case "WALL_METRICS_TEXT_WIDTH":
                return MATERIAL_METRICS_TEXT
            case "WALL_OPENING_METRICS_TEXT":
                return MATERIAL_METRICS_TEXT
            case "WALL_METRICS_TEXT_DISPLAY_ID":
                return MATERIAL_METRICS_TEXT_DISPLAY_ID
            case "WALL_METRICS_TEXT_DISPLAY_ID_BACKGROUND":
                return MATERIAL_METRICS_TEXT_DISPLAY_ID_BACKGROUND
            case "WALL_METRICS_TEXT_THICKNESS":
                return MATERIAL_METRICS_TEXT_THICKNESS
            case "WALL_METRICS_TEXT_THICKNESS_BACKGROUND":
                return MATERIAL_METRICS_TEXT_THICKNESS_BACKGROUND
            case "POINT_OF_INTEREST":
                return MATERIAL_POINT_OF_INTEREST
            case "WALL_SIZE_ADJUSTMENT_OVERLAY":
                return this.wallMaterial(component.wallComponent)
            case "FLOOR_SLAB_FLOOR":
                return MATERIAL_FLOOR_SLAB_FLOOR_DEFAULT
            default:
                return MATERIAL_DEBUG
        }
    }

    protected calculateZFightingOffsetY(component: BuildingComponent): Optional<number> {
        return null
    }

    // noinspection OverlyComplexFunctionJS
    protected calculateZFightingOffsetZ(component: BuildingComponent): Optional<number> {
        switch (component.type) {
            case "WALL_WITH_HOLES":
                return noThicknessWallOffsetZ(this, component.component.value)
            case "WALL_OPENING_DOOR":
            case "WALL_OPENING_WINDOW":
            case "WALL_OPENING_OPENING":
                return noThicknessOpeningOffsetZ(this, component.component.value)
            default:
                return null
        }
    }

    protected calculateZFightingScale(component: BuildingComponent): Optional<Vector3> {
        switch (component.type) {
            case "WALL_WITH_HOLES":
                return new Vector3(1, 0.1, 1)
            default:
                return null
        }
    }

    protected initializeRaycasters(edit: boolean): TRaycaster[] {
        return []
    }

    protected calculateCanBeDeleted(component: RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint): boolean {
        return false
    }

    protected isDraggable(component: BuildingComponent & TDraggableRelated<TDraggableRaycasterEmitterOrConsumer<RaycastableBuildingComponent<TraversalBuildingComponent>>>): boolean {
        return false
    }

    protected calculateShowSelectionDetails(selection: readonly (RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint)[]): boolean {
        return false
    }

    // noinspection OverlyComplexFunctionJS,FunctionTooLongJS
    protected calculateRenderOrder(component: BuildingComponent): Optional<number> {
        if (component.type === "WALL_OPENING_METRICS_TEXT") {
            return 1011
        }
        if (component.type === "WALL_OPENING_METRICS_TEXT_BACKGROUND") {
            return 1010
        }
        if (component.type === "WALL_OPENING_METRICS_LINE") {
            return 1009
        }
        if (component.type === "WALL_METRICS_TEXT_DISPLAY_ID") {
            return 1008
        }
        if (component.type === "WALL_METRICS_TEXT_DISPLAY_ID_BACKGROUND") {
            return 1007
        }
        if (component.type === "WALL_METRICS_TEXT_THICKNESS") {
            return 1006
        }
        if (component.type === "WALL_METRICS_TEXT_THICKNESS_BACKGROUND") {
            return 1005
        }
        if (component.type === "WALL_METRICS_TEXT_WIDTH") {
            return 1004
        }
        if (component.type === "WALL_METRICS_TEXT_WIDTH_BACKGROUND") {
            return 1003
        }
        if (component.type === "WALL_METRICS_LINE") {
            return 1002
        }

        switch (component.type) {
            case "ROOM_TEXT":
                return 17

            case "ROOM_TEXT_BACKGROUND":
                return 16

            case "POINT_OF_INTEREST":
                return 14

            case "WALL_SIZE_ADJUSTMENT_OVERLAY":
                return 13

            case "WALL_SIZE_ADJUSTMENT":
                return 12

            case "WALL_OPENING_DOOR":
                return 10

            case "WALL_OPENING_WINDOW":
                return 9

            case "WALL_OPENING_OPENING":
                return 8

            case "WALL_WITH_HOLES":
            case "WALL_WITHOUT_HOLES":
                return 7

            case "WALL_OPENING_DOOR_DOORWAY_ARC":
            case "WALL_OPENING_DOOR_DOORWAY_LINE":
                return 6

            case "FURNITURE":
                return 5

            case "ROOM":
                return 4

            case "ROOM_SLAB_FLOOR":
                return 2

            case "FLOOR_SLAB_FLOOR":
                return 1

            default:
                return 9999
        }
    }

    // noinspection OverlyComplexFunctionJS
    private wallMaterial(component: WallWithHolesBuildingComponent | WallWithoutHolesBuildingComponent | WallSizeAdjustmentBuildingComponent): TMaterialDeclaration<TMaterial> {
        const wallType = wallTypeOfWall(component.component.value)
        switch (wallType) {
            case "INTERIOR":
                return MATERIAL_WALL_INTERIOR
            case "EXTERIOR":
                return MATERIAL_WALL_EXTERIOR
            case "INTERMEDIATE":
                return MATERIAL_WALL_INTERMEDIATE
            default:
                console.warn("Unknown wall type", wallType)
                return MATERIAL_WALL_INTERIOR
        }
    }
}